# Lambda Functions Documentation

This document provides a high-level overview of all Lambda functions in the JiniX application.

## 1. Request Orchestrator Lambda

**Location:** `amplify/request-orchestrator/handler.ts`

**Purpose:** Central GraphQL resolver that handles all chat-related operations and AI interactions.

**Key Operations:**

- `getChatHistory` - Retrieves message history for a chat
- `sendMessage` - Sends user messages and generates AI responses
- `streamMessage` - Handles real-time streaming of AI responses
- `createChatWithQuestionnaire` - Creates AI chats with health questionnaires
- `createHealthChat` - Creates dedicated health assessment chats
- `resetChat` - Resets questionnaire progress
- `createGroupChat` - Creates multi-user chat sessions
- `createDoctorChat` - Creates chats with healthcare providers

**Authentication:** Uses Cognito user pools, extracts user ID from JWT token

---

## 2. Post-Confirmation Lambda

**Location:** `amplify/functions/post-confirmation/handler.ts`

**Purpose:** Triggered after user registration to set up initial user data.

**Operations:**

- Creates user profile in database
- Auto-generates AI chat sessions (ALS specialist, General health assistant)
- Sends welcome messages to new chats

**Trigger:** Cognito post-confirmation event

---

## 3. OneUp Health Connect Lambda

**Location:** `amplify/functions/oneup-health-connect/handler.ts`

**Purpose:** Manages integration with OneUp Health API for medical data access.

**Key Operations:**

- `createUser` - Creates user account in OneUp Health
- `getAuthUrl` - Generates OAuth URLs for health system connections
- `handleCallback` - Processes OAuth callbacks from health systems
- `getUserConnections` - Lists user's connected health systems
- `fetchData` - Retrieves medical data from connected systems
- `refreshAccessToken` - Refreshes expired access tokens

**External API:** OneUp Health FHIR API

---

## 4. Sync Medical Data Lambda

**Location:** `amplify/functions/sync-medical-data/handler.ts`

**Purpose:** Synchronizes medical records from OneUp Health into the application database.

**Operations:**

- Fetches medical records from multiple FHIR endpoints
- Maps FHIR resources to application data model
- Stores medical records with proper categorization
- Handles data deduplication and updates

**FHIR Resources:** Patient, Condition, Observation, Medication, Procedure, Allergy, Encounter

---

## 5. Sync Health Systems Lambda

**Location:** `amplify/functions/sync-health-systems/handler.ts`

**Purpose:** Synchronizes available health systems from OneUp Health into the application.

**Operations:**

- Fetches list of supported health systems
- Processes and validates health system data
- Batch updates health system records in database
- Handles location and address data normalization

**Data Processing:** Batch processing with chunking for performance

---

## 6. Search Medical Systems Lambda

**Location:** `amplify/functions/search-medical-systems/handler.ts`

**Purpose:** Provides search functionality for medical systems and healthcare providers.

**Operations:**

- Searches OneUp Health system directory
- Filters by location, system type, and query terms
- Returns formatted results with provider details
- Manages user access tokens for API calls

**Search Parameters:** Query string, offset, system type

---

## Common Patterns

### Authentication

- All functions use Cognito user pools for authentication
- User ID extracted from JWT token (`event.identity.sub`)
- OneUp Health integration uses app-specific user IDs (`jinix_{userId}`)

### Error Handling

- Consistent error response format across all functions
- Proper logging for debugging and monitoring
- Graceful degradation for external API failures

### Database Integration

- Uses AWS Amplify Data client for database operations
- Consistent schema models across all functions
- Proper relationship management between entities

### External APIs

- OneUp Health FHIR API for medical data
- OpenAI API for AI chat responses
- Proper token management and refresh logic
