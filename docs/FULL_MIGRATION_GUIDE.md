# Chat Application Migration Guide

This guide provides detailed steps for migrating the entire chat application to the new architecture.

## Migration Strategy

1. **Incremental Approach**: Migrate components one by one
2. **Backward Compatibility**: Keep old context working alongside new one
3. **Test as You Go**: Test each component after migration

## Prerequisites

- You've already set up the new architecture files:
  - `ViewStateContext.tsx`
  - `LayoutAnimations.tsx`
  - Layout components in `/components/Chat/layout/`
  - `ChatContextAdapter.tsx`

## Step 1: Update the Main Layout

1. Update `ChatsLayout.tsx` to use the new layout components
2. Wrap with `ChatContextAdapter` for backward compatibility

```tsx
export default function ChatsLayout({ children }) {
  return (
    <AllChatsProvider>
      <ViewStateProvider>
        <ChatContextAdapter>
          <ChatsLayoutContent>{children}</ChatsLayoutContent>
        </ChatContextAdapter>
      </ViewStateProvider>
    </AllChatsProvider>
  );
}
```

## Step 2: Migrate Action Menu Components

Update menu components to use the new ViewState:

```tsx
// Old approach
const { setShowNewChatPage } = useContext(ChatContext);
setShowNewChatPage(true);

// New approach
const { setActiveView } = useViewState();
setActiveView("NEW_CHAT");

// Backward compatibility approach
const { setActiveView } = useViewState();
const { setShowNewChatPage } = useContext(ChatContext);
setActiveView("NEW_CHAT");
setShowNewChatPage(true); // Keep old approach working
```

## Step 3: Migrate Header Components

Create new versions of headers that use ViewState:

```tsx
// Old approach
const { currentChat } = useCurrentChat();
const { showChatDetails, setShowChatDetails } = useContext(ChatContext);

// New approach
const { currentChat, activeView, setActiveView } = useViewState();
```

## Step 4: Migrate Container Components

Update container components to use ViewState:

1. **ChatDetailsContainer**
2. **NewChatContainer**
3. **ChatDetailsMobileContainer**

## Step 5: Update Detail Views

Update how detail views are displayed:

```tsx
// Old approach
{
  showChatDetails && <ChatDetailsView />;
}

// New approach
{
  isViewActive("CHAT_DETAILS") && <ChatDetailsView />;
}
```

## Step 6: Update Navigation Logic

Replace direct state setting with ViewState:

```tsx
// Old approach
const handleBackClick = () => {
  if (selectedUserDetail) {
    setSelectedUserDetail(null);
  } else {
    setShowChatDetails(false);
  }
};

// New approach
const handleBackClick = () => {
  goBack(); // Uses the built-in history management
};
```

## Step 7: Test Mobile Functionality

1. Test responsive layout with mobile view
2. Ensure headers appear correctly
3. Verify animations work properly
4. Confirm navigation flows correctly

## Step 8: Add the Ability to Create New Views

For each new view:

1. Add a new view type to `ChatViewState`
2. Create a component for the view
3. Update the layout to render the component
4. Add navigation to the view

Example for adding a Settings view:

```tsx
// 1. Add to ChatViewState
export type ChatViewState =
  // ... existing states
  "CHAT_SETTINGS";

// 2. Create component
export const ChatSettingsPanel = () => {
  // Component implementation
};

// 3. Update layout
const renderDetailsPanel = () => {
  if (activeView === "CHAT_SETTINGS") {
    return <ChatSettingsPanel />;
  }
  // ... other views
};

// 4. Add navigation
<Button onClick={() => setActiveView("CHAT_SETTINGS")}>Settings</Button>;
```

## Step 9: Clean Up Legacy Code

After migrating all components, gradually remove:

1. Direct uses of the old boolean states
2. Unused imports of the old context
3. Redundant state management

## Step 10: Documentation & Knowledge Sharing

1. Keep the migration guide updated
2. Document common patterns for the team
3. Share the new architecture approach with all developers

## Timeline Recommendation

- Week 1: Set up architecture and migrate main layout
- Week 2: Migrate core components (headers, action menus)
- Week 3: Migrate container components and detail views
- Week 4: Testing, bug fixing, and cleaning up legacy code
