import { type ClientSchema, a, defineData } from "@aws-amplify/backend";
import { requestOrchestrator } from "../request-orchestrator/resource";
import { oneupHealthConnect } from "../functions/oneup-health-connect/resource";
import { syncMedicalData } from "../functions/sync-medical-data/resource";
import { searchMedicalSystems } from "../functions/search-medical-systems/resource";
import { syncHealthSystems } from "../functions/sync-health-systems/resource";
import { postConfirmation } from "../functions/post-confirmation/resource";
const schema = a
  .schema({
    // ===== ENUMS & CUSTOM TYPES =====
    MessageType: a.enum(["TEXT", "IMAGE", "FILE", "SYSTEM", "AUDIO", "VIDEO"]),
    UserStatus: a.enum(["ONLINE", "OFFLINE", "AWAY", "DO_NOT_DISTURB"]),
    DeviceType: a.enum(["MOBILE", "DESKTOP", "TABLET"]),
    ChatType: a.enum(["GROUP", "DIRECT", "AI"]),
    UserType: a.enum(["HUMAN", "AI", "SYSTEM"]),

    FriendRequestStatus: a.enum(["PENDING", "ACCEPTED", "DECLINED"]),
    CommunityPermission: a.enum(["open", "request"]),
    CommunityRole: a.enum(["admin", "moderator", "member"]),

    ClientInfoType: a.customType({
      browser: a.string(),
      platform: a.string(),
      deviceType: a.ref("DeviceType"),
      ipAddress: a.ipAddress(),
      userAgent: a.string(),
    }),

    LocationType: a.customType({
      Lat: a.float(),
      Lng: a.float(),
    }),
    HealthSystemAddress: a.customType({
      line: a.string().array(),
      city: a.string(),
      state: a.string(),
    }),

    HealthSystemLocation: a.customType({
      name: a.string(),
      address: a.ref("HealthSystemAddress"),
    }),

    // ===== CORE USER MANAGEMENT =====
    User: a
      .model({
        userId: a.id().required(),
        name: a.string().required(),
        email: a.string().required(),
        profilePicture: a.url(),
        userType: a.ref("UserType"),
        is_1healthup_connected: a.boolean().default(false),
    
        messages: a.hasMany("ChatMessage", "userId"),
        chatParticipants: a.hasMany("ChatParticipant", "userId"),
        medicalProfile: a.hasOne("MedicalProfile", "userId"),

        posts: a.hasMany("Post", "userId"),
        comments: a.hasMany("Comment", "userId"),
        savedPosts: a.hasMany("SavedPost", "userId"),
        likes: a.hasMany("PostLike", "userId"),
        commentLikes: a.hasMany("CommentLike", "userId"),
        communityMemberships: a.hasMany("CommunityMember", "userId"),
        sentFriendRequests: a.hasMany("FriendRequest", "senderId"),
        receivedFriendRequests: a.hasMany("FriendRequest", "receiverId"),
        healthDataConnection: a.hasOne("HealthDataConnection", "userId"),
        connectedSystems: a.hasMany("UserConnectedSystem", "userId"),
      })
      .authorization((allow) => [
        allow.ownerDefinedIn("userId"), // Simplified owner authorization
        allow.authenticated().to(["read"]), // Allow authenticated users to read
      ])
      .identifier(["userId"]),

    // ===== CHAT SYSTEM =====

    Chat: a
      .model({
        name: a.string().required(),
        description: a.string(),
        chatType: a.ref("ChatType"),
        questionnaireId: a.string(),
        lastMessageAt: a.datetime(),
        createdAt: a.datetime(),
        updatedAt: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        metadata: a.customType({
          createdBy: a.string(),
          isArchived: a.boolean(),
          category: a.string(),
          configuration: a.json(),
          currentQuestionIndex: a.integer(),
          isQuestionnaireComplete: a.boolean(),
          questionnaireCompletedAt: a.datetime(),
          diagnosisMessageSent: a.boolean(),
          earlyCompletion: a.boolean(),
        }),

        messages: a.hasMany("ChatMessage", "chatId"),
        chatParticipants: a.hasMany("ChatParticipant", "chatId"),
      })
      .authorization((allow) => [
        allow.owner(), // Owner can perform all operations
        allow.authenticated().to(["read"]), // Authenticated users can read
      ]),

    ChatMessage: a
      .model({
        id: a.id().required(),
        chatId: a.string().required(),
        userId: a.string().required(),
        message: a.string().required(),
        messageType: a.ref("MessageType"),
        showMap: a.boolean(),
        attachments: a.string().array(),
        createdAt: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        chat: a.belongsTo("Chat", "chatId"),
        user: a.belongsTo("User", "userId"),
      })
      .authorization((allow) => [
        allow.owner(),
        allow.authenticated().to(["read"]),
      ])
      .secondaryIndexes((index) => [
        index("chatId")
          .sortKeys(["createdAt"])
          .queryField("listMessagesByDate"),
      ]),

    ChatParticipant: a
      .model({
        id: a.id().required(),
        chatId: a.string().required(),
        userId: a.string().required(),
        joinedAt: a.datetime(),
        lastMessageAt: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        chat: a.belongsTo("Chat", "chatId"),
        user: a.belongsTo("User", "userId"),
      })
      .authorization((allow) => [
        allow.owner(),
        allow.authenticated().to(["read"]),
      ])
      .identifier(["chatId", "userId"])
      .secondaryIndexes((index) => [
        index("userId").queryField("listChatParticipantsByUser"),
        index("chatId").queryField("listChatParticipantsByChat"),
      ]),

    // ===== MEDICAL DATA =====
    MedicalProfile: a
      .model({
        userId: a.id().required(),
        chatId: a.string(),
        profileData: a.json(),
        age: a.integer(),
        gender: a.string(),
        country: a.string(),
        height: a.float(),
        weight: a.float(),
        medicalHistory: a.string().array(),
        createdAt: a.datetime(),
        updatedAt: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        user: a.belongsTo("User", "userId"),
      })
      .authorization((allow) => [allow.owner()])
      .identifier(["userId"]),

    HealthSystem: a
      .model({
        id: a.id().required(),
        name: a.string().required(),
        logo: a.string(),
        ehr: a.string().required(),
        status: a.string().required(),
        locations: a.ref("HealthSystemLocation").array().required(),

        connectedUsers: a.hasMany("UserConnectedSystem", "systemId"),

        createdAt: a.datetime().required(),
        updatedAt: a.datetime().required(),
      })
      .identifier(["id"])
      .secondaryIndexes((index) => [
        index("name").queryField("listHealthSystemsByName"),
      ])
      .authorization((allow) => [allow.authenticated(), allow.publicApiKey()]),

    UsersMedicalRecords: a
      .model({
        userId: a.string().required(),
        recordId: a.string().required(),
        resourceType: a.string().required(),
        rawData: a.json().required(),
        lastUpdated: a.datetime().required(),
        category: a.string().required(),
        date: a.datetime(),
        provider: a.string().required(),
        medicalSystem: a.string().required(),
        externalId: a.string(),
        status: a.string(),
        createdAt: a.datetime(),
        updatedAt: a.datetime(),
      })
      .authorization((allow) => [
        allow.ownerDefinedIn("userId"),
        allow.group("HealthProvider"),
        allow.publicApiKey(),
      ])
      .identifier(["userId", "recordId"])
      .secondaryIndexes((index) => [
        index("userId")
          .sortKeys(["date"])
          .queryField("listUsersMedicalRecordsByDate"),
        index("userId")
          .sortKeys(["resourceType"])
          .queryField("listUsersMedicalRecordsByType"),
        index("userId")
          .sortKeys(["provider"])
          .queryField("listUsersMedicalRecordsByProvider"),
        index("userId")
          .sortKeys(["category"])
          .queryField("listUsersMedicalRecordsByCategory"),
      ]),

    PatientProfile: a
      .model({
        userId: a.string().required(),
        externalId: a.string().required(),
        gender: a.string(),
        birthDate: a.string(),
        name: a.string(),
        address: a.string(),
        phoneNumber: a.string(),
        email: a.string(),
        lastUpdated: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field
      })
      .authorization((allow) => [allow.owner()])
      .secondaryIndexes((index) => [
        index("userId").queryField("listPatientProfilesByUser"),
        index("externalId").queryField("getPatientProfileByExternalId"),
      ]),

    HealthObservation: a
      .model({
        userId: a.string().required(),
        patientId: a.string().required(),
        externalId: a.string().required(),
        category: a.string(),
        code: a.string(),
        value: a.float(),
        unit: a.string(),
        effectiveDateTime: a.string(),
        status: a.string(),
        lastUpdated: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field
      })
      .authorization((allow) => [allow.owner()])
      .secondaryIndexes((index) => [
        index("userId").queryField("listHealthObservationsByUser"),
        index("patientId").queryField("listHealthObservationsByPatient"),
      ]),

    MedicalCondition: a
      .model({
        userId: a.string().required(),
        patientId: a.string().required(),
        externalId: a.string().required(),
        code: a.string(),
        display: a.string(),
        clinicalStatus: a.string(),
        verificationStatus: a.string(),
        onsetDateTime: a.string(),
        lastUpdated: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field
      })
      .authorization((allow) => [allow.owner()])
      .secondaryIndexes((index) => [
        index("userId").queryField("listMedicalConditionsByUser"),
        index("patientId").queryField("listMedicalConditionsByPatient"),
      ]),

    Medication: a
      .model({
        userId: a.string().required(),
        patientId: a.string().required(),
        externalId: a.string().required(),
        name: a.string(),
        status: a.string(),
        isBrand: a.boolean(),
        form: a.string(),
        dosageInstruction: a.string(),
        lastUpdated: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field
      })
      .authorization((allow) => [allow.owner()])
      .secondaryIndexes((index) => [
        index("userId").queryField("listMedicationsByUser"),
        index("patientId").queryField("listMedicationsByPatient"),
      ]),

    ALSCenter: a
      .model({
        ClinicName: a.string().required(),
        DoctorName: a.string().required(),
        WebsiteURL: a.url(),
        ZipCode: a.string(),
        Location: a.customType({
          LatLng: a.ref("LocationType"),
        }),
        Specialties: a.string().array(),
        Distance: a.float(),
        PhoneNumber: a.string(),
      })
      .authorization((allow) => [allow.authenticated().to(["read"])])
      .identifier(["ClinicName", "DoctorName"])
      .secondaryIndexes((index) => [
        index("ZipCode").queryField("listCentersByZipCode"),
      ]),

    // ===== HEALTH DATA CONNECTIONS =====
    HealthDataConnection: a
      .model({
        appUserId: a.string().required(),
        userId: a.string().required(),
        user: a.belongsTo("User", "userId"),
        accessToken: a.string().required(),
        refreshToken: a.string().required(),
        code: a.string(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        createdAt: a.datetime().required(),
        updatedAt: a.datetime().required(),
      })
      .authorization((allow) => [allow.owner(), allow.publicApiKey()])
      .identifier(["appUserId"])
      .secondaryIndexes((index) => [
        index("userId").queryField("listHealthDataConnectionsByUser"),
      ]),

    UserConnectedSystem: a
      .model({
        userId: a.string().required(),
        systemId: a.string().required(),
        createdAt: a.datetime().required(),
        updatedAt: a.datetime().required(),
        user: a.belongsTo("User", "userId"),
        system: a.belongsTo("HealthSystem", "systemId"),
      })
      .authorization((allow) => [
        allow.publicApiKey(),
        allow.ownerDefinedIn("userId"),
      ])
      .identifier(["userId", "systemId"])
      .secondaryIndexes((index) => [
        index("userId").queryField("listUserConnectedSystemsByUser"),
      ]),

    // ===== QUESTIONNAIRE SYSTEM =====
    HealthQuestion: a
      .model({
        prompt: a.string().required(),
        category: a.string().required(),
        questionnaireQuestions: a.hasMany(
          "QuestionnaireQuestion",
          "healthQuestionId"
        ),
      })
      .authorization((allow) => [allow.publicApiKey()]),

    Questionnaire: a
      .model({
        name: a.string().required(),
        questionnaireQuestions: a.hasMany(
          "QuestionnaireQuestion",
          "questionnaireId"
        ),
      })
      .authorization((allow) => [allow.publicApiKey()]),

    QuestionnaireQuestion: a
      .model({
        questionnaireId: a.string().required(),
        healthQuestionId: a.string().required(),
        sequence: a.integer().required(),
        questionnaire: a.belongsTo("Questionnaire", "questionnaireId"),
        healthQuestion: a.belongsTo("HealthQuestion", "healthQuestionId"),
      })
      .authorization((allow) => [allow.publicApiKey()])
      .secondaryIndexes((index) => [
        index("questionnaireId")
          .sortKeys(["sequence"])
          .queryField("listQuestionnaireQuestionsBySequence"),
        index("healthQuestionId").queryField(
          "listQuestionnaireQuestionsByHealthQuestion"
        ),
      ]),

    // ===== COMMUNITY SYSTEM =====
    Community: a
      .model({
        id: a.id(),
        name: a.string().required(),
        description: a.string(),
        icon: a.string(),
        banner: a.string(),
        permission: a.ref("CommunityPermission"),
        rules: a.string().array(),
        membersCount: a.integer().default(1),
        createdBy: a.string().required(),
        createdAt: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        members: a.hasMany("CommunityMember", "communityId"),
        posts: a.hasMany("Post", "communityId"),
      })
      .authorization((allow) => [
        allow.authenticated(), // <-- keep only one
        allow.ownerDefinedIn("createdBy"),
        allow.publicApiKey(),
      ])
      .secondaryIndexes((index) => [
        index("createdBy")
          .sortKeys(["createdAt"])
          .queryField("listCommunitiesByUser"),
      ]),

    CommunityMember: a
      .model({
        id: a.id(),
        communityId: a.string().required(),
        userId: a.string().required(),
        role: a.ref("CommunityRole"),
        joinedAt: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        community: a.belongsTo("Community", "communityId"),
        user: a.belongsTo("User", "userId"),
      })
      .authorization((allow) => [
        allow.authenticated(), // <-- keep only one
        allow.owner(),
      ])
      .secondaryIndexes((index) => [
        index("userId").queryField("listCommunityMembershipsByUser"),
        index("communityId").queryField("listMembersByCommunity"),
      ]),

    Post: a
      .model({
        id: a.id(),
        userId: a.string().required(),
        userName: a.string(),
        title: a.string().required(),
        content: a.string().required(),
        images: a.string().array(),
        likes: a.integer().default(0),
        commentsCount: a.integer().default(0),
        createdAt: a.datetime(),
        communityId: a.string(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        user: a.belongsTo("User", "userId"),
        community: a.belongsTo("Community", "communityId"),
        comments: a.hasMany("Comment", "postId"),
        savedBy: a.hasMany("SavedPost", "postId"),
        likedBy: a.hasMany("PostLike", "postId"),
      })
      .authorization((allow) => [
        allow.authenticated(), // <-- ДОДАЙ ЦЕ для update/create/delete
        allow.owner(),
      ])
      .secondaryIndexes((index) => [
        index("userId").sortKeys(["createdAt"]).queryField("listPostsByUser"),
        index("communityId")
          .sortKeys(["createdAt"])
          .queryField("listPostsByCommunity"),
      ]),

    Comment: a
      .model({
        id: a.id(),
        userId: a.string().required(),
        postId: a.string().required(),
        content: a.string().required(),
        parentCommentId: a.string(),
        likes: a.integer().default(0),
        createdAt: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        user: a.belongsTo("User", "userId"),
        post: a.belongsTo("Post", "postId"),
        replies: a.hasMany("Comment", "parentCommentId"),
        parent: a.belongsTo("Comment", "parentCommentId"),
        likedBy: a.hasMany("CommentLike", "commentId"),
      })
      .authorization((allow) => [
        allow.owner(),
        allow.authenticated().to(["read"]),
      ])
      .secondaryIndexes((index) => [
        index("postId")
          .sortKeys(["createdAt"])
          .queryField("listCommentsByPost"),
        index("parentCommentId").queryField("listCommentReplies"),
      ]),

    PostLike: a
      .model({
        id: a.id(),
        userId: a.string().required(),
        postId: a.string().required(),
        createdAt: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        post: a.belongsTo("Post", "postId"),
        user: a.belongsTo("User", "userId"),
      })
      .authorization((allow) => [
        allow.owner(),
        allow.authenticated().to(["read"]),
      ])
      .secondaryIndexes((index) => [
        index("postId").queryField("listLikesByPost"),
        index("userId").sortKeys(["createdAt"]).queryField("listLikesByUser"),
      ]),

    CommentLike: a
      .model({
        id: a.id(),
        userId: a.string().required(),
        commentId: a.string().required(),
        createdAt: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        comment: a.belongsTo("Comment", "commentId"),
        user: a.belongsTo("User", "userId"),
      })
      .authorization((allow) => [
        allow.owner(),
        allow.authenticated().to(["read"]),
      ])
      .secondaryIndexes((index) => [
        index("commentId").queryField("listLikesByComment"),
        index("userId")
          .sortKeys(["createdAt"])
          .queryField("listCommentLikesByUser"),
      ]),

    SavedPost: a
      .model({
        id: a.id(),
        userId: a.string().required(),
        postId: a.string().required(),
        createdAt: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        post: a.belongsTo("Post", "postId"),
        user: a.belongsTo("User", "userId"),
      })
      .authorization((allow) => [allow.owner(), allow.publicApiKey()])
      .secondaryIndexes((index) => [
        index("userId")
          .sortKeys(["createdAt"])
          .queryField("listSavedPostsByUser"),
        index("postId").queryField("listSavedPostsByPost"),
      ]),

    // ===== FRIEND SYSTEM =====
    FriendRequest: a
      .model({
        id: a.id(),
        senderId: a.string().required(),
        receiverId: a.string().required(),
        status: a.ref("FriendRequestStatus"),
        createdAt: a.datetime(),
        updatedAt: a.datetime(),
        owner: a
          .string()
          .authorization((allow) => [allow.owner().to(["read"])]), // Added protected owner field

        sender: a.belongsTo("User", "senderId"),
        receiver: a.belongsTo("User", "receiverId"),
      })
      .authorization((allow) => [
        allow.ownerDefinedIn("senderId"), // Sender is an owner
        allow.ownerDefinedIn("receiverId"), // Receiver is also an owner
        allow.authenticated().to(["read"]),
      ])
      .secondaryIndexes((index) => [
        index("senderId")
          .sortKeys(["createdAt"])
          .queryField("listSentFriendRequests"),
        index("receiverId")
          .sortKeys(["createdAt"])
          .queryField("listReceivedFriendRequests"),
      ]),

    // ===== ONEUP HEALTH TYPES =====
    OneUpHealthUserType: a.customType({
      id: a.string(),
      app_user_id: a.string(),
      reference_id: a.string(),
      client_id: a.string(),
      access_token: a.string(),
      has_password: a.boolean(),
      has_oauth_scope_patient: a.boolean(),
      has_oauth_scope_system: a.boolean(),
      has_email: a.boolean(),
    }),

    OneUpHealthTokenType: a.customType({
      access_token: a.string().required(),
      refresh_token: a.string(),
      expires_in: a.integer(),
      refresh_expires_in: a.integer(),
      token_type: a.string(),
      scope: a.string(),
    }),

    OneUpHealthPatientType: a.customType({
      id: a.string(),
      name: a.string(),
      gender: a.string(),
      birthDate: a.string(),
      address: a.string(),
      phoneNumber: a.string(),
      email: a.string(),
    }),

    OneUpHealthConditionType: a.customType({
      id: a.string(),
      code: a.string(),
      display: a.string(),
      clinicalStatus: a.string(),
      verificationStatus: a.string(),
      onsetDateTime: a.string(),
    }),

    OneUpHealthObservationType: a.customType({
      id: a.string(),
      category: a.string(),
      code: a.string(),
      value: a.float(),
      unit: a.string(),
      effectiveDateTime: a.string(),
      status: a.string(),
    }),

    OneUpHealthMedicationType: a.customType({
      id: a.string(),
      name: a.string(),
      status: a.string(),
      isBrand: a.boolean(),
      form: a.string(),
      dosageInstruction: a.string(),
    }),

    OneUpHealthDataType: a.customType({
      patients: a.ref("OneUpHealthPatientType").array(),
      conditions: a.ref("OneUpHealthConditionType").array(),
      observations: a.ref("OneUpHealthObservationType").array(),
      medications: a.ref("OneUpHealthMedicationType").array(),
    }),

    OneUpHealthAuthUrlType: a.customType({
      success: a.boolean(),
      data: a.customType({
        authorization_url: a.string().required(),
      }),
      error: a.string(),
    }),

    OneUpHealthConnectionType: a.customType({
      id: a.string(),
      user_id: a.string(),
      status: a.string(),
      provider: a.string(),
      provider_id: a.string(),
      connections: a.string().array(),
    }),

    OneUpHealthSingleConnectionType: a.customType({
      id: a.string(),
      user_id: a.string(),
      status: a.string(),
      provider: a.string(),
    }),

    OneUpHealthCreateUserDataType: a.customType({
      appUserId: a.string().required(),
    }),

    OneUpHealthAuthCodeDataType: a.customType({
      appUserId: a.string().required(),
    }),

    OneUpHealthAuthUrlDataType: a.customType({
      accessToken: a.string().required(),
      systemId: a.string().required(),
      appUserId: a.string().required(),
    }),

    OneUpHealthCallbackDataType: a.customType({
      provider: a.string().required(),
      appUserId: a.string().required(),
    }),

    OneUpHealthConnectionsDataType: a.customType({
      appUserId: a.string().required(),
    }),

    OneUpHealthFetchDataType: a.customType({
      accessToken: a.string().required(),
      endpoint: a.string().required(),
      appUserId: a.string().required(),
      patientId: a.string(),
    }),

    MedicalSystemType: a.customType({
      id: a.integer(),
      name: a.string(),
      address: a.string(),
      fhirVersion: a.string(),
      ehr: a.string(),
      resourceUrl: a.string(),
      logo: a.string(),
    }),

    // ===== MUTATIONS & QUERIES =====
    createChatWithQuestionnaire: a
      .mutation()
      .arguments({
        questionnaireId: a.string().required(),
        name: a.string().required(),
        description: a.string(),
        metadata: a.customType({
          createdBy: a.string().required(),
          isArchived: a.boolean(),
          category: a.string(),
          configuration: a.json(),
        }),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(a.ref("Chat"))
      .handler(a.handler.function(requestOrchestrator)),

    createHealthChat: a
      .mutation()
      .arguments({})
      .authorization((allow) => [allow.authenticated()])
      .returns(a.ref("Chat"))
      .handler(a.handler.function(requestOrchestrator)),

    sendMessage: a
      .mutation()
      .arguments({
        chatId: a.string().required(),
        message: a.string().required(),
        messageType: a.ref("MessageType"),
        attachments: a.string().array(),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(a.ref("ChatMessage"))
      .handler(a.handler.function(requestOrchestrator)),

    streamMessage: a
      .mutation()
      .arguments({
        chatId: a.string().required(),
        message: a.string().required(),
        eventConfig: a.customType({
          channelName: a.string().required(),
          sessionId: a.string().required(),
          namespace: a.string(),
        }),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(a.ref("ChatMessage"))
      .handler(a.handler.function(requestOrchestrator)),

    resetChat: a
      .mutation()
      .arguments({
        chatId: a.string().required(),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(a.ref("Chat"))
      .handler(a.handler.function(requestOrchestrator)),

    // OneUp Health Mutations
    createOneUpHealthUser: a
      .mutation()
      .arguments({
        appUserId: a.string(),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(
        a.customType({
          success: a.boolean(),
          data: a.customType({
            id: a.string(),
            app_user_id: a.string(),
            access_token: a.string(),
          }),
          error: a.string(),
        })
      )
      .handler(a.handler.function(oneupHealthConnect)),

    getOneUpHealthAuthCode: a
      .mutation()
      .arguments({
        appUserId: a.string().required(),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(
        a.customType({
          success: a.boolean(),
          data: a.customType({
            code: a.string(),
          }),
          error: a.string(),
        })
      )
      .handler(a.handler.function(oneupHealthConnect)),

    getOneUpHealthAuthUrl: a
      .mutation()
      .arguments({
        systemId: a.string(),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(a.ref("OneUpHealthAuthUrlType"))
      .handler(a.handler.function(oneupHealthConnect)),

    handleOneUpHealthCallback: a
      .mutation()
      .arguments({
        provider: a.string(),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(
        a.customType({
          success: a.boolean(),
          error: a.string(),
        })
      )
      .handler(a.handler.function(oneupHealthConnect)),

    refreshOneUpHealthToken: a
      .mutation()
      .arguments({
        refreshToken: a.string().required(),
        provider: a.string(),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(
        a.customType({
          success: a.boolean(),
          data: a.customType({
            access_token: a.string(),
            refresh_token: a.string(),
            expires_in: a.integer(),
          }),
          error: a.string(),
        })
      )
      .handler(a.handler.function(oneupHealthConnect)),

    refreshOneUpHealthUserToken: a
      .mutation()
      .arguments({
        provider: a.string().required(),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(
        a.customType({
          success: a.boolean(),
          data: a.customType({
            access_token: a.string(),
            refresh_token: a.string(),
            expires_in: a.integer(),
          }),
          error: a.string(),
        })
      )
      .handler(a.handler.function(oneupHealthConnect)),

    getUserOneUpHealthConnections: a
      .query()
      .arguments({
        appUserId: a.string(),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(
        a.customType({
          success: a.boolean(),
          data: a.customType({
            connections: a.json(),
          }),
          error: a.string(),
        })
      )
      .handler(a.handler.function(oneupHealthConnect)),

    fetchOneUpHealthFhirData: a
      .query()
      .arguments({
        accessToken: a.string(),
        endpoint: a.string().required(),
        patientId: a.string(),
        provider: a.string(),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(
        a.customType({
          success: a.boolean(),
          data: a.customType({
            entry: a.json(),
          }),
          error: a.string(),
        })
      )
      .handler(a.handler.function(oneupHealthConnect)),

    syncMedicalData: a
      .mutation()
      .arguments({})
      .authorization((allow) => [allow.authenticated()])
      .returns(
        a.customType({
          success: a.boolean(),
          data: a.customType({
            syncedRecords: a.integer(),
            message: a.string(),
          }),
          error: a.string(),
        })
      )
      .handler(a.handler.function(syncMedicalData)),

    searchMedicalSystems: a
      .mutation()
      .arguments({
        query: a.string().required(),
        offset: a.integer(),
        system_type: a.string(),
      })
      .authorization((allow) => [allow.authenticated()])
      .returns(a.ref("MedicalSystemType").array())
      .handler(a.handler.function(searchMedicalSystems)),

    syncHealthSystems: a
      .mutation()
      .authorization((allow) => [allow.authenticated()])
      .returns(
        a.customType({
          success: a.boolean(),
          message: a.string(),
          errors: a.string().array(),
        })
      )
      .handler(a.handler.function(syncHealthSystems)),
    BatchCreateHealthSystem: a
      .mutation()
      .authorization((allow) => [allow.authenticated()])
      .arguments({
        contents: a.string().array(),
      })
      .returns(a.boolean())
      .handler(
        a.handler.custom({
          dataSource: a.ref("HealthSystem"),
          entry: "./syncHealthSystemsHandler.js",
        })
      ),
  })

  .authorization((allow) => [
    allow.authenticated().to(["read"]),
    allow.resource(syncMedicalData),
    allow.resource(searchMedicalSystems),
    allow.resource(oneupHealthConnect),
    allow.resource(requestOrchestrator),
    allow.resource(syncHealthSystems),
    allow.resource(postConfirmation),
  ]);

export type Schema = ClientSchema<typeof schema>;

export const data = defineData({
  schema,
  authorizationModes: {
    defaultAuthorizationMode: "userPool",
    apiKeyAuthorizationMode: { expiresInDays: 30 },
  },
});
