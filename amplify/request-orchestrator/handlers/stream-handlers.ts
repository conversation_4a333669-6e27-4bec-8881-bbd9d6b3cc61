import { handleSendMessage } from "./chat-handlers";
import { EventManager } from "../services/EventManager";
import { StreamService } from "../services/StreamService";

import { IdentityInfo, LambdaContext, StreamMessageArgs } from "../types";
import { createRemainingTimeGetter } from "../utils/chat-utils";
import { generateClient } from "aws-amplify/api";
import { Schema } from "../../data/resource";

const client = generateClient<Schema>();

export async function handleStreamMessage(
  args: StreamMessageArgs,
  identity: IdentityInfo,
  context?: LambdaContext
) {
  const { chatId, message, eventConfig } = args;

  if (!message || message.length > 2000) {
    throw new Error(
      "Invalid message: Must be non-empty and less than 2000 characters"
    );
  }

  // Use secure userId from identity
  const userId = identity.sub;
  if (!userId) {
    throw new Error("User authentication required");
  }

  console.log("[handleStreamMessage] Processing message:", { chatId, userId });

  if (eventConfig?.channelName && eventConfig?.sessionId) {
    const eventManager = new EventManager(
      eventConfig.channelName,
      eventConfig.sessionId,
      chatId
    );

    try {
      await eventManager.sendToken("");

      const getRemainingTime = createRemainingTimeGetter(context);

      const streamService = new StreamService(
        eventManager,
        chatId,
        getRemainingTime
      );

      await client.models.ChatMessage.create({
        chatId,
        userId,
        message,
      });

      let chatMessage = await streamService.streamResponseAndKeepAlive(message);

      // Handle case where chatMessage might be in a wrapper object
      if (chatMessage && chatMessage.chatMessage) {
        chatMessage = chatMessage.chatMessage;
      }

      console.log("[handleStreamMessage] Streaming completed successfully");
      return chatMessage;
    } catch (error) {
      console.error(
        "[handleStreamMessage] Failed to complete streaming:",
        error
      );
      await eventManager.sendError(
        "Sorry, something went wrong during processing. Please try again."
      );
    }
  } else {
    console.log(
      "[handleStreamMessage] No event config, using non-streaming fallback"
    );
    // Pass the secure args with userId from identity
    return await handleSendMessage({ ...args, userId }, identity);
  }
}
