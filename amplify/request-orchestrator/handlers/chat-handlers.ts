import { generateClient } from "aws-amplify/api";
import { AiService } from "../services/AiService";
import type { Schema } from "../../data/resource";
import { formatChatHistory } from "../utils/chat-utils";
import { createStandardChat } from "../utils/chat-creation-utils";
import {
  AI_ASSISTANT_ID,
  MESSAGE_TYPE_TEXT,
  MESSAGE_TYPE_SYSTEM,
  ANONYMOUS_USER_ID,
} from "../../../constants/chat";
import {
  GetChatHistoryArgs,
  SendMessageArgs,
  IdentityInfo,
  CreateChatWithQuestionnaireArgs,
} from "../types";

import questionnaireMessagesMock from "../mocks/questionnaire-messages.json";

const client = generateClient<Schema>();

const aiService = new AiService(process.env.OPENAI_API_KEY || "");

export async function handleGetChatHistory(
  args: GetChatHistoryArgs,
  identity: IdentityInfo
) {
  console.log(
    "handleGetChatHistory called with args:",
    args,
    "and identity:",
    identity
  );

  const { chatId } = args;
  const { data: messages } = await client.models.ChatMessage.list({
    filter: {
      chatId: {
        eq: chatId,
      },
    },
  });

  return messages;
}

export async function handleSendMessage(
  args: SendMessageArgs,
  identity: IdentityInfo
) {
  console.log(
    "handleSendMessage called with args:",
    JSON.stringify(args),
    "and identity:",
    JSON.stringify(identity)
  );

  const {
    chatId,
    message,
    messageType = MESSAGE_TYPE_TEXT,
    attachments = [],
  } = args;

  if (!chatId) {
    throw new Error("chatId is required");
  }

  if (!message || message.length > 2000) {
    throw new Error("Invalid message");
  }

  // Use secure userId from identity
  const userId = identity.sub;
  if (!userId) {
    throw new Error("User authentication required");
  }

  try {
    const { data: chatExists } = await client.models.Chat.get({ id: chatId });
    if (!chatExists) {
      throw new Error(`Chat with ID ${chatId} does not exist`);
    }

    const chatHistory = await client.models.ChatMessage.list({
      filter: {
        chatId: {
          eq: chatId,
        },
      },
    });
    const { data: userMessage } = await client.models.ChatMessage.create({
      chatId,
      userId,
      message,
      messageType: messageType || MESSAGE_TYPE_TEXT,
      attachments: attachments || [],
    });

    if (!userMessage) {
      throw new Error("Failed to create user message");
    }

    const formattedChatHistory = formatChatHistory(chatHistory);

    const aiResponse = await aiService.getChatCompletion(
      message,
      formattedChatHistory
    );

    if (aiResponse) {
      const { data: aiMessageResult } = await client.models.ChatMessage.create({
        chatId,
        userId: AI_ASSISTANT_ID,
        message: aiResponse,
        messageType: MESSAGE_TYPE_TEXT,
        attachments: [],
      });
      console.log("AI response created:", aiMessageResult);

      if (!aiMessageResult) {
        throw new Error("Failed to store AI response");
      }

      return {
        ...userMessage,
        aiResponse: {
          id: aiMessageResult.id,
          message: aiResponse,
          userId: AI_ASSISTANT_ID,
          chatId,
        },
      };
    }

    return userMessage;
  } catch (error) {
    console.error("Error in handleSendMessage:", error);
    throw error;
  }
}

export async function handleCreateChatWithQuestionnaire(
  args: CreateChatWithQuestionnaireArgs,
  identity: IdentityInfo
) {
  console.log(
    "handleCreateChatWithQuestionnaire called with args:",
    JSON.stringify(args),
    "and identity:",
    JSON.stringify(identity)
  );

  const { name, description, questionnaireId } = args;

  // Use secure userId from identity
  const userId = identity.sub;
  if (!userId) {
    throw new Error("User authentication required");
  }

  try {
    let chatId;
    if (questionnaireId === "2") {
      chatId = `${userId}-health`;
    }

    const chat = await createStandardChat({
      id: chatId,
      name:
        name ||
        (questionnaireId === "2"
          ? "Health Assessment"
          : "Health Assistant Chat"),
      description:
        description ||
        (questionnaireId === "2"
          ? "Health tracking and assessment"
          : "Chat session with health assistant"),
      chatType: "AI",
      questionnaireId,
      participantUserIds: [userId],
      systemMessage:
        "Let's start with some questions to understand your needs better.",
    });

    return chat;
  } catch (error) {
    console.error("Error in handleCreateChatWithQuestionnaire:", error);
    throw error;
  }
}

export async function handleCreateHealthChat(
  args: CreateChatWithQuestionnaireArgs,
  identity: IdentityInfo
) {
  console.log(
    "handleCreateHealthChat called with args:",
    JSON.stringify(args),
    "and identity:",
    JSON.stringify(identity)
  );

  // Use secure userId from identity
  const userId = identity.sub;
  if (!userId) {
    throw new Error("User authentication required");
  }

  try {
    const chatId = `${userId}-health`;
    console.log(`Using deterministic health chat ID: ${chatId}`);

    const { data: existingChat } = await client.models.Chat.get({ id: chatId });
    if (existingChat) {
      console.log(
        `Health chat already exists with ID ${chatId}, returning existing chat`
      );
      return existingChat;
    }

    console.log("Creating new health chat with direct model approach");
    try {
      const { data: chat } = await client.models.Chat.create({
        id: chatId,
        name: "Health Assessment",
        description: "Health tracking and assessment",
        chatType: "AI",
        questionnaireId: "2",
        metadata: {
          createdBy: userId,
          isArchived: false,
          isQuestionnaireComplete: false,
          currentQuestionIndex: 0,
        },
      });

      if (!chat) {
        throw new Error(
          "Failed to create health chat - null response from Chat.create"
        );
      }

      console.log(`Successfully created health chat with ID: ${chat.id}`);

      console.log(`Adding participant ${userId} to chat ${chat.id}`);
      const { data: participant } = await client.models.ChatParticipant.create({
        id: `${chat.id}:${userId}`,
        chatId: chat.id,
        userId: userId,
        joinedAt: new Date().toISOString(),
      });

      if (!participant) {
        console.error(`Failed to add participant ${userId} to chat ${chat.id}`);
      } else {
        console.log(
          `Successfully added participant ${userId} to chat ${chat.id}`
        );
      }

      console.log(`Adding system message to chat ${chat.id}`);
      try {
        const { data: message } = await client.models.ChatMessage.create({
          chatId: chat.id,
          userId: AI_ASSISTANT_ID,
          message:
            "Let's start with some health questions to understand your needs better.",
          messageType: MESSAGE_TYPE_SYSTEM,
          attachments: [],
          createdAt: new Date().toISOString(),
        });

        if (message) {
          console.log(`Successfully added system message to chat ${chat.id}`);
        }
      } catch (msgError) {
        console.error(
          `Failed to add system message to chat ${chat.id}:`,
          msgError
        );
      }

      return chat;
    } catch (createError) {
      console.error("Error during direct Chat.create:", createError);
      throw createError;
    }
  } catch (error) {
    console.error("Error in handleCreateHealthChat:", error);

    if (error instanceof Error) {
      throw error;
    } else {
      throw new Error(`Failed to create health chat: ${error}`);
    }
  }
}

export async function handleCreateDoctorChat(
  {
    doctorId,
    name,
    description,
    questionnaireId,
    metadata,
  }: {
    doctorId: string;
    name?: string;
    description?: string;
    questionnaireId?: string;
    metadata?: any;
  },
  identity: IdentityInfo
) {
  // Use secure userId from identity
  const userId = identity.sub;
  if (!userId || !doctorId) {
    throw new Error("User authentication and doctorId are required");
  }

  try {
    const chat = await createStandardChat({
      id: `doctorchat-${userId}-${doctorId}`,
      name: name || "Doctor Chat",
      description: description || "Chat with your doctor",
      chatType: "DIRECT",
      questionnaireId,
      metadata,
      participantUserIds: [userId, doctorId],
      systemMessage: "Doctor chat created between patient and doctor.",
    });

    return chat;
  } catch (error) {
    console.error("Error creating doctor chat:", error);
    throw error;
  }
}

export async function handleCleanChat(chatId: string) {
  if (!chatId) {
    throw new Error("chatId is required");
  }
  try {
    const { data: messages } = await client.models.ChatMessage.list({
      filter: { chatId: { eq: chatId } },
    });
    if (messages && messages.length > 0) {
      for (const msg of messages) {
        if (msg.id) {
          await client.models.ChatMessage.delete({ id: msg.id });
        }
      }
    }
    await client.models.ChatMessage.create({
      chatId: chatId,
      userId: AI_ASSISTANT_ID,
      message:
        "Let's start with some health questions to understand your needs better.",
      messageType: MESSAGE_TYPE_SYSTEM,
      attachments: [],
      createdAt: new Date().toISOString(),
    });
    return { success: true, deleted: messages?.length || 0 };
  } catch (error) {
    console.error("Error cleaning chat messages:", error);
    throw error;
  }
}

export async function handleResetQuestionnaire(
  args: any,
  identity: IdentityInfo
) {
  if (!args.chatId) throw new Error("chatId is required");

  await client.models.Chat.update({
    id: args.chatId,
    metadata: {
      currentQuestionIndex: 0,
      isQuestionnaireComplete: false,
    },
  });

  await handleCleanChat(args.chatId);
  return { success: true };
}

export async function handleCreateGroupChat(
  {
    userIds,
    name,
    description,
    questionnaireId,
    metadata,
  }: {
    userIds: string[];
    name: string;
    description?: string;
    questionnaireId?: string;
    metadata?: any;
  },
  identity: IdentityInfo
) {
  // Ensure the authenticated user is included in the group
  const authenticatedUserId = identity.sub;
  if (!authenticatedUserId) {
    throw new Error("User authentication required");
  }

  if (!userIds || userIds.length === 0) {
    throw new Error("At least one user is required to create a group chat");
  }

  // Add authenticated user to the group if not already included
  const finalUserIds = userIds.includes(authenticatedUserId)
    ? userIds
    : [...userIds, authenticatedUserId];

  try {
    const chat = await createStandardChat({
      name,
      description,
      chatType: "GROUP",
      questionnaireId,
      metadata,
      participantUserIds: finalUserIds,
      systemMessage: `Group chat '${name}' created.`,
    });

    return chat;
  } catch (error) {
    console.error("Error creating group chat:", error);
    throw error;
  }
}
