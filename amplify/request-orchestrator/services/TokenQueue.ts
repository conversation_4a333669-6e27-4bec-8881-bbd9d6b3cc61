export class TokenQueue {
  private tokens: string[] = [];

  constructor(private processTokens: (text: string) => Promise<void>) {}

  add(token: string): void {
    this.tokens.push(token);
  }

  isEmpty(): boolean {
    return this.tokens.length === 0;
  }

  size(): number {
    return this.tokens.length;
  }

  async process(): Promise<void> {
    if (this.isEmpty()) return;

    const currentTokens = [...this.tokens];
    this.tokens = [];
    const tokensToSend = currentTokens.join("");

    if (!tokensToSend) {
      return;
    }

    console.log(`[TokenQueue] Processing tokens batch: "${tokensToSend}"`);

    try {
      await this.processTokens(tokensToSend);
    } catch (error) {
      console.error("[TokenQueue] Error processing tokens:", error);
    }
  }
}
