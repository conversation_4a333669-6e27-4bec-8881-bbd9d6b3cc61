import { AiService } from "./AiService";
import { EventManager } from "./EventManager";
import { formatChatHistory } from "../utils/chat-utils";
import {
  QuestionnaireService,
  QuestionnaireResult,
} from "./QuestionnaireService";
import { StreamHandler } from "./StreamHandler";
import { generateClient } from "aws-amplify/api";
import type { Schema } from "../../data/resource";
import { AI_ASSISTANT_ID } from "../../../constants/chat";
import { MedicalProfileService } from "./MedicalProfileService";

const vercelAIService = new AiService(process.env.OPENAI_API_KEY || "");
const client = generateClient<Schema>();

const MESSAGE_TYPE_TEXT = "TEXT";

export class StreamService {
  private eventManager: EventManager;
  private chatId: string;
  private remainingTimeGetter: () => number;
  private metadata: any = null;
  private questionnaireService: QuestionnaireService;
  private streamHandler: StreamHandler;

  constructor(
    eventManager: EventManager,
    chatId: string,
    remainingTimeGetter: () => number
  ) {
    this.eventManager = eventManager;
    this.chatId = chatId;
    this.remainingTimeGetter = remainingTimeGetter;
    this.questionnaireService = new QuestionnaireService(
      chatId,
      vercelAIService
    );
    this.streamHandler = new StreamHandler(vercelAIService);
  }

  private async updateMetadata(updates: any): Promise<void> {
    try {
      const { data: chat } = await client.models.Chat.get({
        id: this.chatId,
      });

      if (!chat?.metadata) {
        console.error("[StreamService] No metadata found in chat record");
        throw new Error("No metadata found in chat record");
      }

      console.log(
        "[StreamService] Current metadata before update:",
        JSON.stringify(chat.metadata)
      );

      if (
        updates.isQuestionnaireComplete === true &&
        !updates.questionnaireCompletedAt
      ) {
        updates.questionnaireCompletedAt = new Date().toISOString();
        console.log("[StreamService] Added questionnaire completion timestamp");
      }

      const updatedMetadata = {
        ...chat.metadata,
        ...updates,
      };

      const result = await client.models.Chat.update({
        id: this.chatId,
        metadata: updatedMetadata,
      });

      this.metadata = result.data?.metadata || chat.metadata;
    } catch (error) {
      console.error("[StreamService] Error updating metadata:", error);
    }
  }

  private async ensureMetadata(): Promise<any> {
    if (!this.metadata) {
      const { data: chat } = await client.models.Chat.get({
        id: this.chatId,
      });
      this.metadata = chat?.metadata || {};
    }
    return this.metadata;
  }

  async streamResponseAndKeepAlive(prompt: string): Promise<any> {
    console.log("[StreamService] Starting streaming process");

    return new Promise<any>(async (resolve, reject) => {
      try {
        const { data: chat } = await client.models.Chat.get({
          id: this.chatId,
        });
        this.metadata = chat?.metadata || {};

        const metadataIndicatesComplete =
          this.metadata.isQuestionnaireComplete === true ||
          this.metadata.questionnaireCompletedAt;

        if (!metadataIndicatesComplete) {
          await this.questionnaireService.initState();
        } else {
          console.log(
            "[StreamService] Metadata indicates questionnaire is already complete - skipping questionnaire flow entirely"
          );
          this.questionnaireService = new QuestionnaireService(
            this.chatId,
            vercelAIService
          );
          this.questionnaireService.forceMarkAsComplete();
          return this.handleRegularChatFlow(prompt, resolve, reject);
        }

        const isComplete = this.questionnaireService.isQuestionnaireComplete();
        console.log(
          `[StreamService] Questionnaire completion status: ${isComplete}`
        );

        if (
          (this.metadata.isQuestionnaireComplete === true ||
            this.metadata.questionnaireCompletedAt) &&
          !isComplete
        ) {
          console.log(
            "[StreamService] Metadata shows questionnaire complete but local state doesn't - updating local state"
          );
          await this.questionnaireService.forceMarkAsComplete();
          console.log(
            "[StreamService] Forced questionnaire completion state based on metadata"
          );
          return this.handleRegularChatFlow(prompt, resolve, reject);
        }

        if (!this.questionnaireService.isQuestionnaireComplete()) {
          console.log(
            "[StreamService] Questionnaire not complete, processing questionnaire flow"
          );
          try {
            const result: QuestionnaireResult =
              await this.questionnaireService.handleQuestionnaireResponse(
                prompt,
                this.eventManager
              );

            if (result.status === "completed") {
              console.log(
                "[StreamService] Questionnaire completed, immediately processing with regular chat flow"
              );

              await this.updateMetadata({
                isQuestionnaireComplete: true,
                questionnaireCompletedAt: new Date().toISOString(),
              });

              const { data: refreshedChat } = await client.models.Chat.get({
                id: this.chatId,
              });
              this.metadata = refreshedChat?.metadata || this.metadata;

              const finalUserResponse = result.userResponse || prompt;

              const strategy = this.questionnaireService.getStrategy();

              // Use the strategy's completion handler
              const completionResult = await strategy.onQuestionnaireComplete(
                this.chatId,
                this.questionnaireService.getContext(),
                finalUserResponse,
                this.eventManager
              );

              // Set metadata flags based on questionnaire type
              if (this.metadata.questionnaireId === "1") {
                // For diagnosis questionnaire
                return this.handleRegularChatFlow(
                  completionResult.enhancedPrompt,
                  resolve,
                  reject,
                  completionResult.showSpecialUI
                );
              } else {
                // For other questionnaire types
                return this.handleRegularChatFlow(
                  completionResult.enhancedPrompt,
                  resolve,
                  reject,
                  completionResult.showSpecialUI
                );
              }
            }

            if (result.chatMessage) {
              console.log(
                "[StreamService] Returning chat message from in-progress questionnaire:",
                result.chatMessage?.id
              );
              return resolve(result.chatMessage);
            }

            console.log(
              "[StreamService] Creating placeholder message for questionnaire response"
            );
            const { data: placeholderMessage } =
              await client.models.ChatMessage.create({
                chatId: this.chatId,
                userId: AI_ASSISTANT_ID,
                message: "Processing your input...",
                messageType: MESSAGE_TYPE_TEXT,
                attachments: [],
              });

            return resolve(placeholderMessage);
          } catch (error) {
            console.error(
              "[StreamService] Error in questionnaire processing:",
              error
            );
            reject(error);
            return;
          }
        } else {
          console.log(
            "[StreamService] Questionnaire already completed, proceeding directly to regular chat flow"
          );

          // Only send diagnosis message for HealthQuestionnaireStrategy (ID 1)
          if (
            this.metadata.diagnosisMessageSent !== true &&
            this.metadata.questionnaireId === "1"
          ) {
            console.log(
              "[StreamService] Generating initial diagnosis based on questionnaire answers"
            );

            const diagnosticPrompt = `Based on all the patient's answers to the questionnaire, please provide an initial assessment and possible diagnosis. Focus solely on providing diagnostic information and explanation of potential conditions.

DO NOT ask any questions in your response. This should be a purely informative diagnosis message.

Patient's latest input: ${prompt}`;

            const result = await this.handleRegularChatFlow(
              diagnosticPrompt,
              resolve,
              reject
            );

            return result;
          }

          return this.handleRegularChatFlow(prompt, resolve, reject);
        }
      } catch (error: any) {
        await this.handleCriticalError(error, reject);
      }
    });
  }

  private async handleRegularChatFlow(
    prompt: string,
    resolve: (value: any) => void,
    reject: (reason: any) => void,
    showMap: boolean = false
  ): Promise<void> {
    const { data: messagesData } =
      await client.models.ChatMessage.listMessagesByDate(
        {
          chatId: this.chatId,
        },
        { sortDirection: "ASC" }
      );

    const formattedChatHistory = formatChatHistory(messagesData);
    console.log("[StreamService] Chat history unform:", messagesData);
    console.log("[StreamService] Chat history loaded:", formattedChatHistory);

    const isQuestionnaireComplete =
      this.questionnaireService.isQuestionnaireComplete() ||
      this.metadata.isQuestionnaireComplete === true ||
      this.metadata.questionnaireCompletedAt;

    await this.ensureMetadata();

    // Only show diagnostic summary for HealthQuestionnaireStrategy (ID 1)
    const isFirstMessageAfterCompletion =
      isQuestionnaireComplete &&
      this.metadata.isQuestionnaireComplete === true &&
      this.metadata.diagnosisMessageSent !== true &&
      this.metadata.questionnaireId === "1"; // Check for health questionnaire

    const shouldShowMap = isFirstMessageAfterCompletion;

    let enhancedPrompt = prompt;
    if (shouldShowMap) {
      enhancedPrompt = `You are a medical assistant providing a diagnosis summary.
      
Your task is to analyze the patient's symptoms and provide a clear diagnostic assessment.

Important instructions:
1. DO NOT ask any follow-up questions in your response
2. Focus exclusively on providing your diagnostic assessment
3. Explain possible conditions that match the symptoms
4. Present your findings in a professional but accessible manner
5. If appropriate, suggest general next steps (but without asking questions)

${prompt}`;

      console.log(
        "[StreamService] Using enhanced diagnostic prompt for map message"
      );
    }

    console.log(
      `[StreamService] Regular chat flow: questionnaire complete=${isQuestionnaireComplete}, first message after completion=${isFirstMessageAfterCompletion}, show map=${shouldShowMap}`
    );

    try {
      const result = await this.streamHandler.processStream({
        prompt: enhancedPrompt,
        formattedChatHistory,
        chatId: this.chatId,
        eventManager: this.eventManager,
        showMap,
        onSuccess: async (messageId, chatMessage, completeResponse) => {
          if (showMap && this.metadata.questionnaireId === "1") {
            console.log(
              "[StreamService] This is a diagnostic message, updating metadata"
            );

            if (
              chatMessage &&
              chatMessage.message &&
              chatMessage.message.includes("?")
            ) {
              console.warn(
                "[StreamService] Warning: Diagnosis message contains questions, which is not ideal"
              );
            }

            const { data: freshChat } = await client.models.Chat.get({
              id: this.chatId,
            });

            if (freshChat?.metadata?.isQuestionnaireComplete === true) {
              await this.updateMetadata({ diagnosisMessageSent: true });
            } else {
              console.log(
                "[StreamService] Skipping diagnosis message flag - questionnaire not confirmed complete"
              );
            }
          }

          console.log(
            "[StreamService] Resolving with chat message:",
            chatMessage?.id
          );

          return { messageId, chatMessage };
        },
      });

      resolve(result.chatMessage);
    } catch (error) {
      console.error("[StreamService] Failed to initialize stream:", error);
      await this.eventManager.sendError(
        "Error initializing stream. Please try again."
      );
      reject(error);
    }
  }

  private async handleCriticalError(
    error: any,
    reject: (reason: any) => void
  ): Promise<void> {
    console.error("[StreamService] Critical error:", error);
    const userFriendlyMessage =
      "Failed to process your request due to a server error. Please try again later.";
    await this.eventManager.sendError(userFriendlyMessage);
    reject(error);
  }

  async handleTimeoutExceeded(): Promise<void> {
    const userFriendlyMessage =
      "Response interrupted due to time constraints. Please try again with a more specific question.";

    const { data: chatMessage } = await client.models.ChatMessage.create({
      chatId: this.chatId,
      userId: AI_ASSISTANT_ID,
      message: userFriendlyMessage,
      messageType: MESSAGE_TYPE_TEXT,
      attachments: [],
    });

    const messageId = chatMessage?.id || "";
    await this.eventManager.sendToken("\n\n" + userFriendlyMessage);
    await this.eventManager.sendComplete(messageId, userFriendlyMessage);
  }

  shouldTerminateEarly(): boolean {
    const remainingTime = this.remainingTimeGetter();
    const timeoutThreshold = 10000;

    if (remainingTime < timeoutThreshold) {
      console.log(
        `[StreamService] Remaining time ${remainingTime}ms is less than threshold ${timeoutThreshold}ms, stopping stream`
      );
      return true;
    }

    return false;
  }
}
