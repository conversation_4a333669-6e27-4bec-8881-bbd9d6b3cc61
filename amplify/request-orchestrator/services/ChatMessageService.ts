import { generateClient } from "aws-amplify/api";
import type { Schema } from "../../data/resource";
import { AI_ASSISTANT_ID } from "../../../constants/chat";

const client = generateClient<Schema>();

export class ChatMessageService {
  static async createCompletedMessage(
    chatId: string,
    message: string
  ): Promise<string> {
    try {
      const result = await client.models.ChatMessage.create({
        chatId,
        userId: AI_ASSISTANT_ID,
        message,
        messageType: "TEXT",
        attachments: [],
      });
      console.log(
        "[ChatMessageService] Message created successfully:",
        result.data?.id
      );
      if (!result.data?.id) {
        throw new Error("Failed to create message");
      }
      return result.data?.id;
    } catch (error) {
      console.error("[ChatMessageService] Failed to create message:", error);
      throw error;
    }
  }

  static async getChatHistory(chatId: string) {
    return await client.models.ChatMessage.list({
      filter: { chatId: { eq: chatId } },
    });
  }
}
