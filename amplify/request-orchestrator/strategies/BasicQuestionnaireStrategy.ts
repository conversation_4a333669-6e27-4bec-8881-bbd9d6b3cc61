// import { generateClient } from "@aws-amplify/api";
// import { Context } from "../interfaces/QuestionnaireContext";
// import { QuestionnaireStrategy } from "../interfaces/QuestionnaireStrategy";
// import { AiService } from "../services/AiService";
// import { EventManager } from "../services/EventManager";
// import { StreamHandler } from "../services/StreamHandler";
// import { formatChatHistory } from "../utils/chat-utils";
// import type { Schema } from "../../data/resource";

// const client = generateClient<Schema>();

// export class BasicQuestionnaireStrategy implements QuestionnaireStrategy {
//   private aiService: AiService;
//   private streamHandler: StreamHandler;

//   constructor(aiService: AiService) {
//     this.aiService = aiService;
//     this.streamHandler = new StreamHandler(aiService);
//   }

//   async initialize(context: Context): Promise<void> {
//     if (context.metadata.isQuestionnaireComplete) {
//       console.log(
//         "[BasicQuestionnaireStrategy] Questionnaire is complete - skipping question load"
//       );
//       context.questions = [];
//       return;
//     }

//     try {
//       const questionnaireId = context.metadata.questionnaireId || "basic";
//       console.log(
//         `[BasicQuestionnaireStrategy] Loading questions for questionnaire ID: ${questionnaireId}`
//       );

//       const questions = [
//         { prompt: "What's your name?" },
//         { prompt: "How old are you?" },
//         { prompt: "What brings you here today?" },
//       ];

//       console.log(
//         `[BasicQuestionnaireStrategy] Loaded ${questions.length} basic questions`
//       );
//       context.questions = questions;

//       if (typeof context.metadata.currentQuestionIndex !== "number") {
//         context.metadata.currentQuestionIndex = 0;
//       }

//       if (context.metadata.currentQuestionIndex >= context.questions.length) {
//         context.metadata.isQuestionnaireComplete = true;
//       }
//     } catch (error) {
//       console.error(
//         "[BasicQuestionnaireStrategy] Error loading questions:",
//         error
//       );
//       throw error;
//     }
//   }

//   async handleResponse(
//     userResponse: string,
//     context: Context,
//     eventManager: EventManager
//   ): Promise<{
//     nextQuestion: string | null;
//     isComplete: boolean;
//     messageId?: string;
//     chatMessage?: any;
//   }> {
//     const currentQuestionIndex =
//       typeof context.metadata.currentQuestionIndex === "number"
//         ? context.metadata.currentQuestionIndex
//         : 0;

//     if (
//       currentQuestionIndex >= context.questions.length ||
//       context.metadata.isQuestionnaireComplete
//     ) {
//       return {
//         nextQuestion: null,
//         isComplete: true,
//       };
//     }

//     const nextQuestion = context.questions[currentQuestionIndex];
//     if (!nextQuestion) {
//       await eventManager.sendError(
//         "Sorry, there was a problem with the questionnaire. Please try again."
//       );
//       throw new Error(`No question found at index ${currentQuestionIndex}`);
//     }

//     console.log(
//       `[BasicQuestionnaireStrategy] Processing question #${currentQuestionIndex + 1}: ${nextQuestion.prompt}`
//     );

//     const prompt = nextQuestion.prompt;
//     const formattedChatHistory = formatChatHistory(context.chatHistory);
//     if (!context.metadata.chatId) {
//       console.error(
//         "[BasicQuestionnaireStrategy] No chatId found in context metadata"
//       );
//       throw new Error("No chatId found in context metadata");
//     }
//     try {
//       const result = await this.streamHandler.processStream({
//         prompt,
//         formattedChatHistory,
//         chatId: context.metadata.chatId,
//         eventManager,
//         onSuccess: async (messageId, chatMessage) => {
//           console.log(
//             `[BasicQuestionnaireStrategy] Question sent with ID: ${messageId}`
//           );
//           return {
//             nextQuestion: prompt,
//             isComplete: false,
//             messageId,
//             chatMessage,
//           };
//         },
//       });

//       context.metadata.currentQuestionIndex = currentQuestionIndex + 1;

//       if (context.metadata.currentQuestionIndex >= context.questions.length) {
//         console.log("[BasicQuestionnaireStrategy] That was the final question");
//         context.metadata.isQuestionnaireComplete = true;
//       }

//       return result;
//     } catch (error) {
//       console.error(
//         "[BasicQuestionnaireStrategy] Error processing question:",
//         error
//       );
//       throw error;
//     }
//   }
// }
