import type { PostConfirma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "aws-lambda";
import { Amplify } from "aws-amplify";
import { generateClient } from "aws-amplify/data";
import { getAmplifyDataClientConfig } from "@aws-amplify/backend/function/runtime";
import { env } from "$amplify/env/post-confirmation"; // Adjust to match function name
import { Schema } from "../../data/resource"; // Adjust path to your schema

// Initialize Amplify
const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);
Amplify.configure(resourceConfig, libraryOptions);

const client = generateClient<Schema>();

// Configuration for AI chats
const AI_CHAT_CONFIGS = [
  {
    idSuffix: "als",
    name: "AI Doctor Agent: ALS",
    description: "AI Doctor specializing in ALS treatment and care",
    questionnaireId: "1",
    welcomeMessage:
      "Hello, I'm your AI Doctor Agent specializing in ALS. How can I assist you today?",
  },
];

const AI_ASSISTANT_ID = process.env.AI_ASSISTANT_ID || "your-ai-assistant-id"; // Use environment variable

async function createAIChat(
  userId: string,
  chatConfig: (typeof AI_CHAT_CONFIGS)[0]
): Promise<void> {
  try {
    const chatId = `${userId}-${chatConfig.idSuffix}`;

    // Check if chat already exists
    const { data: existingChat } = await client.models.Chat.get({ id: chatId });

    if (existingChat) {
      console.log(
        `Chat ${chatConfig.name} already exists for user ${userId}: ${existingChat.id}`
      );
      return;
    }

    console.log(
      `Creating chat ${chatConfig.name} for user ${userId} with ID: ${chatId}`
    );
    const { data: chat } = await client.models.Chat.create({
      id: chatId,
      name: chatConfig.name,
      description: chatConfig.description,
      chatType: "AI",
      questionnaireId: chatConfig.questionnaireId,
    });

    if (!chat) {
      console.error(
        `Failed to create chat ${chatConfig.name} for user ${userId}`
      );
      return;
    }

    await client.models.ChatParticipant.create({
      id: chatId,
      chatId: chatId,
      userId: userId,
      joinedAt: new Date().toISOString(),
    });

    const { data: message } = await client.models.ChatMessage.create({
      chatId: chatId,
      userId: AI_ASSISTANT_ID,
      message: chatConfig.welcomeMessage,
      messageType: "SYSTEM",
      attachments: [],
      createdAt: new Date().toISOString(),
    });

    console.log(`Successfully created chat ${chatConfig.name}: ${chatId}`);
  } catch (error) {
    console.error(
      `Error creating chat ${chatConfig.name} for user ${userId}:`,
      error
    );
  }
}

export const handler: PostConfirmationTriggerHandler = async (event) => {
  try {
    const userId = event.request.userAttributes.sub;
    const userName = event.userName;
    const email = event.request.userAttributes.email;

    // Create UserProfile record
    await client.models.User.create({
      email: email,
      userId: userId,
      name: userName,
    });

    console.log(`Successfully created UserProfile for user ${userId}`);

    // Create all configured AI chats
    await Promise.all(
      AI_CHAT_CONFIGS.map((config) => createAIChat(userId, config))
    );

    return event;
  } catch (error) {
    console.error("Error in post-confirmation handler:", error);
    return event;
  }
};
