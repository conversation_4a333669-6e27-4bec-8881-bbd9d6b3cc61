import { defineFunction, secret } from "@aws-amplify/backend";

export const syncMedicalData = defineFunction({
  name: "sync-medical-data",
  entry: "./handler.ts",
  environment: {
    ONEUP_CLIENT_ID: secret("ONEUP_CLIENT_ID"),
    ONEUP_CLIENT_SECRET: secret("ONEUP_CLIENT_SECRET"),
    GRAPHQL_ENDPOINT: secret("GRAPHQL_ENDPOINT"),
    GRAPHQL_API_KEY: secret("GRAPHQL_API_KEY"),
  },
  timeoutSeconds: 300, // 5 minutes for potentially large sync operations
});
