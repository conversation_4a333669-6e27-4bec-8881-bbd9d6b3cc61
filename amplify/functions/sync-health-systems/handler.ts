import { Amplify } from "aws-amplify";
import axios, { type AxiosError } from "axios";
import { generateClient } from "@aws-amplify/api";
import type { Schema } from "../../data/resource";
import type { <PERSON><PERSON> } from "aws-lambda";
import { getAmplifyDataClientConfig } from "@aws-amplify/backend/function/runtime";
import { env } from "$amplify/env/sync-medical-data";

// Logging helper for consistent timestamp logging
const logWithTimestamp = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
  if (data) console.log(JSON.stringify(data, null, 2));
};

logWithTimestamp("Function initialization started");

const { resourceConfig, libraryOptions } =
  await getAmplifyDataClientConfig(env);
Amplify.configure(resourceConfig, libraryOptions);
const client = generateClient<Schema>();

logWithTimestamp("Amplify client initialized");

interface ResponseType {
  success: boolean;
  message: string;
  errors?: string[];
}

// Enhanced type definitions to better match C# model
export enum Status {
  ConnectionWorking = "connection_working",
}

export enum State {
  Empty = "",
  AL = "AL",
  AK = "AK",
  AR = "AR",
  AZ = "AZ",
  CA = "CA",
  CO = "CO",
  CT = "CT",
  DC = "DC",
  DE = "DE",
  FL = "FL",
  GA = "GA",
  GU = "GU",
  HI = "HI",
  IA = "IA",
  ID = "ID",
  IL = "IL",
  IN = "IN",
  StateIn = "In",
  KS = "KS",
  KY = "KY",
  LA = "LA",
  MA = "MA",
  MD = "MD",
  ME = "ME",
  MI = "MI",
  MN = "MN",
  MO = "MO",
  MS = "MS",
  MT = "MT",
  NC = "NC",
  ND = "ND",
  NE = "NE",
  NH = "NH",
  NJ = "NJ",
  NM = "NM",
  NV = "NV",
  NY = "NY",
  OH = "OH",
  Ohio = "Ohio",
  OK = "OK",
  OR = "OR",
  PA = "PA",
  PR = "PR",
  RI = "RI",
  SC = "SC",
  SD = "SD",
  TN = "TN",
  TX = "TX",
  UT = "UT",
  VA = "VA",
  VI = "VI",
  VT = "VT",
  WA = "WA",
  WI = "WI",
  WV = "WV",
  WY = "WY",
}

// Interfaces for the 1up.health API response
export type Root = Root2[];

export interface Root2 {
  id: number;
  name: string;
  logo?: string;
  status: string;
  ehr: string;
  locations: Location[];
}

export interface Location {
  name: string;
  address: Address;
}

export interface Address {
  line: string[];
  city?: string;
  state?: string;
}

// Helper function to validate and normalize state data
const normalizeState = (stateStr?: string): string => {
  if (!stateStr) return "";

  const upperState = stateStr.toUpperCase();
  // Check if it's a valid state abbreviation
  if (Object.values(State).includes(upperState as State)) {
    return upperState;
  }

  // Map common state name variations to abbreviations
  const stateMap: Record<string, string> = {
    OHIO: "OH",
    INDIANA: "IN",
    CALIFORNIA: "CA",
    "NEW YORK": "NY",
    // Add more mappings as needed
  };

  return stateMap[upperState] || stateStr;
};

// Helper function to sanitize address data
const sanitizeAddress = (address: Address): Address => ({
  line:
    Array.isArray(address.line) && address.line.length > 0
      ? address.line.map((line) => line || "").filter(Boolean)
      : ["Unknown"],
  city: address.city || "Unknown",
  state: normalizeState(address.state),
});

// Helper function to sanitize health system data
const sanitizeHealthSystem = (system: Root2): Root2 => {
  // Ensure status is valid
  const status =
    system.status === Status.ConnectionWorking
      ? system.status
      : Status.ConnectionWorking;

  // Ensure locations array is valid
  const locations =
    Array.isArray(system.locations) && system.locations.length > 0
      ? system.locations.map((location) => ({
          name: location.name || system.name || "Unknown Location",
          address: sanitizeAddress(
            location.address || { line: [], city: "", state: "" }
          ),
        }))
      : [
          {
            name: system.name || "Unknown System",
            address: { line: ["Unknown"], city: "Unknown", state: "" },
          },
        ];

  return {
    ...system,
    status,
    locations,
    name: system.name || "Unknown System",
    ehr: system.ehr || "unknown",
  };
};

// Split array into chunks for batch processing
const chunkArray = <T>(array: T[], size: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
};

export const handler: Handler = async (event: any): Promise<ResponseType> => {
  const startTime = Date.now();
  logWithTimestamp("Handler execution started", { event });

  try {
    logWithTimestamp("Fetching health systems from 1up.health API");
    const apiStartTime = Date.now();

    const response = await axios.get<Root>(
      "https://api.1up.health/connect/system/clinical",
      {
        params: {
          client_id: env.ONEUP_CLIENT_ID,
          client_secret: env.ONEUP_CLIENT_SECRET,
        },
        timeout: 30000, // 30 second timeout
      }
    );

    const apiEndTime = Date.now();
    logWithTimestamp(
      `API request completed in ${apiEndTime - apiStartTime}ms`,
      {
        statusCode: response.status,
        dataLength: response.data.length,
      }
    );

    // Sanitize and prepare health systems
    logWithTimestamp("Processing and sanitizing health systems");
    const processStartTime = Date.now();

    const healthSystems = response.data
      .filter((system) => system && typeof system === "object") // Filter out null/undefined items
      .map((system) => sanitizeHealthSystem(system))
      .map((system) => {
        // Log validation issues but don't stop processing
        if (!system.name || !system.ehr || !system.status) {
          logWithTimestamp(`Invalid system data detected`, {
            id: system.id,
            name: system.name || "MISSING",
            ehr: system.ehr || "MISSING",
            status: system.status || "MISSING",
          });
        }

        // Prepare for storing in DynamoDB
        return JSON.stringify({
          id: system.id.toString(),
          name: system.name || "Unknown System",
          logo:
            system.logo ||
            "https://1uphealth-assets.s3-us-west-2.amazonaws.com/systems/health-system-default.png",
          ehr: system.ehr || "unknown",
          status: system.status || Status.ConnectionWorking,
          locations: system.locations.map((location) => ({
            name: location.name || "Unknown Location",
            address: {
              line: location.address.line || ["Unknown"],
              city: location.address.city || "Unknown",
              state: location.address.state || "",
            },
          })),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      });

    const processEndTime = Date.now();
    logWithTimestamp(
      `Health systems processing completed in ${processEndTime - processStartTime}ms`,
      {
        systemCount: healthSystems.length,
      }
    );

    if (healthSystems.length === 0) {
      logWithTimestamp("No health systems to sync");
      return {
        success: false,
        message: "No health systems to sync",
        errors: ["API returned no health systems"],
      };
    }

    // Process in batches of 25 (DynamoDB batch write limit)
    const batches = chunkArray(healthSystems, 25);
    logWithTimestamp(`Created ${batches.length} batches for processing`, {
      batchCount: batches.length,
      batchSize: 25,
    });

    const errors: string[] = [];
    let successCount = 0;

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const batchStartTime = Date.now();
      logWithTimestamp(`Processing batch ${i + 1}/${batches.length}`, {
        batchSize: batch.length,
      });

      try {
        logWithTimestamp(`Calling BatchCreateHealthSystem for batch ${i + 1}`);

        if (i === 0) {
          // Log the raw string for debugging
          logWithTimestamp("First item in batch sample:", {
            firstItemString: batch[0],
          });
        }

        const { errors: batchErrors } =
          await client.mutations.BatchCreateHealthSystem({
            contents: batch,
          });

        const batchEndTime = Date.now();

        if (batchErrors && batchErrors.length > 0) {
          logWithTimestamp(
            `Batch ${i + 1} completed with errors in ${batchEndTime - batchStartTime}ms`,
            {
              errorCount: batchErrors.length,
              errors: batchErrors,
            }
          );
          errors.push(
            ...batchErrors.map((error) => `Batch error: ${error.message}`)
          );
        } else {
          logWithTimestamp(
            `Batch ${i + 1} completed successfully in ${batchEndTime - batchStartTime}ms`
          );
          successCount += batch.length;
        }
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        logWithTimestamp(`Batch ${i + 1} failed with error`, {
          error: errorMsg,
        });

        if (error instanceof Error && error.stack) {
          logWithTimestamp("Error stack trace:", { stack: error.stack });
        }

        errors.push(`Batch processing error: ${errorMsg}`);
        await new Promise((resolve) => setTimeout(resolve, 500));
      }

      if (i < batches.length - 1) {
        await new Promise((resolve) => setTimeout(resolve, 200));
      }
    }

    const endTime = Date.now();
    logWithTimestamp(
      `Handler execution completed in ${endTime - startTime}ms`,
      {
        successCount,
        errorCount: errors.length,
        totalTimeMs: endTime - startTime,
      }
    );

    return {
      success: errors.length === 0,
      message:
        errors.length === 0
          ? `Successfully synced ${successCount} health systems`
          : `Synced ${successCount} health systems with ${errors.length} errors`,
      errors: errors.length > 0 ? errors : undefined,
    };
  } catch (error) {
    const axiosError = error as AxiosError;
    const endTime = Date.now();

    logWithTimestamp(
      `Handler execution failed after ${endTime - startTime}ms`,
      {
        error: axiosError.message,
        response: axiosError.response?.data,
        status: axiosError.response?.status,
        config: {
          url: axiosError.config?.url,
          method: axiosError.config?.method,
          timeout: axiosError.config?.timeout,
        },
      }
    );

    console.error("Error syncing health systems:", axiosError.message);
    return {
      success: false,
      message: `Failed to sync health systems: ${axiosError.message}`,
      errors: [axiosError.message],
    };
  }
};
