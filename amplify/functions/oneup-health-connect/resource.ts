import { defineFunction, secret } from "@aws-amplify/backend";

export const oneupHealthConnect = defineFunction({
  name: "oneup-health-connect",
  entry: "./handler.ts",
  environment: {
    ONEUP_CLIENT_ID: secret("ONEUP_CLIENT_ID"),
    ONEUP_CLIENT_SECRET: secret("ONEUP_CLIENT_SECRET"),
    APP_DOMAIN: process.env.APP_DOMAIN || "http://localhost:3000",

    GRAPHQL_ENDPOINT: secret("GRAPHQL_ENDPOINT"),
    GRAPHQL_API_KEY: secret("GRAPHQL_API_KEY"),
  },
  runtime: 20,
  timeoutSeconds: 300,
});
