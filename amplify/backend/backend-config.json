{"api": {"jinix": {"dependsOn": [], "output": {"authConfig": {"additionalAuthenticationProviders": [{"apiKeyConfig": {"apiKeyExpirationDate": "2025-03-04T09:14:38.838Z", "apiKeyExpirationDays": 7, "description": ""}, "authenticationType": "API_KEY"}], "defaultAuthentication": {"authenticationType": "AWS_IAM"}}}, "providerPlugin": "awscloudformation", "service": "AppSync"}}, "auth": {"jinix74face0a": {"customAuth": false, "dependsOn": [], "frontendAuthConfig": {"mfaConfiguration": "OFF", "mfaTypes": ["SMS"], "passwordProtectionSettings": {"passwordPolicyCharacters": [], "passwordPolicyMinLength": 8}, "signupAttributes": ["EMAIL"], "socialProviders": [], "usernameAttributes": ["EMAIL"], "verificationMechanisms": ["EMAIL"]}, "providerPlugin": "awscloudformation", "service": "Cognito"}}}