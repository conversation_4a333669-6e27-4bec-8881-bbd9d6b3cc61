import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { SavedPostQueryResponse } from "@/types/saved-post";

export const bookmarkService = {
  createBookmark: async (userId: string, postId: string) => {
    const client = generateClient<Schema>();
    
    return await client.graphql({
      query: `
        mutation CreateSavedPost($input: CreateSavedPostInput!) {
          createSavedPost(input: $input) {
            id
            userId
            postId
            createdAt
          }
        }
      `,
      variables: {
        input: {
          userId,
          postId,
          createdAt: new Date().toISOString()
        }
      }
    });
  },

  findBookmark: async (userId: string, postId: string) => {
    const client = generateClient<Schema>();
    
    return await client.graphql<SavedPostQueryResponse>({
      query: `
        query GetSavedPostsByUserAndPost($userId: String!, $postId: String!) {
          listSavedPostsByUser(userId: $userId, filter: { postId: { eq: $postId } }) {
            items {
              id
              postId
            }
          }
        }
      `,
      variables: {
        userId,
        postId
      }
    });
  },

  deleteBookmark: async (bookmarkId: string) => {
    const client = generateClient<Schema>();
    
    return await client.graphql({
      query: `
        mutation DeleteSavedPost($input: DeleteSavedPostInput!) {
          deleteSavedPost(input: $input) {
            id
          }
        }
      `,
      variables: {
        input: { id: bookmarkId }
      }
    });
  }
};
