import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";

const client = generateClient<Schema>();

export const postService = {
  async createPost(postData: {
    userId: string;
    userName: string;
    title: string;
    content: string;
    images: string[];
    communityId?: string;
  }) {
    try {
      const { userId, userName, title, content, images, communityId } = postData;
      
      const newPost = {
        id: crypto.randomUUID(),
        userId,
        userName,
        title,
        content,
        images,
        likes: 0,
        commentsCount: 0,
        createdAt: new Date().toISOString(),
        communityId
      };
      
      const result = await client.models.Post.create(newPost);
      return result;
    } catch (error) {
      console.error("Error creating post:", error);
      throw error;
    }
  },
  
  async getPostsByCommunity(communityId: string) {
    try {
      const result = await client.models.Post.list({
        filter: {
          communityId: { eq: communityId }
        }
      });
      
      // Sort posts by createdAt in descending order
      const sortedData = result.data.sort((a, b) => {
        // Handle null values by treating them as older than any valid date
        if (!a.createdAt) return 1;
        if (!b.createdAt) return -1;
        // If both have valid dates, compare them
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });
      
      return sortedData;
    } catch (error) {
      console.error("Error fetching community posts:", error);
      throw error;
    }
  },
  
  async getPostById(postId: string) {
    try {
      const result = await client.models.Post.get({ id: postId });
      return result;
    } catch (error) {
      console.error("Error getting post:", error);
      throw error;
    }
  },
  
  async updatePost(postId: string, updates: any) {
    try {
      const result = await client.models.Post.update({
        id: postId,
        ...updates
      });
      return result;
    } catch (error) {
      console.error("Error updating post:", error);
      throw error; 
    }
  }
};
