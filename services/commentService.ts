import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { CommentCreate, CommentListResponse } from "@/types/comment";

// Cache implementation
interface CacheItem<T> {
  data: T;
  timestamp: number;
}

const CACHE_EXPIRATION = 5 * 60 * 1000; // 5 minutes
const commentCache = new Map<string, CacheItem<any>>();

// Only pending requests to prevent duplicates, no caching
const pendingRequests = new Map<string, Promise<any>>();

const getCachedData = <T>(key: string): T | null => {
  const item = commentCache.get(key);
  if (!item) return null;
  
  const now = Date.now();
  if (now - item.timestamp > CACHE_EXPIRATION) {
    commentCache.delete(key);
    return null;
  }
  
  return item.data as T;
};

const setCachedData = <T>(key: string, data: T): void => {
  commentCache.set(key, {
    data,
    timestamp: Date.now()
  });
};

const invalidateCache = (postId?: string): void => {
  if (postId) {
    commentCache.delete(`comments_${postId}`);
  } else {
    commentCache.clear();
  }
};

export const commentService = {
  fetchComments: async (postId: string, forceRefresh = false) => {
    // If not forcing refresh, check cache first
    if (!forceRefresh) {
      const cacheKey = `comments_${postId}`;
      const cachedData = getCachedData<CommentListResponse>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    } else {
      // If forcing refresh, invalidate the cache for this post
      invalidateCache(postId);
    }

    // Check if request is already pending
    if (pendingRequests.has(postId)) {
      return pendingRequests.get(postId);
    }

    const client = generateClient<Schema>();
    
    const requestPromise = (async () => {
      try {
        const result = await client.graphql<CommentListResponse>({
          query: `
            query ListCommentsByPost($postId: String!) {
              listCommentsByPost(postId: $postId) {
                items {
                  id
                  content
                  userId
                  postId
                  parentCommentId
                  createdAt
                  likes
                  user {
                    name
                    profilePicture
                  }
                  likedBy {
                    items {
                      id
                      userId
                    }
                  }
                }
              }
            }
          `,
          variables: { postId }
        });

        // Store result in cache
        setCachedData(`comments_${postId}`, result);
        return result;
      } catch (error) {
        console.error('Error fetching comments:', error);
        throw error;
      } finally {
        // Remove from pending requests
        pendingRequests.delete(postId);
      }
    })();

    // Store pending request
    pendingRequests.set(postId, requestPromise);
    return requestPromise;
  },

  createComment: async (comment: CommentCreate) => {
    const client = generateClient<Schema>();
    
    const result = await client.models.Comment.create(comment);
    
    // Invalidate cache after creating comment
    if (comment.postId) {
      invalidateCache(comment.postId);
      // Return fresh comments data
      const refreshedComments = await commentService.fetchComments(comment.postId, true);
      return { 
        comment: result, 
        refreshedComments: refreshedComments 
      };
    }
    
    return { comment: result, refreshedComments: null };
  },

  createReply: async (reply: CommentCreate) => {
    const client = generateClient<Schema>();
    
    // Clear the cache before creating the reply to ensure we'll get fresh data
    if (reply.postId) {
      invalidateCache(reply.postId);
    }
    
    const result = await client.models.Comment.create(reply);
    
    if (reply.postId) {
      try {
        // Force a complete refresh by directly querying rather than using fetchComments
        // This bypasses any potential caching issues
        const client = generateClient<Schema>();
        const refreshedComments = await client.graphql<CommentListResponse>({
          query: `
            query ListCommentsByPost($postId: String!) {
              listCommentsByPost(postId: $postId) {
                items {
                  id
                  content
                  userId
                  postId
                  parentCommentId
                  createdAt
                  likes
                  user {
                    name
                    profilePicture
                  }
                  likedBy {
                    items {
                      id
                      userId
                    }
                  }
                }
              }
            }
          `,
          variables: { postId: reply.postId }
        });
        
        // Update cache with fresh data
        setCachedData(`comments_${reply.postId}`, refreshedComments);
        
        return { 
          reply: result, 
          refreshedComments: refreshedComments 
        };
      } catch (error) {
        console.error('Error refreshing comments after reply:', error);
        // Fall back to the standard refresh method if direct approach fails
        const refreshedComments = await commentService.fetchComments(reply.postId, true);
        return { 
          reply: result, 
          refreshedComments: refreshedComments 
        };
      }
    }
    
    return { reply: result, refreshedComments: null };
  },

  // Force refresh comments for a post
  refreshComments: async (postId: string) => {
    return await commentService.fetchComments(postId, true);
  },
  
  // Export cache control functions for external use
  clearCache: () => invalidateCache(),
  clearPostCache: (postId: string) => invalidateCache(postId)
};
