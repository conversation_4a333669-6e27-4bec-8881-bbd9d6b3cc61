# Setting Up Secrets for Jinix

This document describes how to properly configure secrets required by the Jinix application.

## OpenAI API Key

The application requires an OpenAI API key to function correctly. Follow these steps to set it up:

1. Create a secret named `OPENAI_API_KEY` in the Amplify backend:

```bash
npx ampx secrets set OPENAI_API_KEY
```

2. When prompted, enter your OpenAI API key.

3. The key will be securely stored and made available to the functions that need it.

## Verifying Your Setup

To verify that your secrets are properly configured:

1. Run the Amplify sandbox:

```bash
npx ampx sandbox
```

2. Test the relevant functions to ensure they can access the secret.

## Important Security Notes

- Never store API keys or other secrets directly in environment variables or code
- Don't commit secrets to your repository
- Use the Amplify secrets management system for all sensitive information
- Each environment (dev, staging, prod) may need its own set of secrets
