/**
 * Path utility functions for chat routing and navigation
 */

import { ChatViewState } from "@/components/Chat/context/ViewStateContext";

export interface ParsedPath {
  chatId: string | null;
  userId?: string | null;
  isDetailsRoute: boolean;
  isUserDetailsRoute?: boolean;
  isNewChatRoute: boolean;
  isConfirmGroupChatRoute: boolean;
  isChatListRoute: boolean;
  isChatConversationRoute: boolean;
  routeType:
    | "chat-list"
    | "new-chat"
    | "confirm-group"
    | "chat-conversation"
    | "chat-details"
    | "user-details"
    | "unknown";
}

/**
 * Parses the pathname and extracts chat routing information
 */
export function parseChatPathname(pathname: string | null): ParsedPath {
  if (!pathname) {
    return {
      chatId: null,
      isDetailsRoute: false,
      isNewChatRoute: false,
      isConfirmGroupChatRoute: false,
      isChatListRoute: false,
      isChatConversationRoute: false,
      routeType: "unknown",
    };
  }

  // Handle exact matches first
  if (pathname === "/chats") {
    return {
      chatId: null,
      isDetailsRoute: false,
      isNewChatRoute: false,
      isConfirmGroupChatRoute: false,
      isChatListRoute: true,
      isChatConversationRoute: false,
      routeType: "chat-list",
    };
  }

  if (pathname === "/chats/new") {
    return {
      chatId: null,
      isDetailsRoute: false,
      isNewChatRoute: true,
      isConfirmGroupChatRoute: false,
      isChatListRoute: false,
      isChatConversationRoute: false,
      routeType: "new-chat",
    };
  }

  if (pathname === "/chats/new/confirm") {
    return {
      chatId: null,
      isDetailsRoute: false,
      isNewChatRoute: false,
      isConfirmGroupChatRoute: true,
      isChatListRoute: false,
      isChatConversationRoute: false,
      routeType: "confirm-group",
    };
  }

  // Handle dynamic chat routes: /chats/[chatId] and /chats/[chatId]/details
  if (pathname.startsWith("/chats/")) {
    const pathParts = pathname.split("/");

    // Ensure we have at least 3 parts: ['', 'chats', 'chatId']
    if (pathParts.length >= 3 && pathParts[1] === "chats") {
      const chatId = pathParts[2];

      // Skip if it's a known static route
      if (chatId === "new") {
        return {
          chatId: null,
          isDetailsRoute: false,
          isNewChatRoute: true,
          isConfirmGroupChatRoute: false,
          isChatListRoute: false,
          isChatConversationRoute: false,
          routeType: "new-chat",
        };
      }

      // Check if it's a details route: /chats/[chatId]/details
      const isDetailsRoute =
        pathParts.length >= 4 && pathParts[3] === "details";

      // Check if it's a user details route: /chats/[chatId]/user/[userId]
      const isUserDetailsRoute =
        pathParts.length >= 5 && pathParts[3] === "user";

      // Extract userId if this is a user details route
      const userId = isUserDetailsRoute ? pathParts[4] : null;

      return {
        chatId,
        userId,
        isDetailsRoute,
        isUserDetailsRoute,
        isNewChatRoute: false,
        isConfirmGroupChatRoute: false,
        isChatListRoute: false,
        isChatConversationRoute: !isDetailsRoute && !isUserDetailsRoute,
        routeType: isUserDetailsRoute
          ? "user-details"
          : isDetailsRoute
            ? "chat-details"
            : "chat-conversation",
      };
    }
  }

  // Default case for unrecognized paths
  return {
    chatId: null,
    isDetailsRoute: false,
    isNewChatRoute: false,
    isConfirmGroupChatRoute: false,
    isChatListRoute: false,
    isChatConversationRoute: false,
    routeType: "unknown",
  };
}

/**
 * Determines the appropriate view state based on parsed pathname
 */
export function getViewStateFromPath(
  parsedPath: ParsedPath,
  currentActiveView?: ChatViewState,
  selectedUserDetail?: unknown
): ChatViewState {
  // Handle USER_DETAILS state specially - only change if no user is selected
  if (currentActiveView === "USER_DETAILS" && selectedUserDetail) {
    return "USER_DETAILS";
  }

  // Handle CONFIRM_GROUP_CHAT state specially to prevent loops
  if (
    currentActiveView === "CONFIRM_GROUP_CHAT" &&
    parsedPath.isConfirmGroupChatRoute
  ) {
    return "CONFIRM_GROUP_CHAT";
  }

  // Map route types to view states
  switch (parsedPath.routeType) {
    case "chat-list":
      return "CHAT_LIST";
    case "new-chat":
      return "NEW_CHAT";
    case "confirm-group":
      return "CONFIRM_GROUP_CHAT";
    case "chat-details":
      return "CHAT_DETAILS";
    case "user-details":
      return "USER_DETAILS";
    case "chat-conversation":
      // Only set to CHAT_CONVERSATION if not already in special states
      if (currentActiveView !== "NEW_CHAT") {
        return "CHAT_CONVERSATION";
      }
      return currentActiveView;
    default:
      return "CHAT_LIST";
  }
}

/**
 * Validates if a chatId is valid (basic validation)
 */
export function isValidChatId(chatId: string | null): boolean {
  if (!chatId) return false;

  // Basic validation - chatId should be a non-empty string
  // and not contain any path separators
  return chatId.length > 0 && !chatId.includes("/") && !chatId.includes("\\");
}

/**
 * Generates paths for navigation
 */
export const ChatPaths = {
  chatList: () => "/chats",
  newChat: () => "/chats/new",
  confirmGroupChat: () => "/chats/new/confirm",
  chatConversation: (chatId: string) => `/chats/${chatId}`,
  chatDetails: (chatId: string) => `/chats/${chatId}/details`,
  userDetails: (chatId: string, userId: string) =>
    `/chats/${chatId}/user/${userId}`,
};

/**
 * Checks if two paths represent the same chat
 */
export function isSameChatPath(
  path1: string | null,
  path2: string | null
): boolean {
  if (!path1 || !path2) return false;

  const parsed1 = parseChatPathname(path1);
  const parsed2 = parseChatPathname(path2);

  return parsed1.chatId === parsed2.chatId && parsed1.chatId !== null;
}

/**
 * Gets the chat ID from various input formats
 */
export function extractChatId(input: string | null): string | null {
  if (!input) return null;

  // If it's already just a chatId (no slashes)
  if (!input.includes("/")) {
    return isValidChatId(input) ? input : null;
  }

  // If it's a path, parse it
  const parsed = parseChatPathname(input);
  return parsed.chatId;
}

/**
 * Navigation utilities for route-based redirection
 */
export const NavigationUtils = {
  /**
   * Gets the appropriate back path based on current route
   */
  getBackPath: (currentPath: string, chatId?: string | null): string => {
    const parsed = parseChatPathname(currentPath);

    switch (parsed.routeType) {
      case "chat-details":
        // From chat details, go back to chat conversation
        return chatId
          ? ChatPaths.chatConversation(chatId)
          : ChatPaths.chatList();

      case "chat-conversation":
        // From chat conversation, go back to chat list
        return ChatPaths.chatList();

      case "new-chat":
        // From new chat, go back to chat list
        return ChatPaths.chatList();

      case "confirm-group":
        // From confirm group, go back to new chat
        return ChatPaths.newChat();

      default:
        // Default fallback to chat list
        return ChatPaths.chatList();
    }
  },

  /**
   * Determines if router.back() should be used instead of programmatic navigation
   * This is optimized for mobile navigation patterns
   */
  shouldUseRouterBack: (currentPath: string): boolean => {
    const parsed = parseChatPathname(currentPath);

    // Use router.back() for details routes - typically entered from conversation
    if (parsed.routeType === "chat-details") {
      return true;
    }

    // Use router.back() for confirm-group -> new-chat navigation
    if (parsed.routeType === "confirm-group") {
      return true;
    }

    // Use router.back() for user details navigation
    if (currentPath.includes("details") || currentPath.includes("user")) {
      return true;
    }

    return false;
  },

  /**
   * Handles navigation with optimal strategy (router.back vs router.push)
   */
  navigate: (
    router: { back: () => void; push: (path: string) => void },
    currentPath: string,
    targetPath?: string
  ) => {
    if (NavigationUtils.shouldUseRouterBack(currentPath)) {
      router.back();
    } else {
      const fallbackPath =
        targetPath || NavigationUtils.getBackPath(currentPath);
      router.push(fallbackPath);
    }
  },

  /**
   * Gets navigation options for mobile-optimized back navigation
   */
  getMobileBackAction: (currentPath: string, chatId?: string | null) => {
    const backPath = NavigationUtils.getBackPath(currentPath, chatId);
    const shouldUseBack = NavigationUtils.shouldUseRouterBack(currentPath);

    return {
      backPath,
      shouldUseBack,
      action: shouldUseBack ? "router.back()" : `router.push('${backPath}')`,
    };
  },

  /**
   * Optimized mobile back handler - intelligently chooses between router.back() and router.push()
   */
  handleMobileBack: (
    router: { back: () => void; push: (path: string) => void },
    currentPath: string,
    chatId?: string | null,
    setActiveView?: (view: ChatViewState) => void
  ) => {
    const parsed = parseChatPathname(currentPath);

    // For details routes, prefer router.back() for better UX
    if (parsed.routeType === "chat-details") {
      router.back();
      setActiveView?.("CHAT_CONVERSATION");
      return;
    }

    // For user details routes, prefer router.back() for better UX
    if (parsed.routeType === "user-details") {
      router.back();
      setActiveView?.("CHAT_CONVERSATION");
      return;
    }

    // For confirm group, go back to new chat
    if (parsed.routeType === "confirm-group") {
      router.back();
      setActiveView?.("NEW_CHAT");
      return;
    }

    // For other routes, use programmatic navigation
    const backPath = NavigationUtils.getBackPath(currentPath, chatId);
    router.push(backPath);
  },
};

/**
 * Enhanced utilities for details panel navigation
 */
export const DetailsPanelUtils = {
  /**
   * Determines if details panel should be visible based on route and view state
   */
  shouldShowDetailsPanel: (
    parsedPath: ParsedPath,
    activeView: ChatViewState,
    isMobile: boolean
  ): boolean => {
    if (isMobile) return false; // Details panel is handled differently on mobile

    return (
      parsedPath.isDetailsRoute ||
      activeView === "CHAT_DETAILS" ||
      activeView === "USER_DETAILS" ||
      (activeView === "NEW_CHAT" && !isMobile)
    );
  },

  /**
   * Gets the appropriate details panel content based on route and state
   */
  getDetailsPanelContent: (
    parsedPath: ParsedPath,
    activeView: ChatViewState,
    // We're keeping this parameter for API consistency, but ignoring it
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    _selectedUserDetail?: unknown
  ): "chat-details" | "user-details" | "new-chat" | null => {
    if (parsedPath.isUserDetailsRoute || activeView === "USER_DETAILS") {
      return "user-details";
    }

    if (parsedPath.isDetailsRoute || activeView === "CHAT_DETAILS") {
      return "chat-details";
    }

    if (activeView === "NEW_CHAT") {
      return "new-chat";
    }

    return null;
  },

  /**
   * Handles details panel route synchronization
   */
  syncDetailsWithRoute: (
    router: { push: (path: string) => void },
    chatId: string | null,
    activeView: ChatViewState,
    targetDetailsState: boolean,
    userId?: string | null
  ) => {
    if (!chatId) return;

    if (targetDetailsState) {
      if (activeView === "USER_DETAILS" && userId) {
        // Navigate to user details route
        router.push(ChatPaths.userDetails(chatId, userId));
      } else if (activeView !== "CHAT_DETAILS") {
        // Navigate to chat details route
        router.push(ChatPaths.chatDetails(chatId));
      }
    } else if (
      !targetDetailsState &&
      (activeView === "CHAT_DETAILS" || activeView === "USER_DETAILS")
    ) {
      // Navigate back to conversation route
      router.push(ChatPaths.chatConversation(chatId));
    }
  },

  /**
   * Enhanced back navigation from details panel
   */
  handleDetailsBack: (
    router: { back: () => void; push: (path: string) => void },
    pathname: string,
    chatId: string | null,
    setActiveView?: (view: ChatViewState) => void
  ) => {
    const parsed = parseChatPathname(pathname);

    if ((parsed.isDetailsRoute || parsed.isUserDetailsRoute) && chatId) {
      // Use router.back() for better UX when coming from details route
      router.back();
      setActiveView?.("CHAT_CONVERSATION");
    } else {
      // Fallback to programmatic navigation
      const backPath = chatId
        ? ChatPaths.chatConversation(chatId)
        : ChatPaths.chatList();
      router.push(backPath);
      setActiveView?.("CHAT_CONVERSATION");
    }
  },
};
