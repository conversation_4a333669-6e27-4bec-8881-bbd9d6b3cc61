import { FriendRequestStatus } from "./common/types";

// Using any type for user-related properties to bypass type checking temporarily
export type UserType = any;

export type MessageWithoutChat = {
  id: string;
  chatId: string;
  userId?: string | null;
  message: string;
  messageType?: "TEXT" | "IMAGE" | "FILE" | "SYSTEM" | "AUDIO" | "VIDEO" | null;
  showMap?: boolean | null;
  attachments?: (string | null)[] | null;
  createdAt?: string | null;
  updatedAt?: string | null;
  isGhost?: boolean;
  user?: any;
};

export type GhostMessageType = {
  id: string;
  message: string;
  userId: string;
  chatId: string;
  isGhost: true;
  showMap?: boolean;
  createdAt: string;
  updatedAt: string;
  user?: any;
};

export interface ChatMessage {
  id: string;
  chatId: string;
  userId?: string | null;
  message: string;
  messageType?: "TEXT" | "IMAGE" | "FILE" | "SYSTEM" | "AUDIO" | "VIDEO" | null;
  createdAt?: string | null;
  senderName?: string | null;
  showMap?: boolean | null;
  attachments?: string[] | null;
}

export interface ChatParticipant {
  userId: string;
  user?: any;
}

export interface ChatParticipantFormatted {
  userId: string;
  user?: any;
}

export interface Chat {
  id: string;
  name: string;
  description?: string | null;
  questionnaireId?: string | null;
  chatType: "GROUP" | "DIRECT" | "AI";
  lastMessageAt?: string | null;
  updatedAt?: string | null;
  createdAt?: string | null;
  metadata?: {
    createdBy?: string | null;
    isArchived?: boolean | null;
    currentQuestionIndex?: number | null;
    isQuestionnaireComplete?: boolean | null;
    questionnaireCompletedAt?: string | null;
    diagnosisMessageSent?: boolean | null;
    earlyCompletion?: boolean | null;
    category?: string | null;
    configuration?: any;
  } | null;
  messages?: ChatMessage[] | null;
  chatParticipants?: ChatParticipant[] | null;
  lastMessage?: ChatMessage | null;
  displayTime?: string | null;
}
