export interface Comment {
  id: string
  userId: string
  content: string
  createdAt: string
  likes: number
  postId: string
  parentCommentId?: string | null
  user: {
    name: string
    profilePicture?: string
  }
  isLiked?: boolean;
}

export interface GroupedComment extends Comment {
  replies: GroupedComment[]
  isLiked?: boolean;
}

export interface CommentListResponse {
  listCommentsByPost: {
    items: CommentWithLikes[];
  }
}

export interface CommentCreate {
  content: string;
  userId: string;
  postId: string;
  likes: number;
  parentCommentId?: string; // Add this property as optional
}

export interface CommentWithLikes {
  id: string;
  content: string;
  userId: string;
  postId: string;
  parentCommentId?: string;
  createdAt: string;
  likes: number;
  user: {
    name: string;
    profilePicture?: string;
  };
  likedBy: {
    items: {
      id: string
      userId: string
    }[];
  };
  isLiked?: boolean;
}
