"use client";

import { useState, useEffect } from "react";
import { generateClient } from "aws-amplify/api";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Edit, Trash, MoveUp, MoveDown } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Schema } from "@/amplify/data/resource";
import { parseAmplifyConfig } from "aws-amplify/utils";
import { Amplify } from "aws-amplify";
import outputs from "@/amplify_outputs.json";
const amplifyConfig = parseAmplifyConfig(outputs);
Amplify.configure({
  ...amplifyConfig,
  API: {
    ...amplifyConfig.API,
    Events: {
      endpoint:
        "https://jo6vp743zjf6xdt4b6u5amf434.appsync-api.us-east-1.amazonaws.com/event",
      region: "us-east-1",
      defaultAuthMode: "apiKey",
      apiKey: "da2-liw5uercxfccrhgzlqhxj3tu2u",
    },
  },
});

const client = generateClient<Schema>();
export default function AdminPage() {
  // const [questionnaires, setQuestionnaires] = useState([]);
  // const [selectedQuestionnaire, setSelectedQuestionnaire] = useState(null);
  // const [newQuestionnaireName, setNewQuestionnaireName] = useState("");

  // const [healthQuestions, setHealthQuestions] = useState([]);
  // const [newQuestion, setNewQuestion] = useState({ prompt: "", category: "" });

  // const [questionnaireQuestions, setQuestionnaireQuestions] = useState([]);
  // const [selectedHealthQuestion, setSelectedHealthQuestion] = useState("");

  // const fetchQuestionnaires = async () => {
  //   try {
  //     const response = await client.models.Questioneer.list();
  //     setQuestionnaires(response.data);
  //   } catch (error) {
  //     console.error("Error fetching questionnaires:", error);
  //   }
  // };

  // const fetchHealthQuestions = async () => {
  //   try {
  //     const response = await client.models.HealthQuestion.list();
  //     setHealthQuestions(response.data);
  //   } catch (error) {
  //     console.error("Error fetching health questions:", error);
  //   }
  // };

  // const fetchQuestionnaireQuestions = async (questionnaireId) => {
  //   try {
  //     const response = await client.models.listQuestioneerQuestionsBySequence(
  //       { questioneerId: questionnaireId },
  //       { sort: "asc" }
  //     );
  //     setQuestionnaireQuestions(response.data);
  //   } catch (error) {
  //     console.error("Error fetching questionnaire questions:", error);
  //   }
  // };

  // useEffect(() => {
  //   fetchQuestionnaires();
  //   fetchHealthQuestions();
  // }, []);

  // useEffect(() => {
  //   if (selectedQuestionnaire) {
  //     fetchQuestionnaireQuestions(selectedQuestionnaire.id);
  //   }
  // }, [selectedQuestionnaire]);

  // const createQuestionnaire = async () => {
  //   if (newQuestionnaireName.trim() === "") return;

  //   try {
  //     await client.models.Questioneer.create({
  //       name: newQuestionnaireName,
  //     });
  //     setNewQuestionnaireName("");
  //     fetchQuestionnaires();
  //   } catch (error) {
  //     console.error("Error creating questionnaire:", error);
  //   }
  // };

  // const createHealthQuestion = async () => {
  //   if (newQuestion.prompt.trim() === "" || newQuestion.category.trim() === "")
  //     return;

  //   try {
  //     await client.models.HealthQuestion.create({
  //       prompt: newQuestion.prompt,
  //       category: newQuestion.category,
  //     });
  //     setNewQuestion({ prompt: "", category: "" });
  //     fetchHealthQuestions();
  //   } catch (error) {
  //     console.error("Error creating health question:", error);
  //   }
  // };

  // const addQuestionToQuestionnaire = async () => {
  //   if (!selectedQuestionnaire || !selectedHealthQuestion) return;

  //   try {

  //     const nextSequence =
  //       questionnaireQuestions.length > 0
  //         ? Math.max(...questionnaireQuestions.map((q) => q.sequence)) + 1
  //         : 1;

  //     await client.models.QuestioneerQuestion.create({
  //       questioneerId: selectedQuestionnaire.id,
  //       healthQuestionId: selectedHealthQuestion,
  //       sequence: nextSequence,
  //     });

  //     fetchQuestionnaireQuestions(selectedQuestionnaire.id);
  //     setSelectedHealthQuestion("");
  //   } catch (error) {
  //     console.error("Error adding question to questionnaire:", error);
  //   }
  // };

  // const reorderQuestion = async (questionId, direction) => {
  //   const currentIndex = questionnaireQuestions.findIndex(
  //     (q) => q.id === questionId
  //   );
  //   if (currentIndex === -1) return;

  //   const targetIndex =
  //     direction === "up" ? currentIndex - 1 : currentIndex + 1;
  //   if (targetIndex < 0 || targetIndex >= questionnaireQuestions.length) return;

  //   try {
  //     const currentQuestion = questionnaireQuestions[currentIndex];
  //     const targetQuestion = questionnaireQuestions[targetIndex];

  //     await client.models.QuestioneerQuestion.update({
  //       id: currentQuestion.id,
  //       sequence: targetQuestion.sequence,
  //     });

  //     await client.models.QuestioneerQuestion.update({
  //       id: targetQuestion.id,
  //       sequence: currentQuestion.sequence,
  //     });

  //     fetchQuestionnaireQuestions(selectedQuestionnaire.id);
  //   } catch (error) {
  //     console.error("Error reordering questions:", error);
  //   }
  // };

  // const removeQuestion = async (questionId) => {
  //   try {
  //     await client.models.QuestioneerQuestion.delete({ id: questionId });
  //     fetchQuestionnaireQuestions(selectedQuestionnaire.id);
  //   } catch (error) {
  //     console.error("Error removing question:", error);
  //   }
  // };

  return (
    <div className="container mx-auto p-6">
      {/* <h1 className="text-2xl font-bold mb-6">Questionnaire Admin Panel</h1>

      <Tabs defaultValue="questionnaires">
        <TabsList className="mb-4">
          <TabsTrigger value="questionnaires">Questionnaires</TabsTrigger>
          <TabsTrigger value="questions">Health Questions</TabsTrigger>
        </TabsList>

        <TabsContent value="questionnaires" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Create Questionnaire</CardTitle>
            </CardHeader>
            <CardContent className="flex gap-4">
              <Input
                placeholder="Questionnaire Name"
                value={newQuestionnaireName}
                onChange={(e) => setNewQuestionnaireName(e.target.value)}
              />
              <Button onClick={createQuestionnaire}>
                <Plus className="mr-2 h-4 w-4" /> Create
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Manage Questionnaires</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Select
                value={selectedQuestionnaire?.id || ""}
                onValueChange={(value) => {
                  const selected = questionnaires.find((q) => q.id === value);
                  setSelectedQuestionnaire(selected);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a questionnaire" />
                </SelectTrigger>
                <SelectContent>
                  {questionnaires.map((q) => (
                    <SelectItem key={q.id} value={q.id}>
                      {q.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedQuestionnaire && (
                <div className="space-y-4 mt-4">
                  <h3 className="text-lg font-medium">
                    Questions in {selectedQuestionnaire.name}
                  </h3>

                  <div className="flex gap-4 mb-4">
                    <Select
                      value={selectedHealthQuestion}
                      onValueChange={setSelectedHealthQuestion}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder="Select a question to add" />
                      </SelectTrigger>
                      <SelectContent>
                        {healthQuestions.map((q) => (
                          <SelectItem key={q.id} value={q.id}>
                            {q.prompt}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button onClick={addQuestionToQuestionnaire}>
                      <Plus className="mr-2 h-4 w-4" /> Add Question
                    </Button>
                  </div>

                  <div className="space-y-2">
                    {questionnaireQuestions.map((q, index) => {
                      const healthQuestion = healthQuestions.find(
                        (h) => h.id === q.healthQuestionId
                      );
                      return (
                        <div
                          key={q.id}
                          className="flex items-center gap-2 p-3 border rounded-md"
                        >
                          <span className="font-medium mr-2">
                            {q.sequence}.
                          </span>
                          <span className="flex-1">
                            {healthQuestion?.prompt || "Unknown Question"}
                          </span>
                          <div className="flex gap-2">
                            <Button
                              size="icon"
                              variant="outline"
                              onClick={() => reorderQuestion(q.id, "up")}
                              disabled={index === 0}
                            >
                              <MoveUp className="h-4 w-4" />
                            </Button>
                            <Button
                              size="icon"
                              variant="outline"
                              onClick={() => reorderQuestion(q.id, "down")}
                              disabled={
                                index === questionnaireQuestions.length - 1
                              }
                            >
                              <MoveDown className="h-4 w-4" />
                            </Button>
                            <Button
                              size="icon"
                              variant="destructive"
                              onClick={() => removeQuestion(q.id)}
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                    {questionnaireQuestions.length === 0 && (
                      <p className="text-muted-foreground">
                        No questions added to this questionnaire yet.
                      </p>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Create Health Question</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Input
                placeholder="Question Prompt"
                value={newQuestion.prompt}
                onChange={(e) =>
                  setNewQuestion({ ...newQuestion, prompt: e.target.value })
                }
              />
              <Input
                placeholder="Category"
                value={newQuestion.category}
                onChange={(e) =>
                  setNewQuestion({ ...newQuestion, category: e.target.value })
                }
              />
              <Button onClick={createHealthQuestion}>
                <Plus className="mr-2 h-4 w-4" /> Create Question
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Health Questions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {healthQuestions.map((question) => (
                  <div
                    key={question.id}
                    className="flex items-center justify-between p-3 border rounded-md"
                  >
                    <div>
                      <p className="font-medium">{question.prompt}</p>
                      <p className="text-sm text-muted-foreground">
                        Category: {question.category}
                      </p>
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <Edit className="h-4 w-4 mr-2" /> Edit
                      </Button>
                    </div>
                  </div>
                ))}
                {healthQuestions.length === 0 && (
                  <p className="text-muted-foreground">
                    No health questions created yet.
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs> */}
    </div>
  );
}
