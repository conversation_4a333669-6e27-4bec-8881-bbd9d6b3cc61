export default function PrivacyPolicy() {
    return (
        <main className="min-h-screen flex flex-col justify-center items-center bg-white text-black p-6">
            <h1 className="text-4xl font-bold mb-6">Privacy Policy</h1>
            <p className="text-gray-800 mb-4">Effective Date: 07.03.2025</p>
            <p className="text-gray-800 mb-4">
                JiniX respects your privacy and is committed to protecting it. This Privacy Policy describes how we collect, use, and disclose information when you use our app.
            </p>
            <h2 className="text-2xl font-semibold mb-4">Information We Collect</h2>
            <p className="text-gray-800 mb-4">
                Facebook Profile Information (name, email, profile picture, etc.)<br />
                Other data you provide directly to us
            </p>
            <h2 className="text-2xl font-semibold mb-4">How We Use Information</h2>
            <p className="text-gray-800 mb-4">
                To provide and improve our services<br />
                To personalize your experience<br />
                To comply with legal obligations
            </p>
            <h2 className="text-2xl font-semibold mb-4">Data Sharing</h2>
            <p className="text-gray-800 mb-4">
                We do not sell or rent your data. We may share data with service providers to help us operate the app.
            </p>
            <h2 className="text-2xl font-semibold mb-4">Your Rights</h2>
            <p className="text-gray-800 mb-4">
                You may request access, correction, or deletion of your data by contacting <NAME_EMAIL>.
            </p>
            <h2 className="text-2xl font-semibold mb-4">Contact Us</h2>
            <p className="text-gray-800">
                If you have any questions, please contact <NAME_EMAIL>.
            </p>
        </main>
    );
}