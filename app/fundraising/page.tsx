import Image from "next/image"
import Link from "next/link"
import { DonationForm } from "@/components/donation-form"

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <header className="border-b ">
        <div className="px-10 flex items-center justify-between  h-[128px]">
          <Link href="/" className="flex items-center">
            <Image src="/SAM9L.svg" alt="SAMSOL Logo" width={173} height={56} className="h-[72px] w-[188]" />
          </Link>
          <nav className="hidden md:flex items-center space-x-6 h-[38px]">
            <Link href="/" className="text-sm font-medium text-gray-700 hover:text-gray-900">
              Home
            </Link>
            <Link href="/about" className="text-sm font-medium text-gray-700 hover:text-gray-900">
              About Us
            </Link>
            <Link href="/what-is-samsol" className="text-sm font-medium text-gray-700 hover:text-gray-900">
              What is SAMSOL
            </Link>
            <Link href="/contact" className="text-sm font-medium text-gray-700 hover:text-gray-900">
              Contact Us
            </Link>
            <Link
              href="/donate"
              className="rounded-full bg-orange-400 px-4 py-2 text-sm font-medium text-black hover:bg-orange-500"
            >
              Donate Now
              <span className="ml-2 ">→</span>
            </Link>
          </nav>
          <button className="block md:hidden">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-6 w-6"
            >
              <line x1="4" x2="20" y1="12" y2="12" />
              <line x1="4" x2="20" y1="6" y2="6" />
              <line x1="4" x2="20" y1="18" y2="18" />
            </svg>
          </button>
        </div>
      </header>

      <main className="flex-1">
        <div className="container mx-auto  px-4 pt-[84px]">
          <div className="text-center mb-6">
            <h2 className="text-sm font-medium text-[#0569A0] bg-[#F0F9FF] rounded-xl mx-auto w-32 text-center px-4 py-1">Donate Now</h2>
            <h1 className="pt-2 text-[56px] font-bold tracking-tight text-gray-900">Fuel the Mission</h1>
            <p className="mt-2 text-center text-gray-500 text-lg max-w-4xl mx-auto">
              Your support fuels the mission of The SAMD9L Foundation, driving research, patient support, and advocacy efforts.
              Whether through donations or active involvement, every contribution makes a difference.
            </p>
          </div>
          <div className="max-w-[712px] mx-auto ">
          <DonationForm />
          </div>
          
        </div>

        <div className="relative mt-[112px] px-2 ">
          <div className="relative h-[634px]  w-full overflow-hidden rounded-2xl ">
            <Image src="/qwerty.jpeg" alt="Medical care image" fill className="object-cover" />
            <div className="absolute inset-0 bg-black/30"></div>
            <div className="absolute inset-0 flex items-center mt-56 px-8 md:px-16">
              <div className="text-6xl md:text-7xl  text-white">
                <h2 >
                  Drive the
                </h2>
                <span className="text-orange-400 mr-4"> Change </span>
                <span className="mr-4">with </span>
                <span>Us</span>
              </div>
            </div>
          </div>
        </div>
      </main>

      <footer className="border-t py-[60px]">
        <div className="container mx-auto px-4">
          <div className="flex flex-col items-center justify-center space-y-4">
            <Image src="/SAM9L.svg" alt="SAMSOL Logo" width={153} height={50}  />
            <div className="flex space-x-4 text-sm">
              <Link href="/" className="text-gray-500 hover:text-gray-700">
                Home
              </Link>
              <Link href="/donate" className="text-gray-500 hover:text-gray-700">
                Donate Now
              </Link>
            </div>
            <p className="text-xs text-gray-400">Copyright © 2025 The SAMSOL Foundation</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
