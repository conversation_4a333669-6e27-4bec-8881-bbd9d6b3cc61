"use client";

import { useRouter } from "next/navigation";
import Image from "next/image";
import SymptomContent from "@/app/(protected)/medical-record/components/SymptomContent";
// import { useMedicalRecord } from "@/hooks/useMedicalRecord";
import { useEffect, useState } from "react";
import { OneUpHealthService, MedicalRecord } from "@/lib/services/oneUpHealthService";
import { useUser } from "@/hooks/useUsers";

export default function SymptomDetails() {
  const router = useRouter();
  // const { selectedRecord, records: rawRecords } = useMedicalRecord();
  // const records = Array.isArray(rawRecords) ? rawRecords : [];

  // TEMP: Directly fetch records for debug
  const { currentUser } = useUser();
  const [records, setRecords] = useState<MedicalRecord[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchRecords() {
      if (!currentUser) return;
      const service = new OneUpHealthService({ userId: currentUser.userId });
      try {
        const recs = await service.fetchMedicalRecordsFromDatabase();
        setRecords(recs);
      } catch (e) {
        setRecords([]);
      } finally {
        setLoading(false);
      }
    }
    fetchRecords();
  }, [currentUser]);


  const symptoms = records.filter((r) => r.type === "Symptom");

  const handleSymptomClick = (id: string) => {
    router.push(`/symptom/${id}`);
  };

  // Format date as "day month year"
  const formatDate = (dateString: string) => {
    if (!dateString) return "-";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    return date.toLocaleDateString(undefined, {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF]">
      {/* Header with back button and title */}
      <div className="flex items-center justify-between pt-[51px] pl-6 pr-6 py-4">
        <div className="flex items-center">
          <Image
            src="/chevron-left.svg"
            alt="Back"
            height={24}
            width={24}
            className="cursor-pointer"
            onClick={() => router.back()}
          />
          <h1 className="ml-4 text-[16px] font-medium">Symptom</h1>
        </div>

        {/* Add Symptom button */}
        <button
          className="flex items-center gap-1 bg-white px-4 py-2 h-[40px] w-auto rounded-full shadow-md"
          onClick={() => router.push("/add-symptom")}
        >
          <Image
            src="/plus.svg"
            alt="Plus"
            width={20}
            height={20}
            className="w-5 h-5 flex-shrink-0"
          />
          <span className="text-[14px] font-medium">Symptom</span>
        </button>
      </div>

      <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto">
        {/* Display symptom content if record is available */}
        {/* {selectedRecord && selectedRecord.type === "Symptom" && (
          <div className="bg-white rounded-3xl p-4 border border-gray-100 shadow-sm mb-6">
            <SymptomContent record={selectedRecord} />
          </div>
        )} */}

        {/* Timeline with real symptom entries */}
        <div className="space-y-8">
          {loading ? (
            <div className="text-center text-gray-400 py-8">
              Loading records...
            </div>
          ) : (
            <>
              {records.length > 0 && symptoms.length === 0 && (
                <div className="text-center text-gray-400 py-8">
                  No symptom records found (but other records exist).
                </div>
              )}
              {symptoms.map((symptom, index) => (
                <div key={symptom.id} className="relative">
                  {/* Timeline line for all but the last item */}
                  {index < symptoms.length - 1 && (
                    <div
                      className="absolute top-4 left-1.5 w-[1px] h-full bg-gray-300"
                      style={{ transform: "translateX(-50%)" }}
                    ></div>
                  )}

                  {/* Timeline dot */}
                  <div className="absolute top-1.5 left-0 w-3 h-3 bg-white rounded-full border border-gray-300 z-10"></div>

                  {/* Date */}
                  <div className="ml-6 text-sm text-[#4285F4] bg-[#EDF2FD] rounded-full px-3 py-1 inline-block">
                    {formatDate(symptom.date)}
                  </div>

                  {/* Використовуй SymptomContent для картки симптома */}
                  <div
                    className="ml-6 mt-2 cursor-pointer"
                    onClick={() => handleSymptomClick(symptom.id)}
                  >
                    <SymptomContent record={symptom} />
                  </div>
                </div>
              ))}
              {records.length === 0 && (
                <div className="text-center text-gray-400 py-8">
                  No medical records found.
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
