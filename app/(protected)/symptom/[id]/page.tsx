'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { OneUpHealthService, MedicalRecord } from '@/lib/services/oneUpHealthService';
import { useUser } from '@/hooks/useUsers';

export default function SymptomDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const { currentUser } = useUser();
  const [symptom, setSymptom] = useState<MedicalRecord | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchSymptom() {
      if (!currentUser) return;
      const service = new OneUpHealthService({ userId: currentUser.userId });
      try {
        const records = await service.fetchMedicalRecordsFromDatabase();
        const found = records.find(
          (r) => r.type === "Symptom" && r.id === params.id
        );
        setSymptom(found || null);
      } catch {
        setSymptom(null);
      } finally {
        setLoading(false);
      }
    }
    fetchSymptom();
  }, [currentUser, params.id]);

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF]">
      {/* Header with back button and title */}
      <div className="flex items-center pt-[51px] pl-6 py-4">
        <Image
          src="/chevron-left.svg"
          alt="Back"
          height={24}
          width={24}
          className="cursor-pointer"
          onClick={() => router.back()}
        />
        <div className="ml-4 flex items-center">
          <h1 className="text-[14px] font-normal mr-2">Symptom Report</h1>
          <p className="text-[14px] text-gray-500">
            {symptom ? new Date(symptom.date).toLocaleDateString(undefined, {
              year: "numeric",
              month: "long",
              day: "numeric",
            }) : ""}
          </p>
        </div>
      </div>

      <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto space-y-4">
        {loading ? (
          <div className="flex flex-col items-center justify-center h-[200px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-500">Loading...</p>
          </div>
        ) : symptom ? (
          <div className="bg-white rounded-3xl p-4 border border-gray-100 shadow-sm">
            <div className="flex items-center mb-3">
              <div className="text-[#FF4901] mr-2">
                <Image
                  src="/outpatient/heart-rate-monitor.svg"
                  alt="Symptom"
                  width={24}
                  height={24}
                  className="w-6 h-6"
                />
              </div>
              <h2 className="text-[#FF4901] font-medium">Symptom Recording</h2>
            </div>
            <p className="text-gray-700 text-[14px]">
              {symptom.description}
            </p>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-[200px]">
            <p className="text-gray-500">Symptom not found</p>
          </div>
        )}
      </div>
    </div>
  );
}
