
"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { fetchAuthSession } from "@aws-amplify/auth";
import { useWindowSize } from "@/hooks/useWindowSize";
import { Schema } from "@/amplify/data/resource";
import { generateClient } from "aws-amplify/api";
import { GraphQLResult } from '@aws-amplify/api';
import { useRouter } from "next/navigation";

import MobileHeader from "@/components/discovery/MobileHeader";
import Moment from "@/components/discovery/Moment";
import CommunitySection from "@/components/discovery/CommunitySection";
import DiscoverySidebar from "@/components/discovery/DiscoverySidebar";

type TransformedPost = {
  id: string;
  userName: string;
  groupTitle: string;
  groupText: string;
  avatar: string;
  content: string;
  title: string;
  text: string;
  image: string | null;
  imageGrid?: string[];
  likes: number;
  comments: number;
  timeAgo: string;
  onCommentAdd: () => void;
  isSaved?: boolean;
  onBookmarkChange?: () => void;
};

type SavedPostItem = {
  postId: string;
  post: {
    id: string;
    userId: string;
    userName: string;
    title: string;
    content: string;
    images: string[];
    likes: number;
    commentsCount: number;
    createdAt: string;
  };
};

type SavedPostsResponse = {
  listSavedPostsByUser: {
    items: SavedPostItem[];
  };
};

type Community = {
  id: string;
  name: string;
  description: string;
  members: number;
  avatar: string;
};

type CommunityMembership = {
  communityId: string;
};

type UserCommunitiesResponse = {
  listCommunityMembershipsByUser: {
    items: CommunityMembership[];
  };
};

type PostListResponse = {
  listPosts: {
    items: any[];
    nextToken: string | null;
  };
};

export default function Discovery() {
  const [activeTab, setActiveTab] = useState("moments");
  const [currentUser, setCurrentUser] = useState<{ email: string } | null>(null);
  const [posts, setPosts] = useState<any[]>([]);
  const [savedPosts, setSavedPosts] = useState<any[]>([]);
  const [savedPostIds, setSavedPostIds] = useState<Set<string>>(new Set());
  const [allCommunities, setAllCommunities] = useState<Community[]>([]);
  const [userCommunities, setUserCommunities] = useState<Community[]>([]);
  const [userCommunityIds, setUserCommunityIds] = useState<Set<string>>(new Set());
  const [isLoadingCommunities, setIsLoadingCommunities] = useState(true);
  const [isLoadingPosts, setIsLoadingPosts] = useState(false);
  const [nextToken, setNextToken] = useState<string | null>(null);
  const isDesktop = useWindowSize();
  const client = generateClient<Schema>();
  const router = useRouter();
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.email) {
          const email = String(session.tokens.idToken.payload.email);
          setCurrentUser({ email });
          fetchSavedPosts(email);
          fetchUserCommunities(email);
        } else {
          setCurrentUser({ email: "<EMAIL>" });
        }
      } catch (error) {
        console.error("Error fetching user:", error);
        setCurrentUser({ email: "<EMAIL>" });
      }
    };
    fetchUser();
  }, []);

  const fetchPosts = async (token: string | null = null, isInitial: boolean = false) => {
    if (isLoadingPosts) return;
    setIsLoadingPosts(true);
    try {
      const response = await client.graphql<PostListResponse>({
        query: `
          query ListPosts($limit: Int, $nextToken: String) {
            listPosts(limit: $limit, nextToken: $nextToken) {
              items {
                id
                userId
                userName
                title
                content
                images
                likes
                commentsCount
                createdAt
              }
              nextToken
            }
          }
        `,
        variables: {
          limit: isInitial ? 3 : 10,
          nextToken: token
        }
      }) as GraphQLResult<PostListResponse>;

      console.log("fetchPosts: Response nextToken =", response.data?.listPosts.nextToken);
      const newPosts = response.data?.listPosts.items || [];
      setPosts(prev => token ? [...prev, ...newPosts] : newPosts);
      setNextToken(response.data?.listPosts.nextToken || null);
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setIsLoadingPosts(false);
    }
  };

  const fetchSavedPosts = async (userId: string) => {
    try {
      const result = await client.graphql<SavedPostsResponse>({
        query: `
          query GetSavedPostsForUser($userId: String!) {
            listSavedPostsByUser(userId: $userId) {
              items {
                postId
                post {
                  id
                  userId
                  userName
                  title
                  content
                  images
                  likes
                  commentsCount
                  createdAt
                }
              }
            }
          }
        `,
        variables: { userId }
      }) as GraphQLResult<SavedPostsResponse>;

      const savedPosts = result.data?.listSavedPostsByUser?.items
        .map(item => item.post)
        .filter(post => post !== null);
      const newSavedPostIds = new Set(result.data?.listSavedPostsByUser?.items
        .map(item => item.postId));
      setSavedPostIds(newSavedPostIds);
      setSavedPosts(savedPosts || []);
    } catch (error) {
      console.error("Error fetching saved posts:", error);
      setSavedPosts([]);
      setSavedPostIds(new Set());
    }
  };

  const fetchUserCommunities = async (userId: string) => {
    try {
      const result = await client.graphql<UserCommunitiesResponse>({
        query: `
          query GetUserCommunities($userId: String!) {
            listCommunityMembershipsByUser(userId: $userId) {
              items {
                communityId
              }
            }
          }
        `,
        variables: { userId }
      }) as GraphQLResult<UserCommunitiesResponse>;

      const communityIds = new Set(
        result.data?.listCommunityMembershipsByUser?.items
          ?.map(item => item.communityId) || []
      );
      setUserCommunityIds(communityIds);
      fetchAllCommunities(communityIds);
    } catch (error) {
      console.error("Error fetching user communities:", error);
      setUserCommunityIds(new Set());
    }
  };

  const fetchAllCommunities = async (userCommunityIds?: Set<string>) => {
    setIsLoadingCommunities(true);
    try {
      const result = await client.models.Community.list();
      if (result.data) {
        const mappedCommunities = result.data.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description || "",
          members: item.membersCount || 0,
          avatar: item.icon || "/groupavatar.svg"
        }));
        setAllCommunities(mappedCommunities);
        if (userCommunityIds && userCommunityIds.size > 0) {
          const filteredCommunities = mappedCommunities.filter(
            community => userCommunityIds.has(community.id)
          );
          setUserCommunities(filteredCommunities);
        } else {
          setUserCommunities([]);
        }
      }
    } catch (error) {
      console.error("Error fetching communities:", error);
    } finally {
      setIsLoadingCommunities(false);
    }
  };

  const loadMorePosts = useCallback(() => {
    if (nextToken && !isLoadingPosts) {
      console.log("loadMorePosts: Fetching with nextToken =", nextToken);
      fetchPosts(nextToken);
    }
  }, [nextToken, isLoadingPosts]);

  useEffect(() => {
    if (currentUser?.email) {
      fetchPosts(null, true);
      fetchSavedPosts(currentUser.email);
      fetchUserCommunities(currentUser.email);
    }
  }, [currentUser?.email]);

  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && nextToken && !isLoadingPosts) {
          console.log("Desktop: Triggering loadMorePosts");
          loadMorePosts();
        }
      },
      { threshold: 0.5 }
    );

    if (loadMoreRef.current) {
      console.log("Desktop: Observing loadMoreRef");
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current && loadMoreRef.current) {
        observerRef.current.unobserve(loadMoreRef.current);
      }
    };
  }, [nextToken, isLoadingPosts, loadMorePosts]);

  const handleCommentAdd = async (postId: string) => {
    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      await client.models.Post.update({
        id: postId,
        commentsCount: (post.commentsCount || 0) + 1
      });

      setPosts(currentPosts =>
        currentPosts.map(p =>
          p.id === postId
            ? { ...p, commentsCount: (post.commentsCount || 0) + 1 }
            : p
        )
      );
    } catch (error) {
      console.error("Error updating comment count:", error);
    }
  };

  const transformPost = (post: any, forceSaved = false) => {
    if (!post) return null;

    const userName = post.userName || (post.userId ? post.userId.split('@')[0] : 'Anonymous');
    const isSaved = forceSaved || savedPostIds.has(post.id);

    return {
      id: post.id || '',
      userName,
      groupTitle: post.title || '',
      groupText: post.createdAt ? new Date(post.createdAt).toLocaleDateString() : '',
      avatar: "/placeholder.svg?height=40&width=40",
      content: post.content || '',
      title: post.title || '',
      text: post.title || '',
      image: post.images?.length > 0 ? post.images[0] : null,
      imageGrid: post.images?.length > 1 ? post.images : undefined,
      likes: post.likes || 0,
      comments: post.commentsCount || 0,
      timeAgo: post.createdAt ? new Date(post.createdAt).toLocaleString() : '',
      onCommentAdd: () => handleCommentAdd(post.id),
      isSaved,
      onBookmarkChange: () => {
        if (currentUser?.email) {
          fetchSavedPosts(currentUser.email);
        }
      }
    } as TransformedPost;
  };

  const transformedPosts = posts
    .map(post => transformPost(post))
    .filter((post): post is TransformedPost => post !== null);

  const transformedSavedPosts = savedPosts
    .map(post => transformPost(post, true))
    .filter((post): post is TransformedPost => post !== null);

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    if (currentUser?.email && tab === "moments") {
      setPosts([]);
      setNextToken(null);
      fetchPosts(null, true);
    }
    if (tab === "community" && currentUser?.email) {
      fetchUserCommunities(currentUser.email);
    }
  };

  return (
    <div className="min-h-screen bg-white flex">
      <DiscoverySidebar communities={userCommunities} />
      
      <div className="flex-1 relative">
        <MobileHeader
          title="Discovery"
          tabs={[
            { id: "moments", label: "Moments" },
            { id: "community", label: "Community" }
          ]}
          activeTab={activeTab}
          onTabChange={handleTabChange}
          posts={transformedPosts}
          loadMorePosts={loadMorePosts}
          isLoadingPosts={isLoadingPosts}
          nextToken={nextToken}
        />

        <div className="flex flex-col pb-20 md:flex-row md:ml-0 lg:ml-0 md:mt-0 overflow-auto md:h-full h-[calc(100vh-120px)]">
          <div className="flex-1 mt-5 overflow-auto h-screen">
            <div className={`${activeTab === "moments" ? "block" : "hidden md:block"} px-5`}>
              {isDesktop && transformedPosts.map((post) => (
                <div className="py-5" key={post.id}>
                  <Moment 
                    moment={post} 
                    onCommentAdd={() => handleCommentAdd(post.id)}
                    onBookmarkChange={post.onBookmarkChange}
                  />
                </div>
              ))}
              {isDesktop && posts.length > 0 && (
                <div ref={loadMoreRef} className="h-10">
                  {isLoadingPosts && (
                    <div className="text-center py-4">Loading more posts...</div>
                  )}
                  {!isLoadingPosts && !nextToken && (
                    <div className="text-center py-4"></div>
                  )}
                </div>
              )}
            </div>

            <div className={`${activeTab === "community" ? "block" : "hidden"} md:hidden px-5`}>
              <CommunitySection 
                communities={allCommunities} 
                isLoading={isLoadingCommunities} 
                onRefresh={() => fetchAllCommunities(userCommunityIds)} 
              />
            </div>
          </div>

          <div className="hidden md:block md:ml-4 md:w-[200px] lg:w-[280px] xl:w-[300px] 2xl:w-[320px] md:mr-2 lg:mr-4 md:flex-shrink-0">
            <div className="bg-gray-100 rounded-2xl mt-6 w-full">
              <div className="flex justify-between items-center mb-4">
                <h2 className="font-medium text-gray-800 p-3 md:p-4">Recommended group</h2>
              </div>
              <div className="space-y-3 md:space-y-4 pb-4 md:pb-6">
                <CommunitySection 
                  compact 
                  communities={allCommunities} 
                  isLoading={isLoadingCommunities} 
                  onRefresh={() => fetchAllCommunities(userCommunityIds)} 
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
