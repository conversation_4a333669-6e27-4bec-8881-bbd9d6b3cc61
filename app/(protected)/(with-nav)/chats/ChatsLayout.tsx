"use client";

import React, { useEffect, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { fetchAuthSession } from "aws-amplify/auth";
import { motion, AnimatePresence } from "framer-motion";
import {
  ViewStateProvider,
  useViewState,
} from "@/components/Chat/context/ViewStateContext";
import {
  AllChatsProvider,
  useAllChats,
} from "@/components/Chat/context/AllChatsContext";
import { ChatContextAdapter } from "@/components/Chat/context/ChatContextAdapter";
import { useMobile } from "@/hooks/use-mobile";
import { ChatLayout } from "@/components/Chat/layout/ChatLayout";
import { ChatSidebar } from "@/components/Chat/layout/ChatSidebar";
import { MainContentArea } from "@/components/Chat/layout/MainContentArea";
import { DetailsPanel } from "@/components/Chat/layout/DetailsPanel";

import { ChatDetailsContainer } from "@/components/Chat/containers/ChatDetailsContainer";
import { NewChatContainer } from "@/components/Chat/containers/NewChatContainer";
import { ConfirmNewGroupChatContainer } from "@/components/Chat/containers/ConfirmNewGroupChatContainer";

import { InlineChatDetails } from "@/components/Chat/details/InlineChatDetails";
import { UserDetailsContainer } from "@/components/Chat/containers/UserDetailsContainer";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { ChatList } from "@/components/Chats/ChatList";
import { ChatActionsMenu } from "@/components/Chats/ActionMenu";

import MobileHeader from "@/components/Chat/MobileHeader";
import NewChatHeader from "@/components/NewChat/NewChatHeader.new";
import { ChatHeaderWrapper } from "@/components/Chat/headers/ChatHeaderWrapper";
import { ChatDetailsMobile } from "@/components/Chat/details/ChatDetailsMobile";
import { Chat } from "@/types/chat";
import {
  parseChatPathname,
  getViewStateFromPath,
  isValidChatId,
  ChatPaths,
  NavigationUtils,
  DetailsPanelUtils,
} from "@/utils/pathHelpers";

const client = generateClient<Schema>();

const MainContent = React.memo(
  ({
    isMobile,
    activeView,
    chatId,
    children,
    currentChat,
    handleLeaveGroup,
  }: {
    isMobile: boolean;
    activeView: string;
    chatId: string | null;
    children: React.ReactNode;
    currentChat: Chat | null;
    handleLeaveGroup: () => Promise<void>;
  }) => {
    const contentVariants = React.useMemo(
      () => ({
        hidden: {
          opacity: 0,
          transform: "translateY(8px)",
        },
        visible: {
          opacity: 1,
          transform: "translateY(0px)",
          transition: {
            duration: 0.2,
            ease: [0.4, 0, 0.2, 1],
          },
        },
        exit: {
          opacity: 0,
          transform: "translateY(-8px)",
          transition: { duration: 0.15 },
        },
      }),
      []
    );

    if (isMobile && activeView === "CHAT_LIST") {
      return (
        <motion.div
          variants={contentVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="h-full w-full"
        >
          <ChatList />
        </motion.div>
      );
    }

    const shouldChildrenTakeFlex =
      chatId &&
      activeView !== "CHAT_LIST" &&
      activeView !== "CHAT_DETAILS" &&
      activeView !== "NEW_CHAT" &&
      activeView !== "USER_DETAILS" &&
      activeView !== "CONFIRM_GROUP_CHAT";

    return (
      <div className="h-full w-full flex flex-col">
        <AnimatePresence mode="wait">
          {children && shouldChildrenTakeFlex && (
            <motion.div
              key={`children-${chatId || "no-chat"}`}
              variants={contentVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="flex-1 min-h-0"
            >
              {children}
            </motion.div>
          )}
        </AnimatePresence>

        {isMobile && currentChat && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2 }}
          >
            <InlineChatDetails
              currentChat={currentChat}
              handleLeaveGroup={handleLeaveGroup}
            />
          </motion.div>
        )}

        <AnimatePresence mode="wait">
          {!chatId && activeView === "NEW_CHAT" && (
            <motion.div
              key="new-chat-container"
              variants={contentVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="flex-1 min-h-0"
            >
              <NewChatContainer />
            </motion.div>
          )}

          {!chatId && activeView === "CONFIRM_GROUP_CHAT" && (
            <motion.div
              key="confirm-group-chat-container"
              variants={contentVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              className="flex-1 min-h-0"
            >
              <ConfirmNewGroupChatContainer />
            </motion.div>
          )}

          {!chatId &&
            activeView !== "NEW_CHAT" &&
            activeView !== "CONFIRM_GROUP_CHAT" && (
              <motion.div
                key="empty-state"
                className="h-full w-full flex items-center justify-center"
                variants={contentVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                {children}
              </motion.div>
            )}
        </AnimatePresence>
      </div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.isMobile === nextProps.isMobile &&
      prevProps.activeView === nextProps.activeView &&
      prevProps.chatId === nextProps.chatId &&
      prevProps.currentChat?.id === nextProps.currentChat?.id &&
      prevProps.children === nextProps.children
    );
  }
);

MainContent.displayName = "MainContent";

export default function ChatsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <AllChatsProvider>
      <ViewStateProvider>
        <ChatContextAdapter>
          <ChatsLayoutContent>{children}</ChatsLayoutContent>
        </ChatContextAdapter>
      </ViewStateProvider>
    </AllChatsProvider>
  );
}

function ChatsLayoutContent({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const isMobile = useMobile();
  const isDesktop = !isMobile;
  const { removeChat } = useAllChats();

  const {
    activeView,
    setActiveView,
    currentChat,
    setCurrentChat,
    selectedUserDetail,
    setSelectedUserDetail,
  } = useViewState();

  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [showAllMembers, setShowAllMembers] = useState(false);

  const parsedPath = React.useMemo(() => {
    const parsed = parseChatPathname(pathname);
    return parsed;
  }, [pathname]);

  const chatId = React.useMemo(() => {
    const extractedChatId = parsedPath.chatId;
    const isValid = isValidChatId(extractedChatId);
    return isValid ? extractedChatId : null;
  }, [parsedPath.chatId]);

  React.useEffect(() => {
    let mounted = true;

    const getCurrentUserId = async () => {
      try {
        const session = await fetchAuthSession();
        if (mounted && session.tokens?.idToken?.payload?.sub) {
          const userId = String(session.tokens.idToken.payload.sub);
          setCurrentUserId(userId);
        }
      } catch (error) {
        console.error("Error fetching current user:", error);
      }
    };

    getCurrentUserId();
    return () => {
      mounted = false;
    };
  }, []);

  React.useEffect(() => {
    if (
      activeView === "CONFIRM_GROUP_CHAT" &&
      parsedPath.isConfirmGroupChatRoute
    ) {
      return;
    }

    const newViewState = getViewStateFromPath(
      parsedPath,
      activeView,
      selectedUserDetail
    );

    if (newViewState !== activeView) {
      Promise.resolve().then(() => {
        setActiveView(newViewState);
      });
    }
  }, [parsedPath, selectedUserDetail, setActiveView, activeView]);

  React.useEffect(() => {
    if (chatId && !selectedUserDetail) {
      setShowAllMembers(false);
    }
  }, [chatId, selectedUserDetail]);

  React.useEffect(() => {
    if (parsedPath.isDetailsRoute && activeView !== "CHAT_DETAILS") {
      Promise.resolve().then(() => {
        setActiveView("CHAT_DETAILS");
      });
    } else if (
      !parsedPath.isDetailsRoute &&
      activeView === "CHAT_DETAILS" &&
      chatId
    ) {
      Promise.resolve().then(() => {
        setActiveView("CHAT_CONVERSATION");
      });
    }
  }, [parsedPath, activeView, chatId, setActiveView]);

  const handleLeaveGroup = React.useCallback(async () => {
    if (!chatId || !currentUserId) {
      console.error("Missing required information");
      return;
    }

    try {
      await client.models.ChatParticipant.delete({
        chatId,
        userId: currentUserId,
      });

      setCurrentChat(null);
      removeChat(chatId);
      router.push(ChatPaths.chatList());
      router.refresh();
    } catch (err) {
      console.error("Error leaving group:", err);
    }
  }, [chatId, currentUserId, setCurrentChat, removeChat, router]);

  const goToChatDetails = React.useCallback(() => {
    if (!chatId) return;

    DetailsPanelUtils.syncDetailsWithRoute(router, chatId as string, activeView, true);
    setActiveView("CHAT_DETAILS");
  }, [chatId, router, activeView, setActiveView]);

  const renderDetailsPanel = React.useCallback(() => {
    const detailsContent = DetailsPanelUtils.getDetailsPanelContent(
      parsedPath,
      activeView,
      selectedUserDetail
    );

    const panelVariants = {
      hidden: {
        opacity: 0,
        transform: "translateX(16px)",
      },
      visible: {
        opacity: 1,
        transform: "translateX(0px)",
        transition: {
          duration: 0.2,
          ease: [0.4, 0, 0.2, 1],
        },
      },
      exit: {
        opacity: 0,
        transform: "translateX(16px)",
        transition: { duration: 0.15 },
      },
    };

    return (
      <AnimatePresence mode="wait">
        {(() => {
          switch (detailsContent) {
            case "user-details":
              return (
                <motion.div
                  key="user-details"
                  variants={panelVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                >
                  <UserDetailsContainer />
                </motion.div>
              );

            case "chat-details":
              return (
                <motion.div
                  key="chat-details"
                  variants={panelVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                >
                  <ChatDetailsContainer
                    currentChat={currentChat}
                    handleLeaveGroup={handleLeaveGroup}
                  />
                </motion.div>
              );

            case "new-chat":
              if (isDesktop) {
                return (
                  <motion.div
                    key="new-chat-desktop"
                    variants={panelVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                  >
                    <NewChatContainer />
                  </motion.div>
                );
              }
              return null;

            default:
              return null;
          }
        })()}
      </AnimatePresence>
    );
  }, [
    parsedPath,
    activeView,
    selectedUserDetail,
    currentChat,
    isDesktop,
    handleLeaveGroup,
  ]);

  const getMobilePanelHeight = React.useCallback(() => {
    return activeView === "NEW_CHAT" ? "compact" : "standard";
  }, [activeView]);

  const getHeaderContent = React.useCallback(() => {
    if (!isMobile) {
      return (
        <ChatHeaderWrapper
          handleLeaveGroup={handleLeaveGroup}
          onGroupDetailsClick={goToChatDetails}
        />
      );
    }

    if (activeView === "CHAT_LIST") {
      return (
        <MobileHeader
          showBackButton={false}
          leftContent={
            <div className="text-[24px] font-semibold text-[#171717]">
              Chats
            </div>
          }
          rightContent={
            <div className="flex items-center">
              <div className="p-[2px] bg-white rounded-2xl shadow-lg">
                <ChatActionsMenu />
              </div>
            </div>
          }
        />
      );
    } else if (activeView === "CHAT_DETAILS") {
      return (
        <MobileHeader
          showBackButton={true}
          onBackClick={() => {
            DetailsPanelUtils.handleDetailsBack(
              router,
              pathname || '',
              chatId,
              setActiveView
            );
          }}
          leftContent={
            <div className="text-[18px] font-semibold text-[#171717]">
              Details
            </div>
          }
        />
      );
    } else if (activeView === "USER_DETAILS") {
      return (
        <MobileHeader
          showBackButton={true}
          onBackClick={() => {
            setSelectedUserDetail(null);
            NavigationUtils.handleMobileBack(router, pathname || '', chatId );
          }}
          leftContent={
            <div className="text-[18px] font-semibold text-[#171717]">
              User Details
            </div>
          }
        />
      );
    } else if (activeView === "NEW_CHAT") {
      return (
        <NewChatHeader
          onClose={() => {
            router.push(ChatPaths.chatList());
          }}
        />
      );
    } else if (activeView === "CONFIRM_GROUP_CHAT") {
      return (
        <MobileHeader
          showBackButton={true}
          onBackClick={() => {
            NavigationUtils.handleMobileBack(
              router,
              pathname || '',
              chatId,
              setActiveView
            );
          }}
          leftContent={
            <div className="text-[18px] font-semibold text-[#171717]">
              Set Group Name
            </div>
          }
        />
      );
    } else {
      return (
        <ChatHeaderWrapper
          handleLeaveGroup={handleLeaveGroup}
          onGroupDetailsClick={goToChatDetails}
        />
      );
    }
  }, [
    isMobile,
    activeView,
    chatId,
    handleLeaveGroup,
    goToChatDetails,
    router,
    pathname,
    setActiveView,
    setSelectedUserDetail,
  ]);

  return (
    <div className="flex flex-col md:flex-row h-full w-full overflow-hidden">
      <ChatLayout
        sidebar={<ChatSidebar>{isDesktop && <ChatList />}</ChatSidebar>}
        detailsPanel={
          <DetailsPanel mobilePanelHeight={getMobilePanelHeight()}>
            {renderDetailsPanel()}
          </DetailsPanel>
        }
      >
        <div className="flex flex-col h-full">
          <AnimatePresence mode="wait">
            {isMobile && (
              <motion.div
                className="flex-shrink-0"
                key={`header-${activeView}`}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.15 }}
              >
                {getHeaderContent()}
              </motion.div>
            )}
          </AnimatePresence>

          {isMobile && activeView !== "CONFIRM_GROUP_CHAT" && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              <ChatDetailsMobile
                currentChat={currentChat}
                showAllMembers={showAllMembers}
                setShowAllMembers={setShowAllMembers}
                isMobile={isMobile}
              />
            </motion.div>
          )}

          <div className="flex-1 min-h-0">
            <MainContentArea
              headerContent={
                !isMobile && activeView !== "CONFIRM_GROUP_CHAT"
                  ? getHeaderContent()
                  : undefined
              }
              key={`main-${activeView}-${chatId || "no-chat"}`}
            >
              <MainContent
                isMobile={isMobile}
                activeView={activeView}
                chatId={chatId}
                currentChat={currentChat}
                handleLeaveGroup={handleLeaveGroup}
              >
                {children}
              </MainContent>
            </MainContentArea>
          </div>
        </div>
      </ChatLayout>
    </div>
  );
}
