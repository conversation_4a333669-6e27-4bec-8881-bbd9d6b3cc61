"use client";

import { useState, useEffect } from "react";
import { fetchAuthSession } from "@aws-amplify/auth";
import { useWindowSize } from "@/hooks/useWindowSize";
import { Schema } from "@/amplify/data/resource";
import { generateClient } from "aws-amplify/api";
import { GraphQLResult } from '@aws-amplify/api';
import { useRouter } from "next/navigation";

import MobileHeader from "@/components/discovery/MobileHeader";
// DesktopHeader import removed
import Moment from "@/components/discovery/Moment";
import RecommendedGroup from "@/components/discovery/RecommendedGroup";

type TransformedPost = {
  id: string;
  userName: string;
  groupTitle: string;
  groupText: string;
  avatar: string;
  content: string;
  title: string;
  text: string; // Add this required field
  image: string | null;
  imageGrid?: string[];
  likes: number;
  comments: number;
  timeAgo: string;
  onCommentAdd: () => void;
  isSaved?: boolean;
  onBookmarkChange?: () => void;
};

type SavedPostItem = {
  postId: string;
  post: {
    id: string;
    userId: string;
    userName: string;
    title: string;
    content: string;
    images: string[];
    likes: number;
    commentsCount: number;
    createdAt: string;
  };
};

type SavedPostsResponse = {
  listSavedPostsByUser: {
    items: SavedPostItem[];
  };
};

// Add this type definition near the other type definitions
type PostListResponse = {
  listPosts: {
    items: Array<{
      id: string;
      userId: string;
      userName: string;
      title: string;
      content: string;
      images: string[];
      likes: number;
      commentsCount: number;
      createdAt: string;
      user?: {
        email: string;
        name: string;
      };
    }>;
  };
};

export default function Posts() {
  const [activeTab, setActiveTab] = useState("posts");
  const [currentUser, setCurrentUser] = useState<{ email: string } | null>(null);
  const [posts, setPosts] = useState<any[]>([]);
  const [savedPosts, setSavedPosts] = useState<any[]>([]);
  const [savedPostIds, setSavedPostIds] = useState<Set<string>>(new Set());
  const isDesktop = useWindowSize();
  const client = generateClient<Schema>();
  const router = useRouter();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.email) {
          const email = String(session.tokens.idToken.payload.email);
          setCurrentUser({ email });
          // Fetch saved posts when user is set
          if (activeTab === "saved") {
            fetchSavedPosts(email);
          }
        } else {
          setCurrentUser({ email: "<EMAIL>" });
        }
      } catch (error) {
        setCurrentUser({ email: "<EMAIL>" });
      }
    };
    fetchUser();
  }, [activeTab]); // Add activeTab to dependencies

  const fetchPosts = async () => {
    try {
      const response = await client.models.Post.list();
      // Sort posts by createdAt in descending order, handling nullable values
      const sortedPosts = response.data.sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return dateB - dateA;
      });
      setPosts(sortedPosts);
    } catch (error) {
      console.error("Error fetching posts:", error);
    }
  };

  const fetchSavedPosts = async (userId: string) => {
    try {
      const result = await client.graphql<SavedPostsResponse>({
        query: `
          query GetSavedPostsForUser($userId: String!) {
            listSavedPostsByUser(userId: $userId) {
              items {
                postId
                post {
                  id
                  userId
                  userName
                  title
                  content
                  images
                  likes
                  commentsCount
                  createdAt
                 
                }
              }
            }
          }
        `,
        variables: {
          userId: userId
        }
      }) as GraphQLResult<SavedPostsResponse>;

      const savedPosts = result.data?.listSavedPostsByUser?.items
        .map(item => item.post)
        .filter(post => post !== null);

      // Update saved post IDs
      const newSavedPostIds = new Set(result.data?.listSavedPostsByUser?.items
        .map(item => item.postId));
      setSavedPostIds(newSavedPostIds);
      setSavedPosts(savedPosts || []);
    } catch (error) {
      console.error("Error fetching saved posts:", error);
      setSavedPosts([]);
      setSavedPostIds(new Set());
    }
  };

  useEffect(() => {
    fetchPosts();
  }, []);

  useEffect(() => {
    if (currentUser?.email) {
      fetchSavedPosts(currentUser.email);
      fetchPosts(); // Refetch posts when saved posts change
    }
  }, [currentUser?.email]);

  const handleCommentAdd = async (postId: string) => {
    try {
      const post = posts.find(p => p.id === postId);
      if (!post) return;

      await client.models.Post.update({
        id: postId,
        commentsCount: (post.commentsCount || 0) + 1
      });

      // Оновлюємо тільки конкретний пост
      setPosts(currentPosts =>
        currentPosts.map(p =>
          p.id === postId
            ? { ...p, commentsCount: (p.commentsCount || 0) + 1 }
            : p
        )
      );
    } catch (error) {
      console.error("Error updating comment count:", error);
    }
  };

  // Update tab change handler
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    if (currentUser?.email) {
      fetchSavedPosts(currentUser.email);
      if (tab === "posts") {
        fetchPosts(); // Refetch posts when switching to posts tab
      }
    }
  };

  // Transform posts to match Moment component format
  const transformPost = (post: any, forceSaved = false) => {
    if (!post) return null;

    const userName = post.userName || (post.userId ? post.userId.split('@')[0] : 'Anonymous');
    const isSaved = forceSaved || savedPostIds.has(post.id);

    return {
      id: post.id || '',
      userName,
      groupTitle: post.title || '',
      groupText: post.createdAt ? new Date(post.createdAt).toLocaleDateString() : '',
      avatar: "/placeholder.svg?height=40&width=40",
      content: post.content || '',
      title: post.title || '',
      text: post.title || '',
      image: post.images?.length > 0 ? post.images[0] : null,
      imageGrid: post.images?.length > 1 ? post.images : undefined,
      likes: post.likes || 0,
      comments: post.commentsCount || 0,
      timeAgo: post.createdAt ? new Date(post.createdAt).toLocaleString() : '',
      onCommentAdd: () => handleCommentAdd(post.id),
      isSaved,
      onBookmarkChange: () => {
        if (currentUser?.email) {
          fetchSavedPosts(currentUser.email);
        }
      }
    } as TransformedPost;
  };

  const transformedPosts = posts
    .map(post => transformPost(post))
    .filter((post): post is TransformedPost => post !== null);
    
  const transformedSavedPosts = savedPosts
    .map(post => transformPost(post, true))
    .filter((post): post is TransformedPost => post !== null);

  const recommendedGroups = Array(9).fill({
    name: "Group Name",
    members: 87,
    avatar: "/placeholder.svg?height=40&width=40",
    description: "Corem ipsum dolor sit amet, consectetur adipiscing elit.",
  });

  return (
    <div className="min-h-screen bg-white flex">
      <div className="flex-1 relative">
        {!isDesktop ? (
          <MobileHeader
            title="Discovery"
            tabs={[
              { id: "posts", label: "Moments" },
              { id: "saved", label: "Comunity" }
            ]}
            activeTab={activeTab}
            onTabChange={handleTabChange}
            loadMorePosts={() => {}} // dummy function
            isLoadingPosts={false}   // dummy value
            nextToken={null}         // dummy value
          />
        ) : (
          // DesktopHeader component removed
          <MobileHeader
            title="Discovery"
            tabs={[
              { id: "posts", label: "Moments" },
              { id: "saved", label: "Comunity" }
            ]}
            activeTab={activeTab}
            onTabChange={handleTabChange}
            loadMorePosts={() => {}} // dummy function
            isLoadingPosts={false}   // dummy value
            nextToken={null}         // dummy value
          />
        )}

        <div className="flex flex-col pb-20 md:flex-row py-5 md:ml-[96px] lg:ml-[128px] md:mt-0">
          <div className="w-full md:flex-1">
           

            <div className={`${activeTab === "posts" ? "block" : "hidden"} `}>
              {transformedPosts.map((post) => (
                <Moment
                  key={post.id}
                  moment={post}
                  onCommentAdd={() => handleCommentAdd(post.id)}
                  onBookmarkChange={post.onBookmarkChange}
                />
              ))}
            </div>

            <div className={`${activeTab === "saved" ? "block" : "hidden"} space-y-6`}>
              {transformedSavedPosts.length > 0 ? (
                transformedSavedPosts.map((post) => (
                  <Moment
                    key={post.id}
                    moment={post}
                    onCommentAdd={() => handleCommentAdd(post.id)}
                    onBookmarkChange={post.onBookmarkChange}
                  />
                ))
              ) : (
                <div className="text-center text-gray-500 pt-4">
                  No saved posts yet
                </div>
              )}
            </div>
          </div>

          <div className="hidden md:block md:ml-6 md:w-[300px] lg:w-[350px] ">
            <div className="bg-white rounded-lg p-2 mt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="font-medium text-gray-800">Recommended group</h2>
              </div>
              <div className="space-y-4">
                {recommendedGroups.map((group, index) => (
                  <RecommendedGroup key={index} group={group} />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
