"use client";

import { useRouter } from "next/navigation";
import CustomButton from "@/components/ui/CustomButton";

export default function CommunityRules() {
  const router = useRouter();

  return (
    <div className="flex flex-col min-h-screen">
      <div className="relative mb-8 bg-gray-100 h-20 flex items-center justify-center">
        <button 
          onClick={() => router.back()}
          className="absolute left-0 top-0 text-2xl pl-5 flex items-center justify-center h-full "
        >
          ✕
        </button>
        <h1 className="text-lg font-medium text-center">
          Community Rules
        </h1>
      </div>

      <div className="space-y-4 text-gray-600 px-5 flex-grow ">
        <p>
          Worem ipsum dolor sit amet, consectetur adipiscing elit. 
          Nunc vulputate libero et velit interdum, ac aliquet odio 
          mattis.
        </p>
        <p>
          Worem ipsum dolor sit amet, consectetur adipiscing elit. 
          Nunc vulputate libero et velit interdum, ac aliquet odio 
          mattis. Worem ipsum dolor sit amet, consectetur 
          adipiscing elit. Nunc vulputate libero et velit interdum, ac
          aliquet odio mattis.
        </p>
        <p>
          Worem ipsum dolor sit amet, consectetur adipiscing elit. 
          Nunc vulputate libero et velit interdum, ac aliquet odio 
          mattis. Worem ipsum dolor sit amet, consectetur 
          adipiscing elit. Nunc vulputate libero et velit interdum, ac
          aliquet odio mattis.
        </p>
      </div>

      <div className="px-5 py-6 fixed bottom-0 left-0 right-0 z-50 bg-gray-100 shadow-lg">
        <CustomButton
          onClick={() => router.back()}
          className="bg-[#5185FF] hover:bg-blue-600"
        >
          I Understand
        </CustomButton>
      </div>
    </div>
  );
}
