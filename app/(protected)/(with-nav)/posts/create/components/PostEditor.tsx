interface PostEditorProps {
  title: string;
  content: string;
  onTitleChange: (title: string) => void;
  onContentChange: (content: string) => void;
}

export function PostEditor({ title, content, onTitleChange, onContentChange }: PostEditorProps) {
  return (
    <>
      <div className="mb-4">
        <input
          type="text"
          value={title}
          onChange={(e) => onTitleChange(e.target.value)}
          className="w-full p-2 text-xl border-b border-black focus:outline-none focus:border-blue-500 placeholder-gray-400"
          placeholder="Title"
        />
      </div>

      <div className="mb-4">
        <textarea
          value={content}
          onChange={(e) => onContentChange(e.target.value)}
          className="w-full p-2 text-sm border-b border-gray-300 focus:outline-none focus:border-blue-500 placeholder-gray-400 resize-none"
          rows={6}
          placeholder="Content"
        />
      </div>
    </>
  );
}
