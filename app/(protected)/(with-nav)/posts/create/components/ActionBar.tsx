import Image from "next/image";

interface ActionBarProps {
  onImageChange: (e: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  imageLoading: boolean;
  isMobile?: boolean;
}

export function ActionBar({ onImageChange, imageLoading, isMobile = false }: ActionBarProps) {
  const inputId = isMobile ? "image-upload-mobile" : "image-upload-desktop";
  
  const content = (
    <>
      <div className="flex items-center text-sm text-[#5185FF]">
        <span className="mr-2">🌐</span>
        <span>Everyone can reply</span>
      </div>
      <div className="flex items-center gap-4">
        <label htmlFor={inputId} className="cursor-pointer">
          <div className="text-gray-500">
            <Image src="/Utility.svg" alt="Add Images" width={24} height={24} />
          </div>
          <input
            id={inputId}
            type="file"
            multiple
            accept="image/*"
            className="hidden"
            onChange={onImageChange}
            disabled={imageLoading}
          />
        </label>
      </div>
    </>
  );

  if (isMobile) {
    return (
      <div className="md:hidden fixed inset-x-0 bottom-0 z-50 bg-white border-t border-gray-100 shadow-lg rounded-t-3xl">
        <div className="p-4">
          <div className="flex items-center justify-between mb-6">
            {content}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="hidden md:flex items-center justify-between mt-4">
      {content}
    </div>
  );
}
