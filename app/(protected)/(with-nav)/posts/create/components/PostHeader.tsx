import { useRouter } from "next/navigation";

interface PostHeaderProps {
  onCreatePost: () => Promise<void>;
  isValid: boolean;
}

export function PostHeader({ onCreatePost, isValid }: PostHeaderProps) {
  const router = useRouter();
  
  return (
    <div className="flex justify-between items-center mb-4">
      <button
        onClick={() => router.push("/posts")}
        className="text-gray-600 text-xl font-bold"
      >
        ✕
      </button>
      <div className="flex items-center space-x-2">
        <button
          onClick={onCreatePost}
          className="bg-[#5185FF] text-white px-4 py-1.5 rounded-full text-sm font-medium"
          disabled={!isValid}
        >
          Post
        </button>
      </div>
    </div>
  );
}
