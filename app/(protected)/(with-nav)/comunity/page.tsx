"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import { Plus, Search } from "lucide-react";
import Image from "next/image";

import RecommendedGroup from "@/components/discovery/RecommendedGroup";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";

export default function CommunityPage() {
  const [recommendedCommunities, setRecommendedCommunities] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [ownedCommunities, setOwnedCommunities] = useState<any[]>([]);
  const [joinedCommunities, setJoinedCommunities] = useState<any[]>([]);
  const [searchQuery, setSearchQuery] = useState(""); // додано стан для пошуку
  const client = generateClient<Schema>();
  
  const fetchRecommendedCommunities = async () => {
    setIsLoading(true);
    try {
      const result = await client.models.Community.list({
        limit: 10
      });
      
      if (result.data) {
        const communities = result.data.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description || "",
          members: item.membersCount || 0,
          icon: item.icon || "/groupavatar.svg"
        }));
        
        // For demo purposes, let's split communities into owned and joined
        const owned = communities.slice(0, 1);
        const joined = communities.slice(1);
        
        setRecommendedCommunities(communities);
        setOwnedCommunities(owned);
        setJoinedCommunities(joined);
      }
    } catch (error) {
      console.error("Error fetching communities:", error);
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    fetchRecommendedCommunities();
  }, []);
  
  // Фільтрація спільнот за пошуковим запитом
  const filteredOwned = ownedCommunities.filter(
    (community) =>
      community.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  const filteredJoined = joinedCommunities.filter(
    (community) =>
      community.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="h-full  mt-[69px] ">
      <div className="">
      <div className="flex justify-between items-center mb-3 px-6">
        <h1 className="text-2xl font-medium">Discovery</h1>
        <div className="flex space-x-3">
          <Link href="/comunity/create" className="w-[40px] h-[40px] bg-gray-300 rounded-2xl flex items-center justify-center">
            <Image src="/grayplus.svg" width={24} height={24} alt="Create" />
          </Link>
          <button className="w-[40px] h-[40px] bg-gray-300 rounded-2xl flex items-center justify-center">
            <Image src="/menu-deepg.svg" width={24} height={24} alt="Menu" />
          </button>
        </div>
        </div>
      </div>
      
      <div className="bg-white rounded-2xl shadow-sm p-5 overflow-auto  h-[calc(100vh-120px)]">
        <h2 className="text-[16px] font-semibold mb-[10px]">My Community</h2>
        
        <div className="mb-4">
          <div className="flex items-center border border-[#D4D4D4] rounded-full p-2 bg-[#F5F5F5] h-[56px]">
            <Image
              src="/graysearch.svg"
              alt="Search"
              width={24}
              height={24}
              className="w-6 h-6 text-gray-400"
            />
            <input
              type="text"
              placeholder="Search"
              className="w-full outline-none text-gray-600 bg-[#F5F5F5] pl-2"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)} // додано обробник
            />
          </div>
        </div>
        
        <div className="mb-4">
          <div className="space-y-3">
            {filteredOwned.map((community) => (
              <div key={community.id} className="flex items-center space-x-2">
                <Image 
                  src={community.icon || "/groupavatar.svg"} 
                  width={40} 
                  height={40} 
                  alt={community.name} 
                  className="rounded-full"
                />
                <Link href={`/comunity/${community.id}`} className="text-gray-800 truncate hover:underline">
                  {community.name}
                </Link>
              </div>
            ))}
          </div>
        </div>
        
        <div>
          <div className="space-y-3">
            {filteredJoined.map((community) => (
              <div key={community.id} className="flex items-center space-x-2">
                <Image 
                  src={community.icon || "/groupavatar.svg"} 
                  width={40} 
                  height={40} 
                  alt={community.name} 
                  className="rounded-full"
                />
                <Link href={`/comunity/${community.id}`} className="text-gray-800 truncate hover:underline">
                  {community.name}
                </Link>
              </div>
            ))}
          </div>
        </div>
      </div>
      
     
    </div>
  );
}
