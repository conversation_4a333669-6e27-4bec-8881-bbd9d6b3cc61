"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { fetchAuthSession } from "@aws-amplify/auth";
import { Schema } from "@/amplify/data/resource";
import { generateClient } from "aws-amplify/api";


import Header from "./components/Header";
import CommunityInfo from "./components/CommunityInfo";
import PostForm from "./components/PostForm";
import ImageUploadPreview from "./components/ImageUploadPreview";
import Footer from "./components/Footer";
import LoadingState from "./components/LoadingState";

export default function CreateCommunityPost() {
  const params = useParams();
  const communityId = params?.id as string;
  const router = useRouter();
  const [currentUser, setCurrentUser] = useState<{ email: string } | null>(null);
  const [postTitle, setPostTitle] = useState("");
  const [postContent, setPostContent] = useState("");
  const [postImages, setPostImages] = useState<string[]>([]);
  const [community, setCommunity] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [imageLoading, setImageLoading] = useState(false);
  const client = generateClient<Schema>();

  useEffect(() => {
    const fetchData = async () => {
      try {

        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.email) {
          const email = String(session.tokens.idToken.payload.email);
          setCurrentUser({ email });
        } else {
          router.push(`/comunity/${communityId}`);
          return;
        }
        

        const communityResult = await client.models.Community.get({ id: communityId });
        
        if (!communityResult) {

          router.push("/comunity");
          return;
        }
        
        setCommunity(communityResult);
        

        const membershipResult = await client.models.CommunityMember.list({
          filter: {
            communityId: { eq: communityId },
            userId: { eq: String(session.tokens.idToken.payload.email) }
          }
        });
        
        if (membershipResult.data.length === 0) {
          
          router.push(`/comunity/${communityId}`);
          return;
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        router.push(`/comunity/${communityId}`);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [communityId, router]);

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImageLoading(true);
      const fileArray = Array.from(e.target.files);
      const imagePromises = fileArray.map(file => {
        return new Promise<string>((resolve) => {
          const reader = new FileReader();
          reader.onload = (e) => {
            if (e.target && typeof e.target.result === 'string') {
              resolve(e.target.result);
            }
          };
          reader.readAsDataURL(file);
        });
      });

      try {
        const newImages = await Promise.all(imagePromises);
        setPostImages(prev => [...prev, ...newImages]);
      } catch (error) {
        console.error("Error processing images:", error);
        alert("Error uploading images. Please try again.");
      } finally {
        setImageLoading(false);
      }
    }
  };

  const removeImage = (index: number) => {
    setPostImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleCreatePost = async () => {
    try {
      if (!currentUser || !postTitle.trim() || !postContent.trim() || !communityId) {
        alert('Please fill in both title and content');
        return;
      }

      const newPost = {
        id: crypto.randomUUID(),
        userId: currentUser.email,
        userName: currentUser.email.split('@')[0],
        title: postTitle,
        content: postContent,
        images: postImages,
        likes: 0,
        commentsCount: 0,
        createdAt: new Date().toISOString(),
        communityId: communityId
      };
      
      await client.models.Post.create(newPost);
      router.push(`/comunity/${communityId}`);
    } catch (error) {
      console.error("Error creating post:", error);
      alert('Failed to create post. Please try again.');
    }
  };

  if (isLoading) {
    return <LoadingState />;
  }


  const isFormValid = postTitle.trim() !== "" && postContent.trim() !== "";

  return (
    <div className="max-w-2xl mx-auto p-4">
      <Header 
        communityId={communityId} 
        handleCreatePost={handleCreatePost} 
        isFormValid={isFormValid} 
        router={router}
      />
      
      <CommunityInfo community={community} />
      
      <PostForm 
        postTitle={postTitle}
        setPostTitle={setPostTitle}
        postContent={postContent}
        setPostContent={setPostContent}
      />
      
      <ImageUploadPreview 
        postImages={postImages}
        removeImage={removeImage}
      />
      
      <Footer 
        communityName={community?.name}
        handleImageChange={handleImageChange}
        imageLoading={imageLoading}
      />
    </div>
  );
}
