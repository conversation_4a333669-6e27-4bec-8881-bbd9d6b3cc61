import { ArrowLeft } from "lucide-react";

interface HeaderProps {
  communityId: string;
  handleCreatePost: () => Promise<void>;
  isFormValid: boolean;
  router: any;
}

export default function Header({ communityId, handleCreatePost, isFormValid, router }: HeaderProps) {
  return (
    <div className="flex justify-between items-center mb-4">
      <button
        onClick={() => router.push(`/comunity/${communityId}`)}
        className="text-gray-600 font-medium flex items-center"
      >
        <ArrowLeft className="w-5 h-5 mr-2" />
        Back
      </button>
      <button
        onClick={handleCreatePost}
        className="bg-[#5185FF] text-white px-4 py-1.5 rounded-full text-sm font-medium"
        disabled={!isFormValid}
      >
        Post
      </button>
    </div>
  );
}
