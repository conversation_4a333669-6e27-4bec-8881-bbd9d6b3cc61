import Image from "next/image";

interface CommunityInfoProps {
  community: {
    name: string;
    icon?: string;
  } | null;
}

export default function CommunityInfo({ community }: CommunityInfoProps) {
  return (
    <div className="flex items-center mb-4">
      <div className="flex items-center py-1 px-2 rounded-full text-sm text-black bg-white border border-gray-300">
        <div className="w-6 h-6 rounded-full flex-shrink-0 mr-2 overflow-hidden">
          <Image
            src={community?.icon || "/groupavatar.svg"}
            alt="Community Avatar"
            width={24}
            height={24}
            className="object-cover rounded-full"
          />
        </div>
        <p className="text-sm">{community?.name}</p>
      </div>
    </div>
  );
}
