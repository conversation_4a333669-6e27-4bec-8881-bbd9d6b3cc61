"use client";

import Navbar from "@/components/NavBar";
import "../../globals.css";

import { usePathname } from "next/navigation";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isChatDetailPage = pathname?.match(/^\/chats\/[\w-]+$/);
  const isHealthPage = pathname === "/health";


  let navBarClassName = "block";

  if (isHealthPage) {
    navBarClassName = "block";
  } else if (isChatDetailPage) {
    navBarClassName = "hidden md:block";
  }

  return (
    <>
      <div className="flex md:flex-row flex-col h-full w-full overflow-hidden rounded-3xl">
        <main
          className={`flex-1 overflow-hidden rounded-y-3xl md:rounded-3xl md:order-2 order-1`}
        >
          {children}
        </main>

        <div className={`${navBarClassName} md:order-1 order-2`}>
          <Navbar />
        </div>
      </div>
    </>
  );
}
