"use client";

import { useMedicalRecord } from "@/hooks/useMedicalRecord";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function TestMedicalRecordContext() {
  const { selectedRecord, setSelectedRecord } = useMedicalRecord();
  const router = useRouter();
  const [recordType, setRecordType] = useState<
    | "Outpatient Visit"
    | "Lab Result"
    | "Symptom"
    | "Medication Reaction"
    | "Medication"
  >("Outpatient Visit");

  const getTestRecord = () => {
    const baseRecord = {
      id: `test-record-${Date.now()}`,
      date: "June 13, 2025",
      location: "Test Hospital",
      doctor: "Dr. Test",
      isClickable: true,
    };

    switch (recordType) {
      case "Lab Result":
        return {
          ...baseRecord,
          type: "Lab Result" as const,
          description: "CBC Blood Test Results",
          rawData: {
            resourceType: "Observation",
            status: "final",
            code: {
              text: "Complete Blood Count",
              coding: [{ code: "CBC", display: "Complete Blood Count" }],
            },
            valueQuantity: {
              value: 14.2,
              unit: "g/dL",
            },
            issued: "2025-06-13",
          },
        };
      case "Outpatient Visit":
        return {
          ...baseRecord,
          type: "Outpatient Visit" as const,
          description: "Routine check-up visit",
        };
      case "Symptom":
        return {
          ...baseRecord,
          type: "Symptom" as const,
          description: "Headache and dizziness",
        };
      case "Medication Reaction":
        return {
          ...baseRecord,
          type: "Medication Reaction" as const,
          description: "Allergic reaction to penicillin",
        };
      case "Medication":
        return {
          ...baseRecord,
          type: "Medication" as const,
          description: "Prescription for ibuprofen",
        };
    }
  };

  const handleRecordSet = () => {
    const testRecord = getTestRecord();
    setSelectedRecord(testRecord);
    alert(
      `${recordType} record set in context. Now navigate to the appropriate page to see if data persists.`
    );
  };

  const handleClearRecord = () => {
    setSelectedRecord(null);
    alert("Record cleared from context.");
  };

  const handleNavigate = () => {
    switch (recordType) {
      case "Outpatient Visit":
      case "Lab Result":
        router.push("/outpatient");
        break;
      case "Symptom":
        router.push("/symptom");
        break;
      case "Medication Reaction":
        router.push("/medication-reaction");
        break;
      case "Medication":
        router.push("/medication");
        break;
      default:
        router.push("/medical-record");
    }
  };

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF]">
      <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto space-y-4">
        <h1 className="text-xl font-bold">Medical Record Context Test</h1>

        <div className="space-y-4">
          <div className="p-4 bg-gray-100 rounded-lg">
            <h2 className="font-medium mb-2">Current Context Data:</h2>
            {selectedRecord ? (
              <pre className="bg-white p-3 rounded overflow-auto max-h-60">
                {JSON.stringify(selectedRecord, null, 2)}
              </pre>
            ) : (
              <p>No record set in context</p>
            )}
          </div>

          <div className="mb-4">
            <h2 className="font-medium mb-2">Select Record Type:</h2>
            <div className="flex flex-wrap gap-2">
              {[
                "Outpatient Visit",
                "Lab Result",
                "Symptom",
                "Medication Reaction",
                "Medication",
              ].map((type) => (
                <button
                  key={type}
                  className={`px-3 py-1 rounded ${
                    recordType === type
                      ? "bg-blue-600 text-white"
                      : "bg-gray-200 text-gray-800"
                  }`}
                  onClick={() =>
                    setRecordType(
                      type as
                        | "Outpatient Visit"
                        | "Lab Result"
                        | "Symptom"
                        | "Medication Reaction"
                        | "Medication"
                    )
                  }
                >
                  {type}
                </button>
              ))}
            </div>
            <p className="mt-2 text-sm text-gray-500">
              Selected: <strong>{recordType}</strong>
              {recordType === "Lab Result" && (
                <span className="ml-1 text-blue-600">
                  (This will redirect to the Outpatient page)
                </span>
              )}
            </p>
          </div>

          <div className="flex space-x-4">
            <button
              className="px-4 py-2 bg-blue-500 text-white rounded"
              onClick={handleRecordSet}
            >
              Set Test Record
            </button>

            <button
              className="px-4 py-2 bg-gray-500 text-white rounded"
              onClick={handleClearRecord}
            >
              Clear Record
            </button>

            <button
              className="px-4 py-2 bg-green-500 text-white rounded"
              onClick={handleNavigate}
            >
              Navigate to{" "}
              {recordType === "Lab Result" ? "Outpatient" : recordType} Page
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
