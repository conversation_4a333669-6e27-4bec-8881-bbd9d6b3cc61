'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useState } from 'react';

export default function CreateGoal() {
  const router = useRouter();
  const [title, setTitle] = useState('');
  const [target, setTarget] = useState('');
  const [frequency, setFrequency] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [withoutEndDate, setWithoutEndDate] = useState(false);
  const [isFrequencyOpen, setIsFrequencyOpen] = useState(false);

  const handleSave = () => {
    // Save goal logic would go here
    router.back();
  };

  const frequencyOptions = ['day', 'week', 'month', 'year'];

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF] relative">
      {/* Header with back button and title */}
      <div className="flex items-center justify-between pt-[51px] pl-6 pr-6 py-4 overflow-hidden">
        <div className="flex items-center">
          <Image
            src="/chevron-left.svg"
            alt="Back"
            height={24}
            width={24}
            className="cursor-pointer"
            onClick={() => router.back()}
          />
          <h1 className="text-[22px] font-medium ml-4">Create a New Goal</h1>
        </div>
      </div>

      <div className="flex-1 p-6 bg-white rounded-t-3xl overflow-auto">
        <form className="space-y-8">
          {/* Title field */}
          <div>
            <label htmlFor="title" className="block text-[18px] font-medium mb-2">
              Title
            </label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder='e.g., "Improve My Sleep Routine"'
              className="w-full bg-gray-100 rounded-2xl py-4 px-5 text-gray-500"
            />
          </div>

          {/* Target field */}
          <div>
            <label className="block text-[18px] font-medium mb-2">
              Target
            </label>
            <div className="mb-2">I want to</div>
            <input
              type="text"
              value={target}
              onChange={(e) => setTarget(e.target.value)}
              placeholder="e.g., walk 8000 steps"
              className="w-full bg-gray-100 rounded-2xl py-4 px-5 text-gray-500 mb-2"
            />
            
            <div className="mb-2">per</div>
            <div className="relative">
              <div 
                className="w-full bg-gray-100 rounded-2xl py-4 px-5 text-gray-500 flex justify-between items-center cursor-pointer"
                onClick={() => setIsFrequencyOpen(!isFrequencyOpen)}
              >
                <span>{frequency || 'Select an option'}</span>
                <Image
                  src="/health/chevron-down.svg"
                  alt="Select"
                  width={24}
                  height={24}
                />
              </div>
              
              {isFrequencyOpen && (
                <div className="absolute top-full left-0 w-full bg-white rounded-2xl mt-1 shadow-lg z-10">
                  {frequencyOptions.map((option) => (
                    <div 
                      key={option}
                      className="py-3 px-5 hover:bg-gray-50 cursor-pointer"
                      onClick={() => {
                        setFrequency(option);
                        setIsFrequencyOpen(false);
                      }}
                    >
                      {option}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Start date */}
          <div>
            <label className="block text-[18px] font-medium mb-2">
              Start date
            </label>
            <div className="relative">
              <input
                type="text"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                placeholder="MM / DD / YYYY"
                className="w-full bg-gray-100 rounded-2xl py-4 px-5 text-gray-500"
              />
              <div className="absolute right-5 top-1/2 transform -translate-y-1/2">
                <Image
                  src="/health/calender.svg"
                  alt="Calendar"
                  width={24}
                  height={24}
                />
              </div>
            </div>
          </div>

          {/* End date */}
          <div>
            <label className="block text-[18px] font-medium mb-2">
              End date
            </label>
            <div className="relative">
              <input
                type="text"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                placeholder="MM / DD / YYYY"
                disabled={withoutEndDate}
                className={`w-full bg-gray-100 rounded-2xl py-4 px-5 text-gray-500 ${withoutEndDate ? 'opacity-50' : ''}`}
              />
              <div className="absolute right-5 top-1/2 transform -translate-y-1/2">
                <Image
                  src="/health/calender.svg"
                  alt="Calendar"
                  width={24}
                  height={24}
                />
              </div>
            </div>
            
            <div className="flex items-center mt-4">
              <input
                type="checkbox"
                id="withoutEndDate"
                checked={withoutEndDate}
                onChange={(e) => setWithoutEndDate(e.target.checked)}
                className="w-5 h-5 rounded border-gray-300"
              />
              <label htmlFor="withoutEndDate" className="ml-2 text-gray-800">
                Without end date
              </label>
            </div>
          </div>
        </form>
      </div>

      {/* Save button */}
      <div className="p-6 bg-white rounded-b-3xl">
        <button
          onClick={handleSave}
          className="w-full bg-blue-500 text-white font-medium py-4 rounded-full text-lg"
        >
          Save
        </button>
      </div>
    </div>
  );
}
