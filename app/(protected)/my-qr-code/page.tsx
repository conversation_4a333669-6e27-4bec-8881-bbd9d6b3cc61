"use client";

import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { useRouter } from "next/navigation";

export default function MyQRCodePage() {
  const router = useRouter();

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Header */}
      <div className="p-4 flex items-center">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => router.back()}
          className="mr-4"
        >
          <Image
            src="/chats/arrow-left.svg"
            alt="Back"
            width={24}
            height={24}
          />
        </Button>
        <h1 className="text-xl font-semibold flex-1 text-center pr-8">
          My QR Code
        </h1>
      </div>

      <div className="flex-1 flex flex-col items-start px-[60px] pt-[150px]">
        {/* User profile */}
        <div className="flex items-center mb-6">
          <div className="w-12 h-12 rounded-full bg-green-200 overflow-hidden mr-3 flex items-center justify-center">
            <Image
              src="/Avatar.png"
              alt="Profile"
              width={48}
              height={48}
              className="object-cover"
              onError={(e) => {
                e.currentTarget.src = "https://via.placeholder.com/48";
              }}
            />
          </div>
          <span className="text-lg">User Name</span>
        </div>

        {/* QR Code */}
        <div className="bg-white w-full max-w-xs shadow-sm mb-8">
          <div className="relative aspect-square w-full">
            <div className="absolute inset-0 bg-blue-500 bg-opacity-10">
              <Image
                src="/Qr.svg"
                alt="QR Code"
                width={310}
                height={310}
                className="object-cover"
                onError={(e) => {
                  e.currentTarget.src = "https://via.placeholder.com/200";
                }}
              />
            </div>
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-600 text-center">
          Scan the QR code above to add me as a friend
        </p>
      </div>
    </div>
  );
}
