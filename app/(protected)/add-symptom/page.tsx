'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useState } from 'react';
import CustomButton from '@/components/ui/CustomButton';

export default function AddSymptom() {
  const router = useRouter();
  const [symptom, setSymptom] = useState('');
  const [date, setDate] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!symptom || !date) {
      return; // Prevent empty submissions
    }
    
    setIsLoading(true);
    
    try {
      // In a real app, you would send data to your backend here
      // await addSymptom({ symptom, date });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Navigate back to the symptoms page after successful submission
      router.push('/symptom');
    } catch (error) {
      console.error('Failed to add symptom:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF]">
      {/* Header with back button and title */}
      <div className="flex items-center pt-[51px] pl-6 py-4">
        <Image
          src="/chevron-left.svg"
          alt="Back"
          height={24}
          width={24}
          className="cursor-pointer"
          onClick={() => router.back()}
        />
        <h1 className="ml-4 text-[16px] font-medium">Add Symptom</h1>
      </div>

      <div className="h-full px-6 pt-6 pb-5 bg-white rounded-3xl overflow-auto">
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Symptom Field */}
          <div className="space-y-2">
            <label className="text-[16px] font-medium">Symptom</label>
            <input
              type="text"
              value={symptom}
              onChange={(e) => setSymptom(e.target.value)}
              placeholder="e.g., Headache"
              className="w-full h-[60px] px-4 py-3 rounded-2xl bg-[#F5F5F5] border-none focus:outline-none focus:ring-2 focus:ring-blue-500 text-[16px]"
            />
          </div>

          {/* Date Field */}
          <div className="space-y-2">
            <label className="text-[16px] font-medium">Date</label>
            <div className="relative">
              <input
                type="text"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                placeholder="MM / DD / YYYY"
                className="w-full h-[60px] px-4 py-3 rounded-2xl bg-[#F5F5F5] border-none focus:outline-none focus:ring-2 focus:ring-blue-500 text-[16px]"
                onFocus={(e) => e.target.type = 'date'}
                onBlur={(e) => {
                  if (!e.target.value) {
                    e.target.type = 'text';
                  }
                }}
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <Image
                  src="/health/calender.svg"
                  alt="Calendar"
                  width={20}
                  height={20}
                />
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="pt-8">
            <CustomButton
              type="submit"
              loading={isLoading}
              disabled={!symptom || !date || isLoading}
              className="rounded-full h-[56px] bg-[#5671FF]"
            >
              Save
            </CustomButton>
          </div>
        </form>
      </div>
    </div>
  );
}
