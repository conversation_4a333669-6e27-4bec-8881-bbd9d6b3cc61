'use client';

import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useState } from 'react';
import CustomButton from '@/components/ui/CustomButton';
import { Input } from '@/components/ui/input';

export default function MedicationReaction() {
  const router = useRouter();
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [selectedMedication, setSelectedMedication] = useState('');
  const [showMedicationOptions, setShowMedicationOptions] = useState(false);

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF] relative">
      {/* Header */}
      <div className="flex items-center pt-[51px] pl-6 pr-6 py-4">
        <Image
          src="/chevron-left.svg"
          alt="Back"
          height={24}
          width={24}
          className="cursor-pointer"
          onClick={() => router.back()}
        />
        <div className="ml-4">
          <h1 className="text-[16px] font-medium">Add Medication Reaction</h1>
        </div>
      </div>

      {/* Form Content */}
      <div className="px-6 bg-white rounded-3xl pb-5 pt-5 h-full overflow-auto">
        {/* Related Medication Field */}
        <div className="mb-6">
          <label className="block mb-2 font-medium">Related Medication</label>
          <div className="relative">
            <Input
              type="text"
              placeholder="e.g., Hydroxychloroquine"
              className="w-full p-3 bg-[#F5F5F5] rounded-lg outline-none border-none"
              value={selectedMedication}
              onChange={(e) => setSelectedMedication(e.target.value)}
              onClick={() => setShowMedicationOptions(true)}
            />
            <Image
              src="/health/chevron-down.svg"
              alt="Dropdown"
              width={24}
              height={24}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
              onClick={() => setShowMedicationOptions(!showMedicationOptions)}
            />
          </div>

          {/* Medication Options Dropdown */}
          {showMedicationOptions && (
            <div className="absolute z-10 mt-1 bg-white rounded-lg shadow-lg border p-3 w-[calc(100%-3rem)]">
              <div className="p-2 font-medium border-b">All Current Medication</div>
              <div className="p-2 hover:bg-gray-100 cursor-pointer" onClick={() => {
                setSelectedMedication('Current Medication 1');
                setShowMedicationOptions(false);
              }}>Current Medication 1</div>
              <div className="p-2 hover:bg-gray-100 cursor-pointer" onClick={() => {
                setSelectedMedication('Current Medication 2');
                setShowMedicationOptions(false);
              }}>Current Medication 2</div>
              <div className="p-2 hover:bg-gray-100 cursor-pointer" onClick={() => {
                setSelectedMedication('Current Medication 3');
                setShowMedicationOptions(false);
              }}>Current Medication 3</div>
            </div>
          )}
        </div>

        {/* Reaction Field */}
        <div className="mb-6">
          <label className="block mb-2 font-medium">Reaction</label>
          <textarea
            placeholder="e.g., Skin rash, dizziness"
            className="w-full p-3 bg-[#F5F5F5] rounded-lg outline-none h-32 resize-none border-none"
          ></textarea>
        </div>

        {/* Date Field */}
        <div className="mb-6">
          <label className="block mb-2 font-medium">Date</label>
          <div className="relative">
            <Input
              type="text"
              placeholder="MM / DD / YYYY"
              className="w-full p-3 bg-[#F5F5F5] rounded-lg outline-none border-none"
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
            />
            <Image
              src="/health/calender.svg"
              alt="Calendar"
              width={24}
              height={24}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
              onClick={() => setIsCalendarOpen(!isCalendarOpen)}
            />
          </div>

          {/* Calendar picker */}
          {isCalendarOpen && (
            <div className="absolute z-10 mt-1 bg-white rounded-lg shadow-lg border p-3 w-[calc(100%-3rem)]">
              <div className="flex justify-between items-center mb-4">
                <div className="text-lg font-medium">January 2022</div>
                <div className="flex gap-4">
                  <span className="cursor-pointer">&lt;</span>
                  <span className="cursor-pointer">&gt;</span>
                </div>
              </div>
              
              <div className="grid grid-cols-7 gap-2 text-center">
                <div className="text-xs text-gray-500">SUN</div>
                <div className="text-xs text-gray-500">MON</div>
                <div className="text-xs text-gray-500">TUE</div>
                <div className="text-xs text-gray-500">WED</div>
                <div className="text-xs text-gray-500">THR</div>
                <div className="text-xs text-gray-500">FRI</div>
                <div className="text-xs text-gray-500">SAT</div>
                
                <div className="h-8 w-8 rounded-full flex items-center justify-center bg-blue-500 text-white">1</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">2</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">3</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">4</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">5</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">6</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">7</div>
                
                {/* Remaining calendar days */}
                <div className="h-8 w-8 rounded-full flex items-center justify-center">8</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">9</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">10</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">11</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">12</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">13</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">14</div>
                
                <div className="h-8 w-8 rounded-full flex items-center justify-center">15</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">16</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">17</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">18</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">19</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">20</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">21</div>
                
                <div className="h-8 w-8 rounded-full flex items-center justify-center">22</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">23</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">24</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">25</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">26</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">27</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">28</div>
                
                <div className="h-8 w-8 rounded-full flex items-center justify-center">29</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">30</div>
                <div className="h-8 w-8 rounded-full flex items-center justify-center">31</div>
              </div>
            </div>
          )}
        </div>

        {/* Save Button */}
        <div className="mt-8">
          <CustomButton onClick={() => router.push('/medication')}>
            Save
          </CustomButton>
        </div>
      </div>
    </div>
  );
}
