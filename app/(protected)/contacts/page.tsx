"use client";

import { useRouter } from "next/navigation";
import { useWindowSize } from "@/hooks/useWindowSize";
import ContactsPageContent from "@/components/contacts/ContactsPageContent";
import Image from "next/image";

// Default export for Next.js page (no props)
export default function ContactsPage() {
  const isDesktop = useWindowSize();
  const router = useRouter();
  
  // If accessed directly on desktop, show as modal
  if (isDesktop) {
    return <ContactsPageContent isModal={true} onClose={() => router.push('/chats')} />;
  }
  
  // On mobile, show as regular page with explicit mobile mode flag
  return (
    <div className="h-full pt-[63px]  w-full" style={{ WebkitOverflowScrolling: 'touch' }}>
      <div className="flex items-center  pl-[20px] pb-[16px]">
        <Image
          src="/chevron-left.svg"
          alt="Close"
          height={24}
          width={24}
          className=""
          onClick={() => router.push('/chats')}
        />
        <h1 className="text-[16px] font-semibold flex-1 pl-3">
          New Friends
        </h1>
      </div>
      <ContactsPageContent isModal={false} />
    </div>
  );
}