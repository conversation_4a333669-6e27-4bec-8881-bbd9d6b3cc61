import { useRouter } from 'next/navigation';
import Image from 'next/image';

export default function Header() {
  const router = useRouter();
  
  return (
    <div className="flex items-center pt-[51px] pl-6 py-4 overflow-hidden">
      <Image
        src="/chevron-left.svg"
        alt="Back"
        height={24}
        width={24}
        className="cursor-pointer"
        onClick={() => router.back()}
      />
      <div className="ml-4 flex items-center">
        <h1 className="text-[14px] font-normal mr-2">OutPatient Visit</h1>
        <p className="text-[14px] text-gray-500">March 3, 2025</p>
      </div>
    </div>
  );
}
