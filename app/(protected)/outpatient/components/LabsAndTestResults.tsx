import Image from 'next/image';

export default function LabsAndTestResults() {
  return (
    <div className="bg-white rounded-3xl p-4 border border-gray-100 shadow-sm">
      <div className="flex items-center mb-3">
        <div className="text-teal-500 mr-2">
          <Image
            src="/outpatient/inspect.svg"
            alt="Labs & Test Results"
            width={24}
            height={24}
            className="w-6 h-6"
          />
        </div>
        <h2 className="text-[#19C5BB] font-medium">Labs & Test Results</h2>
      </div>

      <div className="space-y-3">
        <div className="bg-gray-100 rounded-xl p-2 flex justify-between items-center h-[53px]">
          <div>
            <p className="font-medium text-[14px]">Hemoglobin A1c</p>
            <div className="flex items-center text-green-600">
              <Image
                src="/outpatient/check.svg"
                alt="Diagnoses"
                width={16}
                height={16}
                className="w-4 h-4"
              />
              <span className='pl-1 text-[12px] text-[#00A24E]'>Normal</span>
            </div>
          </div>
          <div className="text-xl">5.8%</div>
        </div>

        <div className="bg-gray-100 rounded-xl p-2 flex justify-between items-center h-[53px]">
          <div>
            <p className="font-medium text-[14px]" >Vitamin D</p>
            <div className="flex items-center text-amber-600">
              <Image
                src="/outpatient/Warning.svg"
                alt="Diagnoses"
                width={16}
                height={16}
                className="w-4 h-4"
              />
              <span className='pl-1 text-[#CA8A03] text-[12px]'>Low</span>
            </div>
          </div>
          <div className="text-xl">18 ng/mL</div>
        </div>

        <div className="bg-gray-100 rounded-lg p-2 flex justify-between items-center h-[53px]">
          <div>
            <p className="font-medium text-[14px]">LDL Cholesterol</p>
            <div className="flex items-center text-amber-600">
              <Image
                src="/outpatient/Warning.svg"
                alt="Diagnoses"
                width={16}
                height={16}
                className="w-4 h-4"
              />
              <span className='pl-1 text-[#CA8A03] text-[12px]'>Borderline High</span>
            </div>
          </div>
          <div className="text-xl">132 mg/dL</div>
        </div>
      </div>
    </div>
  );
}
