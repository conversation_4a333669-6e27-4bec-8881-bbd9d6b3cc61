"use client";

import "../globals.css";
import { Toaster } from "sonner";
import {
  getCurrentUser,
  fetchAuthSession,
  signOut,
  signInWithRedirect,
} from "@aws-amplify/auth";
import { Amplify } from "aws-amplify";
import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import { parseAmplifyConfig } from "aws-amplify/utils";
import { Hub } from "aws-amplify/utils";
import outputs from "@/amplify_outputs.json";

const amplifyConfig = parseAmplifyConfig(outputs);
Amplify.configure({
  ...amplifyConfig,
  API: {
    ...amplifyConfig.API,
    Events: {
      endpoint:
        "https://jo6vp743zjf6xdt4b6u5amf434.appsync-api.us-east-1.amazonaws.com/event",
      region: "us-east-1",
      defaultAuthMode: "apiKey",
      apiKey: "da2-vdhqso7unvdwxiubafebezhlxq",
    },
  },
});
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  const checkAuth = async () => {
    try {
      const session = await fetchAuthSession();
      if (session.tokens?.accessToken) {
        setIsAuthenticated(true);
      } else {
        console.log("No access token, redirecting to /login");
        setIsAuthenticated(false);
        router.push("/login");
      }
    } catch (error) {
      console.error("Error fetching auth session or user:", error);
      setIsAuthenticated(false);
      router.push("/login");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Check authentication status when component mounts
    checkAuth();

    // Set up authentication event listener
    const hubListener = Hub.listen("auth", ({ payload }) => {
      const { event } = payload;
      if (event === "signedOut") {
        setIsAuthenticated(false);
        router.push("/login");
      } else if (event === "signedIn") {
        checkAuth();
      }
    });

    return () => {
      hubListener();
    };
  }, [router]);

  const handleLogout = async () => {
    try {
      await signOut();
      setIsAuthenticated(false);
      router.push("/login");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await signInWithRedirect({ provider: "Google" });
    } catch (error) {
      console.error("Google Sign-In Error:", error);
      alert("Google Sign-In Error. Please try again.");
    }
  };

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-secondary"></div>
    );
  }

  if (!isAuthenticated) {
    // Don't show empty div, redirect to login page instead
    router.push("/login");
    return null;
  }

  return (
    <div
      className={`h-full overflow-hidden ${pathname === "/map" ? "m-0" : "m-[6px] md:m-0"}`}
    >
      {children}
      <Toaster />
    </div>
  );
}