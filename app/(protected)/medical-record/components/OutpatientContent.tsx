import Image from "next/image";
import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { type MedicalRecord } from "@/lib/services/oneUpHealthService";

// FHIR resource type definitions
interface FHIRReference {
  reference?: string;
  display?: string;
}

interface FHIRCoding {
  system?: string;
  code?: string;
  display?: string;
}

interface FHIRCodeableConcept {
  coding?: FHIRCoding[];
  text?: string;
}

interface FHIRLocation {
  location: FHIRReference;
  status?: string;
}

interface FHIRParticipant {
  type?: FHIRCodeableConcept[];
  individual?: FHIRReference;
}

interface FHIREncounter {
  resourceType: "Encounter";
  id?: string;
  status?: string;
  class?: FHIRCoding;
  type?: FHIRCodeableConcept[];
  subject?: FHIRReference;
  participant?: FHIRParticipant[];
  period?: {
    start?: string;
    end?: string;
  };
  reasonCode?: FHIRCodeableConcept[];
  location?: FHIRLocation[];
}

// Add Observation resource type for lab results
interface FHIRQuantity {
  value?: number;
  unit?: string;
  system?: string;
  code?: string;
}

interface FHIRReferenceRange {
  low?: FHIRQuantity;
  high?: FHIRQuantity;
  text?: string;
}

interface FHIRObservation {
  resourceType: "Observation";
  id?: string;
  status?: string;
  category?: FHIRCodeableConcept[];
  code?: FHIRCodeableConcept;
  subject?: FHIRReference;
  effectiveDateTime?: string;
  issued?: string;
  valueQuantity?: FHIRQuantity;
  valueString?: string;
  valueCodeableConcept?: FHIRCodeableConcept;
  interpretation?: FHIRCodeableConcept[];
  referenceRange?: FHIRReferenceRange[];
}

interface LabResult {
  id: string;
  name: string;
  value: string;
  unit: string;
  status: string;
  date: string;
  interpretation: "normal" | "high" | "low" | "critical" | "unknown";
  referenceRange?: string;
}

interface OutpatientContentProps {
  record?: MedicalRecord | null;
}

export default function OutpatientContent({ record }: OutpatientContentProps) {
  const [encounterData, setEncounterData] = useState<{
    location: string;
    doctor: string;
    summary: string;
    status: string;
    diagnoses: Array<{ label: string; code: string; type: string }>;
    startDate: string;
    endDate: string;
    type: string;
    reasonCodes: string[];
  }>({
    location: "Loading location...",
    doctor: "Loading provider...",
    summary: "Loading encounter details...",
    status: "-",
    diagnoses: [],
    startDate: "-",
    endDate: "-",
    type: "outpatient",
    reasonCodes: [],
  });

  // Add lab results state
  const [labResults, setLabResults] = useState<LabResult[]>([]);

  useEffect(() => {
    if (!record) return;

    console.log("Processing encounter record:", record);

    // Extract data from FHIR resource if available
    const fhirEncounter = record.rawData as FHIREncounter | undefined;

    // Deep logging of raw data structure to help debug lab results issue
    console.log("Full record object:", JSON.stringify(record, null, 2));

    // Parse data with fallbacks
    setEncounterData({
      location:
        record.location ||
        fhirEncounter?.location?.[0]?.location?.display ||
        "Unknown Location",

      doctor:
        record.doctor ||
        fhirEncounter?.participant?.find(
          (p) =>
            p.type?.[0]?.coding?.[0]?.code === "ATND" ||
            p.type?.[0]?.coding?.[0]?.display?.toLowerCase().includes("doctor")
        )?.individual?.display ||
        "Unknown Provider",

      summary:
        record.description ||
        fhirEncounter?.reasonCode?.[0]?.text ||
        fhirEncounter?.type?.[0]?.text ||
        "Outpatient visit",

      status: fhirEncounter?.status || "finished",

      diagnoses: [], // We'll populate this separately as it requires additional data

      startDate: fhirEncounter?.period?.start || record.date || "-",
      endDate: fhirEncounter?.period?.end || "-",

      type:
        fhirEncounter?.type?.[0]?.text ||
        fhirEncounter?.type?.[0]?.coding?.[0]?.display ||
        "Outpatient Visit",

      reasonCodes:
        (fhirEncounter?.reasonCode
          ?.map((r) => r.text || r.coding?.[0]?.display)
          .filter(Boolean) as string[]) || [],
    });

    // Enhanced lab results extraction
    try {
      console.log("Checking for lab results...");

      // Check multiple possible locations for lab data
      let labObservations: FHIRObservation[] = [];

      // 1. Check relatedResources array
      if (record.relatedResources && Array.isArray(record.relatedResources)) {
        console.log("Found relatedResources array, checking for lab data");

        const observations = record.relatedResources.filter(
          (resource) =>
            resource &&
            typeof resource === "object" &&
            "resourceType" in resource &&
            resource.resourceType === "Observation"
        );

        if (observations.length > 0) {
          console.log(
            `Found ${observations.length} observations in relatedResources`
          );
          labObservations = [
            ...labObservations,
            ...(observations as unknown as FHIRObservation[]),
          ];
        }
      }

      // 2. Check if the rawData itself is an array or contains an entries array (Bundle)
      if (record.rawData) {
        // Check if rawData is a Bundle with entries
        if (
          typeof record.rawData === "object" &&
          "entry" in record.rawData &&
          Array.isArray(record.rawData.entry)
        ) {
          console.log("Found Bundle with entries in rawData");

          const observations = record.rawData.entry
            .filter(
              (entry) =>
                entry &&
                typeof entry === "object" &&
                "resource" in entry &&
                entry.resource &&
                typeof entry.resource === "object" &&
                "resourceType" in entry.resource &&
                entry.resource.resourceType === "Observation"
            )
            .map((entry) => entry.resource);

          if (observations.length > 0) {
            console.log(
              `Found ${observations.length} observations in Bundle entries`
            );
            labObservations = [
              ...labObservations,
              ...(observations as FHIRObservation[]),
            ];
          }
        }

        // Check if rawData is an array itself
        if (Array.isArray(record.rawData)) {
          console.log("rawData is an array, checking for Observations");

          const observations = record.rawData.filter(
            (item) =>
              item &&
              typeof item === "object" &&
              "resourceType" in item &&
              item.resourceType === "Observation"
          );

          if (observations.length > 0) {
            console.log(
              `Found ${observations.length} observations in rawData array`
            );
            labObservations = [
              ...labObservations,
              ...(observations as FHIRObservation[]),
            ];
          }
        }

        // Check if rawData itself is an Observation
        if (
          typeof record.rawData === "object" &&
          "resourceType" in record.rawData &&
          record.rawData.resourceType === "Observation"
        ) {
          console.log("rawData is an Observation itself");
          labObservations.push(record.rawData as unknown as FHIRObservation);
        }
      }

      console.log("Found lab observations:", labObservations);

      if (labObservations.length > 0) {
        // Map observations to lab results
        const extractedLabs = labObservations.map((lab) => {
          // Try to extract a meaningful name for the lab test
          let labName = "Unknown Test";
          if (lab.code) {
            if (lab.code.text) {
              labName = lab.code.text;
            } else if (lab.code.coding && lab.code.coding.length > 0) {
              const coding = lab.code.coding[0];
              labName = coding.display || `Code: ${coding.code}` || labName;
            }
          }

          // Extract value with better handling of different formats
          let value = "";
          let unit = "";

          // Check for different value formats
          if (lab.valueQuantity) {
            value = lab.valueQuantity.value?.toString() || "";
            unit = lab.valueQuantity.unit || lab.valueQuantity.code || "";
          } else if (lab.valueString) {
            value = lab.valueString;
          } else if (lab.valueCodeableConcept) {
            value =
              lab.valueCodeableConcept.text ||
              (lab.valueCodeableConcept.coding &&
              lab.valueCodeableConcept.coding.length > 0
                ? lab.valueCodeableConcept.coding[0].display ||
                  lab.valueCodeableConcept.coding[0].code ||
                  ""
                : "");
          } else if ("component" in lab && Array.isArray(lab.component)) {
            // Some observations store results in components
            const components = lab.component.map((comp) => {
              const compName =
                comp.code?.text || comp.code?.coding?.[0]?.display || "";
              const compValue =
                comp.valueQuantity?.value?.toString() || comp.valueString || "";
              const compUnit =
                comp.valueQuantity?.unit || comp.valueQuantity?.code || "";
              return `${compName}: ${compValue} ${compUnit}`.trim();
            });
            value = components.join(", ");
          }

          if (!value) {
            value = "No value recorded";
          }

          // Determine interpretation
          let interpretation: LabResult["interpretation"] = "unknown";
          if (lab.interpretation && lab.interpretation.length > 0) {
            const code = lab.interpretation[0].coding?.[0]?.code?.toLowerCase();
            const display =
              lab.interpretation[0].coding?.[0]?.display?.toLowerCase() || "";

            if (code?.includes("normal") || display.includes("normal"))
              interpretation = "normal";
            else if (code?.includes("high") || display.includes("high"))
              interpretation = "high";
            else if (code?.includes("low") || display.includes("low"))
              interpretation = "low";
            else if (code?.includes("critical") || display.includes("crit"))
              interpretation = "critical";
          }

          // Reference range
          let referenceRange = undefined;
          if (lab.referenceRange && lab.referenceRange.length > 0) {
            const range = lab.referenceRange[0];
            if (range.text) {
              referenceRange = range.text;
            } else if (range.low || range.high) {
              const lowValue =
                range.low?.value !== undefined ? range.low.value : "";
              const highValue =
                range.high?.value !== undefined ? range.high.value : "";
              const unit = range.low?.unit || range.high?.unit || "";

              if (lowValue !== "" && highValue !== "") {
                referenceRange = `${lowValue}-${highValue} ${unit}`;
              } else if (lowValue !== "") {
                referenceRange = `>${lowValue} ${unit}`;
              } else if (highValue !== "") {
                referenceRange = `<${highValue} ${unit}`;
              }
            }
          }

          return {
            id: lab.id || `lab-${Math.random().toString(36).substr(2, 9)}`,
            name: labName,
            value,
            unit,
            status: lab.status || "unknown",
            date: lab.effectiveDateTime || lab.issued || record.date || "",
            interpretation,
            referenceRange,
          };
        });

        console.log("Extracted lab results:", extractedLabs);
        setLabResults(extractedLabs);
      } else {
        console.log("No lab observations found");
        // Set empty array instead of using demo data
        setLabResults([]);
      }
    } catch (error) {
      console.error("Error extracting lab results:", error);
      setLabResults([]);
    }

    // Extract real diagnoses from the FHIR data if available
    if (fhirEncounter?.reasonCode?.length) {
      const diagnoses = fhirEncounter.reasonCode.map((reason, index) => ({
        label:
          reason.text || reason.coding?.[0]?.display || "Unknown Diagnosis",
        code: reason.coding?.[0]?.code || "-",
        type: index === 0 ? "primary" : "secondary",
      }));

      setEncounterData((prev) => ({ ...prev, diagnoses }));
    } else {
      // No diagnoses found
      setEncounterData((prev) => ({ ...prev, diagnoses: [] }));
    }
  }, [record]);

  // Format dates nicely
  const formatDate = (dateString: string): string => {
    if (dateString === "-") return "-";
    try {
      return new Date(dateString).toLocaleDateString(undefined, {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch {
      // Return the original string if date parsing fails
      return dateString;
    }
  };

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="show"
    >
      {/* Encounter Summary */}
      <motion.div
        className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
        variants={itemVariants}
      >
        <div className="flex items-center mb-3">
          <div className="text-orange-500 mr-2">
            <Image
              src="/outpatient/heart-rate-monitor.svg"
              alt="Encounter Summary"
              width={24}
              height={24}
              className="w-6 h-6"
            />
          </div>
          <h2 className="text-[#FF4901] font-medium">Encounter Summary</h2>
        </div>

        <p className="text-gray-700 text-[14px]">{encounterData.summary}</p>

        {/* Additional encounter details */}
        <div className="mt-4 space-y-2">
          <div className="flex items-center text-gray-600 text-sm">
            <Image
              src="/medical-record/map-marker.svg"
              alt="Location"
              width={16}
              height={16}
              className="w-4 h-4 mr-2"
            />
            {encounterData.location}
          </div>
          <div className="flex items-center text-gray-600 text-sm">
            <Image
              src="/menu-deepg.svg"
              alt="Doctor"
              width={16}
              height={16}
              className="w-4 h-4 mr-2"
            />
            {encounterData.doctor}
          </div>
          <div className="flex justify-between text-sm pt-1">
            <span className="text-gray-500">Visit Type:</span>
            <span className="font-medium">{encounterData.type}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Status:</span>
            <span className="font-medium">
              {encounterData.status.charAt(0).toUpperCase() +
                encounterData.status.slice(1)}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Date:</span>
            <span className="font-medium">
              {formatDate(encounterData.startDate)}
            </span>
          </div>
          {encounterData.endDate !== "-" && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">End Date:</span>
              <span className="font-medium">
                {formatDate(encounterData.endDate)}
              </span>
            </div>
          )}
        </div>
      </motion.div>

      {/* Diagnoses - Only show if there are diagnoses */}
      {encounterData.diagnoses.length > 0 && (
        <motion.div
          className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
          variants={itemVariants}
        >
          <div className="flex items-center mb-3">
            <div className="text-pink-500 mr-2">
              <Image
                src="/outpatient/pinkheart.svg"
                alt="Diagnoses"
                width={24}
                height={24}
                className="w-6 h-6"
              />
            </div>
            <h2 className="text-[#FF899E] font-medium">Diagnoses</h2>
          </div>

          <motion.div
            className="space-y-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.4 }}
          >
            {encounterData.diagnoses.map((diagnosis, index) => (
              <motion.div
                key={index}
                className="bg-gray-100 rounded-lg p-3 flex items-center"
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 + index * 0.1, duration: 0.3 }}
              >
                <span
                  className={`${
                    diagnosis.type === "primary"
                      ? "bg-blue-100 text-blue-800"
                      : "bg-teal-100 text-teal-800"
                  } text-xs px-3 py-1 rounded-md mr-3`}
                >
                  {diagnosis.type === "primary" ? "Primary" : "Secondary"}
                </span>
                <p className="text-[14px]">
                  {diagnosis.label}
                  {diagnosis.code && diagnosis.code !== "-" && (
                    <span className="text-gray-500"> ({diagnosis.code})</span>
                  )}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      )}

      {/* Reason Codes - Only show if there are reason codes */}
      {encounterData.reasonCodes.length > 0 && (
        <motion.div
          className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
          variants={itemVariants}
        >
          <div className="flex items-center mb-3">
            <div className="text-indigo-500 mr-2">
              <Image
                src="/outpatient/inspect.svg"
                alt="Reasons"
                width={24}
                height={24}
                className="w-6 h-6"
              />
            </div>
            <h2 className="text-indigo-500 font-medium">Reasons for Visit</h2>
          </div>

          <div className="space-y-2">
            {encounterData.reasonCodes.map((reason, index) => (
              <div key={index} className="bg-gray-100 rounded-lg p-3">
                <p className="text-sm">{reason}</p>
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Labs & Test Results - Only show if there are actual lab results */}
      {labResults.length > 0 && (
        <motion.div
          className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
          variants={itemVariants}
        >
          <div className="flex items-center mb-3">
            <div className="text-teal-500 mr-2">
              <Image
                src="/outpatient/inspect.svg"
                alt="Labs & Test Results"
                width={24}
                height={24}
                className="w-6 h-6"
              />
            </div>
            <h2 className="text-[#19C5BB] font-medium">Labs & Test Results</h2>
          </div>

          <div className="space-y-4">
            {labResults.map((lab) => (
              <div key={lab.id} className="bg-gray-100 rounded-xl p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-medium text-lg">{lab.name}</h3>
                  <div className="text-xl font-medium">
                    {lab.value} {lab.unit}
                  </div>
                </div>

                <div className="flex justify-between text-sm mb-2">
                  <span className="text-gray-500">Date</span>
                  <span>{formatDate(lab.date)}</span>
                </div>

                <div className="flex items-center mt-3">
                  {lab.interpretation === "normal" ? (
                    <div className="flex items-center px-3 py-1 bg-green-100 rounded-full">
                      <Image
                        src="/outpatient/check.svg"
                        alt="Normal"
                        width={16}
                        height={16}
                        className="w-4 h-4 mr-1"
                      />
                      <span className="text-[12px] text-green-700 font-medium">
                        Normal
                      </span>
                    </div>
                  ) : lab.interpretation === "high" ? (
                    <div className="flex items-center px-3 py-1 bg-yellow-100 rounded-full">
                      <Image
                        src="/outpatient/Warning.svg"
                        alt="High"
                        width={16}
                        height={16}
                        className="w-4 h-4 mr-1"
                      />
                      <span className="text-[12px] text-yellow-700 font-medium">
                        High
                      </span>
                    </div>
                  ) : lab.interpretation === "low" ? (
                    <div className="flex items-center px-3 py-1 bg-yellow-100 rounded-full">
                      <Image
                        src="/outpatient/Warning.svg"
                        alt="Low"
                        width={16}
                        height={16}
                        className="w-4 h-4 mr-1"
                      />
                      <span className="text-[12px] text-yellow-700 font-medium">
                        Low
                      </span>
                    </div>
                  ) : lab.interpretation === "critical" ? (
                    <div className="flex items-center px-3 py-1 bg-red-100 rounded-full">
                      <Image
                        src="/outpatient/Warning.svg"
                        alt="Critical"
                        width={16}
                        height={16}
                        className="w-4 h-4 mr-1"
                      />
                      <span className="text-[12px] text-red-700 font-medium">
                        Critical
                      </span>
                    </div>
                  ) : (
                    <div className="flex items-center px-3 py-1 bg-gray-100 rounded-full">
                      <span className="text-[12px] text-gray-500">
                        No interpretation
                      </span>
                    </div>
                  )}

                  {lab.referenceRange && (
                    <div className="ml-3 text-gray-500 text-xs">
                      Reference Range: {lab.referenceRange}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Debug information for development */}
          {
            <details className="mt-4 text-xs border-t border-gray-200 pt-3">
              <summary className="cursor-pointer font-medium text-blue-500">
                Debug Lab Data
              </summary>
              <div className="mt-2 max-h-48 overflow-auto bg-gray-50 p-2 rounded text-xs">
                <pre>{JSON.stringify(labResults, null, 2)}</pre>
              </div>
            </details>
          }
        </motion.div>
      )}

      {/* Remove the static medication section that always shows demo data */}

      {/* Raw Data for Development */}
      {record?.rawData && (
        <motion.div
          className="bg-gray-100 p-4 rounded-lg mt-6 text-xs"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
        >
          <details>
            <summary className="cursor-pointer font-medium mb-2">
              Raw FHIR Data (Dev Only)
            </summary>
            <pre className="overflow-auto max-h-96">
              {JSON.stringify(record.rawData, null, 2)}
            </pre>
          </details>
        </motion.div>
      )}
    </motion.div>
  );
}
