import Image from "next/image";
import { motion, Variants } from "framer-motion";
import { useState, useEffect } from "react";
import { type MedicalRecord } from "@/lib/services/oneUpHealthService";

// Add FHIR type definitions
interface FHIRCoding {
  system?: string;
  code?: string;
  display?: string;
}

interface FHIRCodeableConcept {
  coding?: FHIRCoding[];
  text?: string;
}

interface FHIRReference {
  reference?: string;
  display?: string;
}

interface FHIRReaction {
  description?: string;
  severity?: string;
  manifestation?: FHIRCodeableConcept[];
}

interface FHIRAllergyIntolerance {
  id?: string;
  clinicalStatus?: FHIRCodeableConcept;
  verificationStatus?: FHIRCodeableConcept;
  type?: string;
  category?: string[];
  criticality?: string;
  code?: FHIRCodeableConcept;
  patient?: FHIRReference;
  recorder?: FHIRReference;
  recordedDate?: string;
  onsetDateTime?: string; // Add the missing property
  reaction?: FHIRReaction[];
}

interface MedicationReactionContentProps {
  record?: MedicalRecord;
}

export default function MedicationReactionContent({
  record,
}: MedicationReactionContentProps) {
  const [reactionData, setReactionData] = useState<{
    allergen: string;
    description: string;
    severity: string;
    status: string;
    verification: string;
    manifestations: string[];
    recordedDate: string;
    recorder: string;
    category: string;
    criticality: string;
  }>({
    allergen: "Unknown Allergen",
    description: "Loading reaction details...",
    severity: "mild",
    status: "active",
    verification: "confirmed",
    manifestations: [],
    recordedDate: "-",
    recorder: "-",
    category: "-",
    criticality: "low",
  });

  useEffect(() => {
    if (!record) return;

    console.log("Processing reaction record:", record);

    // Extract data from FHIR resource if available
    const allergyData = record.rawData as FHIRAllergyIntolerance | undefined;

    if (allergyData) {
      console.log("FHIR Allergy data:", allergyData);

      try {
        // Extract allergen information with proper fallbacks
        const allergenText =
          allergyData.code?.text ||
          allergyData.code?.coding?.find((c) => c.display)?.display ||
          "Unknown Allergen";

        // Get the coding for RxNorm if available (for medication allergies)
        const rxNormCoding = allergyData.code?.coding?.find(
          (c) =>
            c.system?.includes("rxnorm") || c.system?.includes("nlm.nih.gov")
        );

        // Create combined allergen display
        const allergenDisplay = rxNormCoding?.display
          ? `${allergenText} (${rxNormCoding.code})`
          : allergenText;

        // Extract reaction manifestations
        const manifestations =
          allergyData.reaction?.[0]?.manifestation?.map(
            (m) => m.text || m.coding?.[0]?.display || "Unknown manifestation"
          ) || [];

        // Get the reaction description with fallbacks
        const reactionDescription =
          record.description ||
          allergyData.reaction?.[0]?.description ||
          manifestations.join(", ") ||
          `Patient has a reported allergy to ${allergenText}`;

        // Extract all relevant data with fallbacks
        setReactionData({
          allergen: allergenDisplay,
          description: reactionDescription,
          severity: allergyData.reaction?.[0]?.severity || "mild",
          status:
            allergyData.clinicalStatus?.coding?.[0]?.display ||
            allergyData.clinicalStatus?.coding?.[0]?.code ||
            "active",
          verification:
            allergyData.verificationStatus?.coding?.[0]?.display ||
            allergyData.verificationStatus?.coding?.[0]?.code ||
            "confirmed",
          manifestations,
          recordedDate:
            allergyData.recordedDate ||
            allergyData.onsetDateTime ||
            record.date ||
            "-",
          recorder:
            allergyData.recorder?.display ||
            record.doctor ||
            "Healthcare Provider",
          category: allergyData.category?.[0] || "medication",
          criticality: allergyData.criticality || "low",
        });
      } catch (error) {
        console.error("Error extracting allergy data:", error);
        setDefaultReactionData(record);
      }
    } else {
      console.log("No FHIR data available, using record data");
      setDefaultReactionData(record);
    }
  }, [record]);

  // Helper function to set default data when FHIR data is not available
  const setDefaultReactionData = (record: MedicalRecord) => {
    // Only use actual record data, no mock/default values
    setReactionData({
      allergen: record.description || "No allergen information available",
      description: record.description || "No reaction description available",
      severity: "-",
      status: "-",
      verification: "-",
      manifestations: [],
      recordedDate: record.date || "-",
      recorder: record.doctor || "-",
      category: "-",
      criticality: "-",
    });
  };

  // Enhance the formatDate function to handle different date formats
  const formatDate = (dateString: string): string => {
    if (!dateString || dateString === "-") return "Unknown date";

    try {
      // Handle ISO dates
      if (dateString.includes("T")) {
        return new Date(dateString).toLocaleDateString(undefined, {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
      }

      // Handle just date part
      return new Date(dateString).toLocaleDateString(undefined, {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (e) {
      console.warn("Date formatting error:", e);
      return dateString;
    }
  };

  // Function to render severity indicator
  const renderSeverityIndicator = (level: string | undefined) => {
    if (!level || level === "-") {
      // Handle undefined or empty case
      return (
        <div className="flex items-center">
          <span className="inline-block w-3 h-3 rounded-full bg-gray-400 mr-1.5"></span>
          <span className="font-medium text-gray-600">Unknown</span>
        </div>
      );
    }

    const levelLower = level.toLowerCase();
    if (levelLower.includes("high") || levelLower.includes("severe")) {
      return (
        <div className="flex items-center">
          <span className="inline-block w-3 h-3 rounded-full bg-red-500 mr-1.5"></span>
          <span className="font-medium text-red-600">
            {level.charAt(0).toUpperCase() + level.slice(1)}
          </span>
        </div>
      );
    } else if (levelLower.includes("moderate")) {
      return (
        <div className="flex items-center">
          <span className="inline-block w-3 h-3 rounded-full bg-yellow-500 mr-1.5"></span>
          <span className="font-medium text-yellow-600">
            {level.charAt(0).toUpperCase() + level.slice(1)}
          </span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center">
          <span className="inline-block w-3 h-3 rounded-full bg-green-500 mr-1.5"></span>
          <span className="font-medium text-green-600">
            {level.charAt(0).toUpperCase() + level.slice(1)}
          </span>
        </div>
      );
    }
  };

  // Animation variants for staggered animations
  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="show"
    >
      {/* Reaction Summary */}
      <motion.div
        className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
        variants={itemVariants}
      >
        <motion.div
          className="flex items-center mb-3"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="text-[#FF4901] mr-2">
            <Image
              src="/outpatient/heart-rate-monitor.svg"
              alt="Medication Reaction"
              width={24}
              height={24}
              className="w-6 h-6"
            />
          </div>
          <h2 className="text-[#FF4901] font-medium">Medication Reaction</h2>
        </motion.div>

        <motion.p
          className="text-gray-700 text-[14px] mb-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          {reactionData.description}
        </motion.p>

        <motion.div
          className="space-y-3 mt-4 pt-3 border-t border-gray-100"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-500">Severity:</span>
            {renderSeverityIndicator(reactionData.severity)}
          </div>

          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-500">Criticality:</span>
            {renderSeverityIndicator(reactionData.criticality)}
          </div>

          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-500">Clinical Status:</span>
            <span
              className={`font-medium ${
                reactionData.status.toLowerCase().includes("active")
                  ? "text-red-600"
                  : "text-green-600"
              }`}
            >
              {reactionData.status.charAt(0).toUpperCase() +
                reactionData.status.slice(1).toLowerCase().replace("-", " ")}
            </span>
          </div>

          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Recorded Date:</span>
            <span className="font-medium">
              {formatDate(reactionData.recordedDate)}
            </span>
          </div>
        </motion.div>
      </motion.div>

      {/* Coding References */}
      {record?.rawData &&
      typeof record.rawData === "object" &&
      record.rawData !== null &&
      "code" in record.rawData &&
      record.rawData.code &&
      typeof record.rawData.code === "object" &&
      "coding" in record.rawData.code &&
      Array.isArray(record.rawData.code.coding) &&
      record.rawData.code.coding.length > 0 ? (
        <motion.div
          className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
          variants={itemVariants}
        >
          <div className="flex items-center mb-3">
            <div className="text-purple-500 mr-2">
              <Image
                src="/medical-record/note.svg"
                alt="Coding Systems"
                width={24}
                height={24}
                className="w-6 h-6"
              />
            </div>
            <h2 className="text-purple-500 font-medium">Coding References</h2>
          </div>

          <div className="space-y-2 text-sm">
            {(record.rawData.code.coding as FHIRCoding[]).map(
              (coding, index) => (
                <div key={index} className="bg-gray-100 rounded-lg p-3">
                  <p className="text-xs text-gray-500">{coding.system}</p>
                  <p className="font-medium">
                    {coding.code} {coding.display && `- ${coding.display}`}
                  </p>
                </div>
              )
            )}
          </div>
        </motion.div>
      ) : null}

      {/* Raw FHIR Data (Dev Only) */}
      {record?.rawData ? (
        <motion.div
          className="bg-gray-100 p-4 rounded-lg mt-6 text-xs"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
        >
          <details>
            <summary className="cursor-pointer font-medium mb-2">
              Raw FHIR Data (Dev Only)
            </summary>
            <pre className="overflow-auto max-h-96">
              {JSON.stringify(record.rawData, null, 2)}
            </pre>
          </details>
        </motion.div>
      ) : null}
    </motion.div>
  );
}
