import Image from "next/image";
import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { type MedicalRecord } from "@/lib/services/oneUpHealthService";

// Define FHIR types for MedicationRequest
interface FHIRCoding {
  system?: string;
  code?: string;
  display?: string;
}

interface FHIRCodeableConcept {
  coding?: FHIRCoding[];
  text?: string;
}

interface FHIRReference {
  reference?: string;
  display?: string;
  type?: string;
  identifier?: {
    system?: string;
    use?: string;
    value?: string;
  };
}

interface FHIRQuantity {
  value?: number;
  unit?: string;
  system?: string;
  code?: string;
}

interface FHIRDosageInstruction {
  route?: FHIRCodeableConcept;
  method?: FHIRCodeableConcept;
  timing?: {
    code?: {
      text?: string;
    };
    repeat?: {
      count?: number;
      boundsPeriod?: {
        start?: string;
        end?: string;
      };
    };
  };
  asNeededBoolean?: boolean;
  text?: string;
  doseAndRate?: Array<{
    doseQuantity?: FHIRQuantity;
    type?: FHIRCodeableConcept;
  }>;
  patientInstruction?: string;
}

interface FHIRDispenseRequest {
  validityPeriod?: {
    start?: string;
    end?: string;
  };
  quantity?: FHIRQuantity;
  numberOfRepeatsAllowed?: number;
  expectedSupplyDuration?: FHIRQuantity;
}

interface FHIRMedicationRequest {
  resourceType: string;
  id?: string;
  status?: string;
  intent?: string;
  medicationReference?: FHIRReference;
  medicationCodeableConcept?: FHIRCodeableConcept;
  subject?: FHIRReference;
  encounter?: FHIRReference;
  authoredOn?: string;
  requester?: FHIRReference;
  recorder?: FHIRReference;
  courseOfTherapyType?: FHIRCodeableConcept;
  dosageInstruction?: FHIRDosageInstruction[];
  dispenseRequest?: FHIRDispenseRequest;
  category?: FHIRCodeableConcept[];
  identifier?: Array<{
    system?: string;
    use?: string;
    value?: string;
  }>;
}

interface MedicationContentProps {
  record?: MedicalRecord;
}

export default function MedicationContent({ record }: MedicationContentProps) {
  const [medicationData, setMedicationData] = useState<{
    medicationName: string;
    status: string;
    intent: string;
    therapyType: string;
    route: string;
    method: string;
    dosage: string;
    schedule: string;
    startDate: string;
    endDate: string;
    instructions: string;
    prescriber: string;
    recorder: string;
    quantity: string;
    supplyDuration: string;
    category: string;
  }>({
    medicationName: "Loading medication details...",
    status: "-",
    intent: "-",
    therapyType: "-",
    route: "-",
    method: "-",
    dosage: "-",
    schedule: "-",
    startDate: "-",
    endDate: "-",
    instructions: "-",
    prescriber: "-",
    recorder: "-",
    quantity: "-",
    supplyDuration: "-",
    category: "-",
  });

  useEffect(() => {
    if (!record) return;

    console.log("Processing medication record:", record);

    // Extract data from FHIR resource if available
    const medicationRequest = record.rawData as
      | FHIRMedicationRequest
      | undefined;

    if (medicationRequest) {
      console.log("FHIR MedicationRequest data:", medicationRequest);

      try {
        // Extract medication name
        const medicationName =
          medicationRequest.medicationReference?.display ||
          medicationRequest.medicationCodeableConcept?.text ||
          record.description ||
          "Unknown medication";

        // Get primary dosage information
        const dosageInstruction = medicationRequest.dosageInstruction?.[0];
        const doseQuantity = dosageInstruction?.doseAndRate?.[0]?.doseQuantity;

        // Format dosage
        const dosage = doseQuantity
          ? `${doseQuantity.value} ${doseQuantity.unit || ""}`
          : "-";

        // Calculate schedule
        let schedule = "-";
        if (dosageInstruction?.timing?.code?.text) {
          schedule = dosageInstruction.timing.code.text;
        } else if (dosageInstruction?.timing?.repeat?.count) {
          schedule = `${dosageInstruction.timing.repeat.count} time(s)`;
        }

        // Get dates
        const startDate =
          dosageInstruction?.timing?.repeat?.boundsPeriod?.start ||
          medicationRequest.authoredOn ||
          medicationRequest.dispenseRequest?.validityPeriod?.start ||
          record.date ||
          "-";

        const endDate =
          dosageInstruction?.timing?.repeat?.boundsPeriod?.end ||
          medicationRequest.dispenseRequest?.validityPeriod?.end ||
          "-";

        // Extract category
        const category =
          medicationRequest.category?.[0]?.text ||
          medicationRequest.category?.[0]?.coding?.[0]?.display ||
          "-";

        // Update state with medication data
        setMedicationData({
          medicationName,
          status: medicationRequest.status || "-",
          intent: medicationRequest.intent || "-",
          therapyType:
            medicationRequest.courseOfTherapyType?.text ||
            medicationRequest.courseOfTherapyType?.coding?.[0]?.display ||
            "-",
          route:
            dosageInstruction?.route?.text ||
            dosageInstruction?.route?.coding?.[0]?.display ||
            "-",
          method:
            dosageInstruction?.method?.text ||
            dosageInstruction?.method?.coding?.[0]?.display ||
            "-",
          dosage,
          schedule,
          startDate,
          endDate,
          instructions:
            dosageInstruction?.patientInstruction ||
            dosageInstruction?.text ||
            "-",
          prescriber: medicationRequest.requester?.display || "-",
          recorder: medicationRequest.recorder?.display || "-",
          quantity: medicationRequest.dispenseRequest?.quantity
            ? `${medicationRequest.dispenseRequest.quantity.value} ${medicationRequest.dispenseRequest.quantity.unit || ""}`
            : "-",
          supplyDuration: medicationRequest.dispenseRequest
            ?.expectedSupplyDuration
            ? `${medicationRequest.dispenseRequest.expectedSupplyDuration.value} ${medicationRequest.dispenseRequest.expectedSupplyDuration.unit || ""}`
            : "-",
          category,
        });
      } catch (error) {
        console.error("Error extracting medication data:", error);
        setDefaultMedicationData(record);
      }
    } else {
      console.log("No FHIR data available, using record data");
      setDefaultMedicationData(record);
    }
  }, [record]);

  // Set default medication data when FHIR data is not available
  const setDefaultMedicationData = (record: MedicalRecord) => {
    setMedicationData({
      medicationName: record.description || "Unknown medication",
      status: "active",
      intent: "order",
      therapyType: "Regular",
      route: "-",
      method: "-",
      dosage: "-",
      schedule: "-",
      startDate: record.date || "-",
      endDate: "-",
      instructions: "-",
      prescriber: record.doctor || "-",
      recorder: record.doctor || "-",
      quantity: "-",
      supplyDuration: "-",
      category: "-",
    });
  };

  // Format dates nicely
  const formatDate = (dateString: string): string => {
    if (!dateString || dateString === "-") return "Unknown date";

    try {
      // Handle ISO dates
      if (dateString.includes("T")) {
        return new Date(dateString).toLocaleDateString(undefined, {
          year: "numeric",
          month: "long",
          day: "numeric",
        });
      }

      // Handle just date part
      return new Date(dateString).toLocaleDateString(undefined, {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    } catch (e) {
      console.warn("Date formatting error:", e);
      return dateString;
    }
  };

  // Get status color based on medication status
  const getStatusColor = (status: string): string => {
    const statusLower = status.toLowerCase();

    if (statusLower === "active" || statusLower === "on-hold") {
      return "text-green-600";
    } else if (statusLower === "completed") {
      return "text-blue-600";
    } else if (statusLower === "stopped" || statusLower === "cancelled") {
      return "text-red-600";
    } else if (statusLower === "unknown") {
      return "text-gray-600";
    } else {
      return "text-yellow-600";
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="show"
    >
      {/* Medication Summary */}
      <motion.div
        className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
        variants={itemVariants}
      >
        <motion.div
          className="flex items-center mb-3"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="text-blue-500 mr-2">
            <Image
              src="/outpatient/bluecapsule.svg"
              alt="Medication"
              width={24}
              height={24}
              className="w-6 h-6"
            />
          </div>
          <h2 className="text-blue-500 font-medium">Medication Summary</h2>
        </motion.div>

        <motion.div
          className="mb-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        >
          <h3 className="font-medium text-xl text-gray-800 mb-1">
            {medicationData.medicationName}
          </h3>
          <div className="flex items-center">
            <span
              className={`font-medium ${getStatusColor(medicationData.status)}`}
            >
              {medicationData.status.charAt(0).toUpperCase() +
                medicationData.status.slice(1)}
            </span>
            {medicationData.category !== "-" && (
              <span className="ml-2 text-sm text-gray-500">
                • {medicationData.category}
              </span>
            )}
          </div>
        </motion.div>

        <motion.div
          className="space-y-3 pt-3 border-t border-gray-100"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.3 }}
        >
          {medicationData.intent !== "-" && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Intent:</span>
              <span className="font-medium">
                {medicationData.intent.charAt(0).toUpperCase() +
                  medicationData.intent.slice(1)}
              </span>
            </div>
          )}

          {medicationData.therapyType !== "-" && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Therapy Type:</span>
              <span className="font-medium">{medicationData.therapyType}</span>
            </div>
          )}

          <div className="flex justify-between text-sm">
            <span className="text-gray-500">Start Date:</span>
            <span className="font-medium">
              {formatDate(medicationData.startDate)}
            </span>
          </div>

          {medicationData.endDate !== "-" && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">End Date:</span>
              <span className="font-medium">
                {formatDate(medicationData.endDate)}
              </span>
            </div>
          )}

          {medicationData.prescriber !== "-" && (
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Prescribed By:</span>
              <span className="font-medium">{medicationData.prescriber}</span>
            </div>
          )}
        </motion.div>
      </motion.div>

      {/* Dosage Information */}
      <motion.div
        className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
        variants={itemVariants}
      >
        <motion.div
          className="flex items-center mb-3"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <div className="text-[#EC4899] mr-2">
            <Image
              src="/outpatient/pinkheart.svg"
              alt="Dosage"
              width={24}
              height={24}
              className="w-6 h-6"
            />
          </div>
          <h2 className="text-[#EC4899] font-medium">Dosage Information</h2>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4, delay: 0.4 }}
        >
          {medicationData.dosage !== "-" && (
            <div className="bg-gray-100 rounded-lg p-3">
              <p className="text-sm text-[#EC4899]">Dosage</p>
              <p className="font-medium">{medicationData.dosage}</p>
            </div>
          )}

          {medicationData.route !== "-" && (
            <div className="bg-gray-100 rounded-lg p-3">
              <p className="text-sm text-[#6466F1]">Route</p>
              <p className="font-medium">{medicationData.route}</p>
            </div>
          )}

          {medicationData.method !== "-" && (
            <div className="bg-gray-100 rounded-lg p-3">
              <p className="text-sm text-[#27A8E4]">Method</p>
              <p className="font-medium">{medicationData.method}</p>
            </div>
          )}

          {medicationData.schedule !== "-" && (
            <div className="bg-gray-100 rounded-lg p-3">
              <p className="text-sm text-[#19C5BB]">Schedule</p>
              <p className="font-medium">{medicationData.schedule}</p>
            </div>
          )}
        </motion.div>

        {medicationData.quantity !== "-" && (
          <motion.div
            className="bg-gray-100 rounded-lg p-3 mb-4"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
          >
            <p className="text-sm text-[#FF4901]">Quantity</p>
            <p className="font-medium">{medicationData.quantity}</p>
          </motion.div>
        )}

        {medicationData.supplyDuration !== "-" && (
          <motion.div
            className="bg-gray-100 rounded-lg p-3 mb-4"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.6 }}
          >
            <p className="text-sm text-[#FF899E]">Supply Duration</p>
            <p className="font-medium">{medicationData.supplyDuration}</p>
          </motion.div>
        )}
      </motion.div>

      {/* Patient Instructions */}
      {medicationData.instructions !== "-" && (
        <motion.div
          className="bg-white rounded-3xl p-5 border border-gray-100 shadow-sm"
          variants={itemVariants}
        >
          <motion.div
            className="flex items-center mb-3"
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
          >
            <div className="text-green-500 mr-2">
              <Image
                src="/medical-record/note.svg"
                alt="Instructions"
                width={24}
                height={24}
                className="w-6 h-6"
              />
            </div>
            <h2 className="text-green-500 font-medium">Patient Instructions</h2>
          </motion.div>

          <motion.div
            className="bg-gray-100 rounded-lg p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.6 }}
          >
            <p className="text-sm">{medicationData.instructions}</p>
          </motion.div>
        </motion.div>
      )}

      {/* Raw Data for Development */}
      {record?.rawData && (
        <motion.div
          className="bg-gray-100 p-4 rounded-lg mt-6 text-xs"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.7 }}
        >
          <details>
            <summary className="cursor-pointer font-medium mb-2">
              Raw FHIR Data (Dev Only)
            </summary>
            <pre className="overflow-auto max-h-96">
              {JSON.stringify(record.rawData, null, 2)}
            </pre>
          </details>
        </motion.div>
      )}
    </motion.div>
  );
}
