"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Image from "next/image";
import { useWindowSize } from "@/hooks/useWindowSize";
import MedicalRecordContent from "@/components/Health/MedicalRecordContent";
import MedicalRecordModal from "@/components/Health/MedicalRecordModal";
import {
  OneUpHealthService,
  type PatientData,
  type MedicalRecord,
} from "@/lib/services/oneUpHealthService";
import { useUser } from "@/hooks/useUsers";
import { useMedicalRecord } from "@/hooks/useMedicalRecord";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { toast } from "sonner";

const client = generateClient<Schema>();

export default function MedicalRecord() {
  const [activeTab, setActiveTab] = useState("All");
  const router = useRouter();
  const searchParams = useSearchParams();
  const isDesktop = useWindowSize();
  const [modalOpen, setModalOpen] = useState(false);
  const [activeRecord, setActiveRecord] = useState<null | MedicalRecord>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [oneUpService, setOneUpService] = useState<OneUpHealthService | null>(
    null
  );

  const [patientData, setPatientData] = useState<PatientData | null>(null);
  const [medicalRecords, setMedicalRecords] = useState<MedicalRecord[]>([]);
  const [userInfo, setUserInfo] = useState({
    name: "-",
    birthDate: "-",
    birthYear: "-",
    gender: "-",
    region: "-",
    race: "-",
    ethnicity: "-",
    maritalStatus: "-",
    email: "-",
    mobilePhone: "-",
    workPhone: "-",
    address: "-",
    primaryLanguage: "-",
    secondaryLanguage: "-",
  });

  const { currentUser, loading: userLoading } = useUser();

  // Add debug to check the actual value
  useEffect(() => {
    if (currentUser) {
      console.log("Current user health connection status:", currentUser);
    }
  }, [currentUser]);

  // Check if we should open the modal from success page
  useEffect(() => {
    if (isDesktop && searchParams?.get("openModal") === "true") {
      // Remove the query parameter to avoid reopening on refresh
      const url = new URL(window.location.href);
      url.searchParams.delete("openModal");
      window.history.replaceState({}, "", url.toString());

      // Set a small timeout to ensure data is loaded
      setTimeout(() => {
        setModalOpen(true);
      }, 500);
    }
  }, [isDesktop, searchParams]);

  useEffect(() => {
    if (!currentUser || userLoading) return;

    const config = { userId: currentUser.userId };
    const service = new OneUpHealthService(config);
    setOneUpService(service);

    // Check for either property name that might exist
    const isHealthConnected = currentUser.is_1healthup_connected === true;

    // Always try to load patient data if the user exists
    loadPatientData(service);
  }, [currentUser, userLoading]);

  const loadPatientData = async (service: OneUpHealthService) => {
    try {
      setIsLoading(true);

      const patient = await service.fetchPatientDataFromDatabase();
      const records = await service.fetchMedicalRecordsFromDatabase();

      if (patient) {
        setPatientData(patient);
        setUserInfo({
          name: patient.name || "-",
          birthDate: patient.birthDate || "-",
          birthYear: patient.birthYear || "-",
          gender: patient.gender || "-",
          region: patient.region || "-",
          race: patient.race || "-",
          ethnicity: patient.ethnicity || "-",
          maritalStatus: (patient as any).maritalStatus || "-",
          email: patient.email || "-",
          mobilePhone: (patient as any).mobilePhone || "-",
          workPhone: (patient as any).workPhone || "-",
          address: patient.address || "-",
          primaryLanguage: (patient as any).primaryLanguage || "-",
          secondaryLanguage: (patient as any).secondaryLanguage || "-",
        });
      }

      setMedicalRecords(records);
    } catch (error) {
      console.error("Failed to load patient data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSyncMedicalData = async () => {
    if (!currentUser) {
      toast.error("User not authenticated");
      return;
    }

    try {
      setIsSyncing(true);
      toast.info("Syncing medical data from 1upHealth...");

      const response = await client.mutations.syncMedicalData({});

      if (response.data) {
        const syncResponse = response.data as unknown as {
          success?: boolean;
          data?: { syncedRecords?: number; message?: string };
          error?: string;
        };

        if (syncResponse.success) {
          const syncedCount = syncResponse.data?.syncedRecords || 0;
          toast.success(
            `Successfully synced ${syncedCount} medical records to database`
          );

          if (oneUpService) {
            await loadPatientData(oneUpService);
          }
        } else {
          toast.error(syncResponse.error || "Sync failed");
        }
      }
    } catch (error) {
      console.error("Error syncing medical data:", error);
      toast.error("Failed to sync medical data");
    } finally {
      setIsSyncing(false);
    }
  };

  const { setSelectedRecord } = useMedicalRecord();

  const handleRecordClick = (record: MedicalRecord) => {
    if (record.isClickable) {
      if (isDesktop) {
        setActiveRecord(record);
        setModalOpen(true);
      } else {
        setSelectedRecord(record);
        router.push("/outpatient");
      }
    }
  };

  const filteredRecords = medicalRecords.filter((record) => {
    if (activeTab === "All") return true;

    const recordDate = new Date(record.date);
    const now = new Date();

    switch (activeTab) {
      case "Day":
        return recordDate.toDateString() === now.toDateString();
      case "Month":
        return (
          recordDate.getMonth() === now.getMonth() &&
          recordDate.getFullYear() === now.getFullYear()
        );
      case "Year":
        return recordDate.getFullYear() === now.getFullYear();
      default:
        return true;
    }
  });

  if (userLoading) {
    return (
      <div className="flex flex-col h-full bg-[#E9F0FF]">
        <div className="flex items-center pt-[51px] pl-6 py-4">
          <Image
            src="/chevron-left.svg"
            alt="Back"
            height={24}
            width={24}
            className="cursor-pointer"
            onClick={() => router.push("/health")}
          />
          <h1 className="text-[16px] font-normal ml-4">Medical Record</h1>
        </div>
        <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto">
          <div className="flex flex-col items-center justify-center h-[400px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-gray-500">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!currentUser) {
    return (
      <div className="flex flex-col h-full bg-[#E9F0FF]">
        <div className="flex items-center pt-[51px] pl-6 py-4">
          <Image
            src="/chevron-left.svg"
            alt="Back"
            height={24}
            width={24}
            className="cursor-pointer"
            onClick={() => router.push("/chats")}
          />
          <h1 className="text-[16px] font-normal ml-4">Medical Record</h1>
        </div>
        <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto">
          <div className="flex flex-col items-center justify-center h-[400px]">
            <p className="text-gray-500 mb-4">
              Please sign in to access your medical records
            </p>
            <button
              className="bg-blue-500 text-white rounded-2xl py-2 px-4"
              onClick={() => router.push("/auth/signin")}
            >
              Sign In
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF]">
      <div className="flex items-center justify-between pt-[51px] px-6 py-4 overflow-hidden">
        <div className="flex items-center">
          <Image
            src="/chevron-left.svg"
            alt="Back"
            height={24}
            width={24}
            className="cursor-pointer"
            onClick={() => router.push("/health")}
          />
          <h1 className="text-[16px] font-normal ml-4">Medical Record</h1>
        </div>

        <div
          className="flex items-center justify-center w-10 h-10 bg-white rounded-2xl shadow-sm cursor-pointer"
          onClick={() => router.push("/setting")}
        >
          <Image src="/setting.svg" alt="Settings" height={24} width={24} />
        </div>
      </div>

      {/* Check for any property that might indicate health connection */}
      {currentUser?.is_1healthup_connected === true ||
      medicalRecords.length > 0 ? (
        <MedicalRecordContent
          isDataConnected={true}
          isLoading={isLoading}
          userInfo={userInfo}
          activeTab={activeTab}
          displayRecords={filteredRecords}
          connectedSystems={[]}
          onTabChange={setActiveTab}
          onRecordClick={handleRecordClick}
          onSyncMedicalData={handleSyncMedicalData}
          isSyncing={isSyncing}
        />
      ) : (
        <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto">
          <div className="flex flex-col items-center justify-center h-full">
            <p className="text-gray-500 mb-6">There's no data found</p>
            <button
              onClick={() => router.push("/setting")}
              className="bg-blue-500 text-white font-medium py-3 px-6 rounded-2xl w-full max-w-md"
            >
              Connect Medical Data
            </button>
          </div>
        </div>
      )}

      {isDesktop && modalOpen && currentUser && (
        <MedicalRecordModal
          onClose={() => setModalOpen(false)}
          userInfo={userInfo}
          records={filteredRecords}
          selectedRecord={activeRecord}
          connectedSystems={[]}
          isLoading={isLoading}
          userId={currentUser.userId}
        />
      )}
    </div>
  );
}
