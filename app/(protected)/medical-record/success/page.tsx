"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { useUser } from "@/hooks/useUsers";
import { useWindowSize } from "@/hooks/useWindowSize";
import MedicalRecordModal from "@/components/Health/MedicalRecordModal";
import {
  OneUpHealthService,
  type MedicalRecord,
  type ConnectedSystem,
} from "@/lib/services/oneUpHealthService";

interface OneUpHealthTokenResponse {
  data?: {
    data?: {
      access_token: string;
      refresh_token?: string;
      expires_in?: number;
    };
  };
}

export default function HealthConnectionSuccess() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateSuccess, setUpdateSuccess] = useState(false);
  const { currentUser, loading, refetch } = useUser();
  const isDesktop = useWindowSize();
  const [modalOpen, setModalOpen] = useState(false);
  const [oneUpService, setOneUpService] = useState<OneUpHealthService | null>(
    null
  );
  // Add state to track whether initialization has been completed
  const [hasInitialized, setHasInitialized] = useState(false);

  const [medicalRecords, setMedicalRecords] = useState<MedicalRecord[]>([]);
  const [connectedSystems, setConnectedSystems] = useState<ConnectedSystem[]>(
    []
  );

  const provider = searchParams?.get("provider") || "1uphealth";
  const success = searchParams?.get("success") === "true";
  const issuer = searchParams?.get("iss");

  const [userInfo, setUserInfo] = useState({
    birthYear: "-",
    gender: "-",
    region: "-",
    race: "-",
    ethnicity: "-",
  });

  // Define all functions before JSX
  const isTokenValid = (): boolean => {
    if (!currentUser) return false;

    const expirationTimeStr = localStorage.getItem(
      `oneup_token_expiration_${currentUser.userId}`
    );
    if (!expirationTimeStr) return false;

    const expirationTime = new Date(expirationTimeStr).getTime();
    return Date.now() < expirationTime - 30000;
  };

  const refreshToken = async (
    service: OneUpHealthService
  ): Promise<boolean> => {
    if (!currentUser) return false;

    try {
      const refreshToken = localStorage.getItem(
        `oneup_refresh_token_${currentUser.userId}`
      );

      if (!refreshToken) return false;

      const client = generateClient<Schema>();

      const refreshResult = (await client.mutations.refreshOneUpHealthToken({
        refreshToken: refreshToken,
      })) as unknown as OneUpHealthTokenResponse;

      if (!refreshResult?.data?.data?.access_token) return false;

      service.setAccessToken(refreshResult.data.data.access_token);
      return true;
    } catch (error) {
      console.error("Failed to refresh token:", error);
      return false;
    }
  };

  const redirectToHealth = () => {
    localStorage.setItem("openHealthModal", "true");
    router.push("/health");
  };

  const fetchModalDataAndRedirect = (service: OneUpHealthService) => {
    Promise.all([
      service.fetchMedicalRecords(),
      service.getConnectedHealthSystems(),
      service.fetchPatientData(),
    ])
      .then(([records, systems, patient]) => {
        if (patient) {
          const modalData = {
            userInfo: {
              birthYear: patient.birthYear || "-",
              gender: patient.gender || "-",
              region: patient.region || "-",
              race: patient.race || "-",
              ethnicity: patient.ethnicity || "-",
            },
            records: records,
            connectedSystems: systems,
            shouldOpenModal: true,
            timestamp: Date.now(),
          };

          localStorage.setItem("healthModalData", JSON.stringify(modalData));
        }

        setIsUpdating(false);
        redirectToHealth();
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
        setIsUpdating(false);
        redirectToHealth();
      });
  };

  const handleContinue = () => {
    if (isDesktop) {
      if (oneUpService && currentUser) {
        setIsUpdating(true);

        if (!isTokenValid()) {
          refreshToken(oneUpService).then((success) => {
            if (success) {
              fetchModalDataAndRedirect(oneUpService);
            } else {
              console.error("Token refresh failed");
              setIsUpdating(false);
              redirectToHealth();
            }
          });
        } else {
          fetchModalDataAndRedirect(oneUpService);
        }
      } else {
        redirectToHealth();
      }
    } else {
      router.push("/medical-record");
    }
  };

  const handleSyncData = async () => {
    if (!currentUser?.userId) return;

    setIsUpdating(true);
    try {
      const client = generateClient<Schema>();
      console.log(
        "Manually syncing medical data for user:",
        currentUser.userId
      );

      const syncResult = await client.mutations.syncMedicalData({});

      console.log("Manual sync result:", syncResult);

      // Refresh user data after successful sync
      await refetch();

      setUpdateSuccess(true);
    } catch (error) {
      console.error("Error syncing medical data:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  useEffect(() => {
    const updateUserStatus = async () => {
      // Only proceed if we haven't initialized yet and user exists
      if (hasInitialized || !currentUser?.userId) return;

      console.log("Update status check:", {
        userId: currentUser?.userId,
        success: success,
        issuer: issuer,
        condition: currentUser?.userId && (success || issuer?.includes("fhir")),
      });

      setIsUpdating(true);
      try {
        const client = generateClient<Schema>();

        // Call the handleOneUpHealthCallback mutation to finish OAuth
        console.log("Calling handleOneUpHealthCallback with:", {
          provider: provider,
        });

        try {
          const callbackResult =
            await client.mutations.handleOneUpHealthCallback({
              provider: provider,
            });

          if (callbackResult.data && callbackResult.data.success) {
            console.log("OAuth callback handled successfully:", callbackResult);

            // Update the user's health connection status
            const updateResponse = await client.models.User.update({
              userId: currentUser.userId,
              is_1healthup_connected: true,
            });
            console.log(
              "User health connection status updated:",
              updateResponse
            );

            // Refresh the local user state to reflect changes
            await refetch();

            // Sync medical data
            console.log("Calling syncMedicalData mutation...");
            const syncResult = await client.mutations.syncMedicalData({
              userId: currentUser.userId,
            });
            console.log("Sync result:", syncResult);

            setUpdateSuccess(true);
          } else {
            console.error(
              "OAuth callback failed:",
              callbackResult?.data?.error
            );
            throw new Error(callbackResult?.data?.error || "Callback failed");
          }
        } catch (error) {
          console.error("Error updating health connection status:", error);
        }
      } catch (error) {
        console.error("Error updating health connection status:", error);
      } finally {
        setIsUpdating(false);
        // Mark initialization as complete regardless of outcome
        setHasInitialized(true);
      }
    };

    console.log("useEffect running with:", {
      loading,
      hasUser: !!currentUser,
      hasInitialized,
    });
    if (!loading && currentUser && !hasInitialized) {
      updateUserStatus();
    }
  }, [
    currentUser,
    loading,
    success,
    issuer,
    provider,
    refetch,
    hasInitialized,
  ]);

  useEffect(() => {
    if (currentUser && !loading && !oneUpService) {
      const config = {
        userId: currentUser.userId,
      };

      const service = new OneUpHealthService(config);
      setOneUpService(service);

      const storedToken = localStorage.getItem(
        `oneup_access_token_${currentUser.userId}`
      );
      if (storedToken) {
        service.setAccessToken(storedToken);

        service
          .fetchMedicalRecords()
          .then((records) => setMedicalRecords(records))
          .catch((err) => console.error("Failed to fetch records:", err));

        service
          .getConnectedHealthSystems()
          .then((systems) => setConnectedSystems(systems))
          .catch((err) => console.error("Failed to fetch systems:", err));
      }
    }
  }, [currentUser, loading, oneUpService]);

  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-6rem)] p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-md w-full">
        <div className="flex flex-col items-center mb-6">
          <div className="h-20 w-20 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mb-4">
            {isUpdating ? (
              <div className="h-12 w-12 text-blue-600 dark:text-blue-400 animate-spin">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
              </div>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-12 w-12 text-green-600 dark:text-green-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            )}
          </div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {isUpdating
              ? "Finalizing Connection..."
              : "Connected Successfully!"}
          </h1>
        </div>

        <p className="text-center text-gray-600 dark:text-gray-300 mb-6">
          {isUpdating ? (
            "We're updating your account settings. Please wait a moment..."
          ) : (
            <>
              Your{" "}
              {provider === "1uphealth" ? "1upHealth" : provider || "health"}{" "}
              account has been successfully connected. You can now access your
              medical records and health data.
              {updateSuccess && (
                <span className="block mt-2 text-green-600 dark:text-green-400">
                  Your profile has been updated successfully!
                </span>
              )}
            </>
          )}
        </p>

        <div className="flex flex-col space-y-4">
          <Button
            onClick={handleContinue}
            className="w-full"
            disabled={isUpdating}
          >
            Continue to Medical Records
          </Button>

          <Button
            onClick={() => router.push("/medical-record")}
            variant="outline"
            className="w-full"
            disabled={isUpdating}
          >
            View My Health Dashboard
          </Button>

          <Button
            onClick={handleSyncData}
            variant="secondary"
            className="w-full"
            disabled={isUpdating}
          >
            Sync Medical Data
          </Button>
        </div>
      </div>
    </div>
  );
}