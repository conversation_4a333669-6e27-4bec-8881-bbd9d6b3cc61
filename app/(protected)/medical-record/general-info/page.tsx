"use client";

import { useRouter } from "next/navigation";
import Image from "next/image";
import { ChevronLeftIcon } from "lucide-react";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useMedicalRecord } from "@/hooks/useMedicalRecord";
import { useUser } from "@/hooks/useUsers";
import { OneUpHealthService } from "@/lib/services/oneUpHealthService";

const defaultUserInfo = {
  name: "-",
  birthDate: "-",
  birthYear: "-",
  gender: "-",
  region: "-",
  race: "-",
  ethnicity: "-",
  maritalStatus: "-",
  email: "-",
  mobilePhone: "-",
  workPhone: "-",
  address: "-",
  primaryLanguage: "-",
  secondaryLanguage: "-",
};

export default function GeneralInfoPage() {
  const router = useRouter();
  const { currentUser } = useUser();
  const [userInfo, setUserInfo] = useState(defaultUserInfo);
  const [connectedSystems, setConnectedSystems] = useState<{ id: string; name: string }[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // If we have a current user, fetch the data directly
    if (currentUser) {
      fetchUserData();
    } else {
      setIsLoading(false);
    }
  }, [currentUser]);

  const fetchUserData = async () => {
    if (!currentUser) return;
    
    try {
      // Initialize the OneUpHealth service
      const service = new OneUpHealthService({ userId: currentUser.userId });
      await service.initialize();
      
      // Fetch patient data
      const patientData = await service.fetchPatientData();
      if (patientData) {
        setUserInfo({
          name: patientData.name || "-",
          birthDate: patientData.birthDate || "-",
          birthYear: patientData.birthYear || "-",
          gender: patientData.gender || "-",
          region: patientData.region || "-",
          race: patientData.race || "-",
          ethnicity: patientData.ethnicity || "-",
          // Access maritalStatus safely with type assertion since it's missing from the PatientData type
          maritalStatus: (patientData as any).maritalStatus || "-",
          email: patientData.email || "-",
          mobilePhone: (patientData as any).mobilePhone || "-",
          workPhone: (patientData as any).workPhone || "-",
          address: patientData.address || "-",
          primaryLanguage: (patientData as any).primaryLanguage || "-",
          secondaryLanguage: (patientData as any).secondaryLanguage || "-",
        });
      }
      
      // Fetch connected systems
      const systems = await service.getConnectedHealthSystems();
      setConnectedSystems(systems);
    } catch (error) {
      console.error("Failed to fetch user data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to display value or dash
  const displayValue = (value?: string): string => {
    return value && value !== "-" ? value : "-";
  };

  if (isLoading) {
    return (
      <div className="flex flex-col h-full bg-[#E9F0FF]">
        <div className="flex items-center pt-[51px] pl-6 py-4 overflow-hidden">
          <Image
            src="/chevron-left.svg"
            alt="Back"
            height={24}
            width={24}
            className="cursor-pointer"
            onClick={() => router.push("/medical-record")}
          />
          <h1 className="text-[16px] font-normal ml-4">General Information</h1>
        </div>
        <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto">
          <div className="flex justify-center items-center h-[400px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </div>
      </div>
    );
  }
  console.log("User Info:", userInfo);
  return (
    <div className="flex flex-col h-full bg-[#E9F0FF]">
      <div className="flex items-center pt-[51px] pl-6 py-4 overflow-hidden">
        <Image
          src="/chevron-left.svg"
          alt="Back"
          height={24}
          width={24}
          className="cursor-pointer"
          onClick={() => router.back()}
        />
        <h1 className="text-[16px] font-normal ml-4">General Information</h1>
      </div>
      
      <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5 overflow-auto">
        <motion.div 
          className="space-y-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="bg-[#F5F5F5] rounded-3xl p-5 border border-gray-100 shadow-sm">
            <div className="flex items-center mb-4">
              <Image
                src="/medical-record/inputt.svg"
                alt="Info"
                width={24}
                height={24}
                className="mr-2"
              />
              <h3 className="text-blue-500 font-medium">Personal Information</h3>
            </div>
          
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-y-4">
                <span className="text-gray-500">Name</span>
                <span className="text-right font-medium">{displayValue(userInfo.name)}</span>
                
                <span className="text-gray-500">Date of Birth</span>
                <span className="text-right font-medium">{displayValue(userInfo.birthDate)}</span>
                
                <span className="text-gray-500">Gender</span>
                <span className="text-right font-medium">{displayValue(userInfo.gender)}</span>
                
                <span className="text-gray-500">Marital Status</span>
                <span className="text-right font-medium">{displayValue(userInfo.maritalStatus)}</span>
                
                <span className="text-gray-500">Region</span>
                <span className="text-right font-medium">{displayValue(userInfo.region)}</span>
                
                <span className="text-gray-500">Race</span>
                <span className="text-right font-medium">{displayValue(userInfo.race)}</span>
                
                <span className="text-gray-500">Ethnicity</span>
                <span className="text-right font-medium">{displayValue(userInfo.ethnicity)}</span>
              </div>
            </div>
          </div>

          <div className="bg-[#F5F5F5] rounded-3xl p-5 border border-gray-100 shadow-sm">
            <div className="flex items-center mb-4">
              <Image
                src="/medical-record/inputt.svg"
                alt="Contact"
                width={24}
                height={24}
                className="mr-2"
              />
              <h3 className="text-blue-500 font-medium">Contact & Address</h3>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-y-4">
                <span className="text-gray-500">Email</span>
                <span className="text-right font-medium break-all">{displayValue(userInfo.email)}</span>
                
                <span className="text-gray-500">Mobile Phone</span>
                <span className="text-right font-medium">{displayValue(userInfo.mobilePhone)}</span>
                
                <span className="text-gray-500">Work Phone</span>
                <span className="text-right font-medium">{displayValue(userInfo.workPhone)}</span>
                
                <span className="text-gray-500">Home Address</span>
                <span className="text-right font-medium">{displayValue(userInfo.address)}</span>
              </div>
            </div>
          </div>

          <div className="bg-[#F5F5F5] rounded-3xl p-5 border border-gray-100 shadow-sm">
            <div className="flex items-center mb-4">
              <Image
                src="/medical-record/inputt.svg"
                alt="Communication"
                width={24}
                height={24}
                className="mr-2"
              />
              <h3 className="text-blue-500 font-medium">Communication Preferences</h3>
            </div>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-y-4">
                <span className="text-gray-500">Primary Language</span>
                <span className="text-right font-medium">{displayValue(userInfo.primaryLanguage)}</span>
                
                <span className="text-gray-500">Secondary Language</span>
                <span className="text-right font-medium">{displayValue(userInfo.secondaryLanguage)}</span>
              </div>
            </div>
          </div>

          {connectedSystems.length > 0 && (
            <div className="bg-[#F5F5F5] rounded-3xl p-5 border border-gray-100 shadow-sm">
              <div className="flex items-center mb-4">
                <Image
                  src="/heart-beat.svg"
                  alt="Connected Systems"
                  width={24}
                  height={24}
                  className="mr-2"
                />
                <h3 className="text-blue-500 font-medium">
                  Connected Health Systems
                </h3>
              </div>
              <div className="space-y-2">
                {connectedSystems.map((system) => (
                  <div
                    key={system.id}
                    className="flex items-center bg-white rounded-lg p-2 border border-gray-100"
                  >
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-sm">{system.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  );
}
