import type { Metada<PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { Toaster } from "sonner";
import { UserProvider } from "@/hooks/useUsers";
import { MedicalRecordProvider } from "@/hooks/useMedicalRecord";

const figtree = localFont({
  src: [
    {
      path: "../public/fonts/Figtree-VariableFont_wght.ttf",
      weight: "100 900",
      style: "normal",
    },
    {
      path: "../public/fonts/Figtree-Italic-VariableFont_wght.ttf",
      weight: "100 900",
      style: "italic",
    },
  ],
  variable: "--font-figtree",
});

export const metadata: Metadata = {
  title: "Jinix",
  description: "Generated by create next app",
  icons: {
    icon: "/jinix.png",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        className={`${figtree.className} ${figtree.variable} md:m-[6px] h-[calc(100vh-12px)] bg-[#E9F0FF]  md:bg-no-repeat md:bg-cover md:bg-center overflow-hidden`}
      >
        <div className="md:rounded-3xl h-full">
          <UserProvider>
            <MedicalRecordProvider>{children}</MedicalRecordProvider>
          </UserProvider>
        </div>
      </body>
    </html>
  );
}
