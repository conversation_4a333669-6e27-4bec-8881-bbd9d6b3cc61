import { fetchAuthSession, getCurrentUser } from "@aws-amplify/auth";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";

const client = generateClient<Schema>();

export async function ensureUserExists(): Promise<string> {
  try {
    const user = await getCurrentUser();
    const session = await fetchAuthSession();
    if (!session || !session.tokens?.idToken) {
      console.log("No session found, user must be authenticated");
      throw new Error("User is not authenticated");
    }

    console.log("Current user:", session);
    const userId = user.userId || user.username;

    try {
      const { data: existingUser } = await client.models.User.get({ userId });

      if (existingUser) {
        console.log("User already exists in database:", userId);
        return userId;
      }
    } catch {
      console.log("Creating new user record for:", userId);
    }

    const { data: newUser } = await client.models.User.create({
      userId,
      name: user.username || "User",
      email: String(session.tokens.idToken.payload.email),
    });

    if (!newUser) {
      throw new Error("Failed to create user record");
    }

    return userId;
  } catch (error) {
    console.log("Error ensuring user exists:", error);
    throw new Error("User authentication required");
  }
}
