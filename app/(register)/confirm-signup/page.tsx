"use client";

import { useState, useRef, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { confirmSignUp, resendSignUpCode, signIn } from "@aws-amplify/auth";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Suspense } from "react";
import CustomButton from "@/components/ui/CustomButton";
import RightSide from "@/components/auth/RightSide";
import { useWindowSize } from "@/hooks/useWindowSize";
import { ArrowLeft } from "lucide-react";

const confirmSchema = z.object({
  code: z.string().min(6, "Verification code must be at least 6 characters").nonempty("Verification code is required"),
});

type ConfirmFormData = z.infer<typeof confirmSchema>;

export default function ConfirmSignupPage() {
  return (
    <Suspense fallback={<p>Loading...</p>}>
      <ConfirmSignup />
    </Suspense>
  );
}

const ConfirmSignup = () => {
  const isDesktop = useWindowSize();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [isInvalidCode, setIsInvalidCode] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams?.get("email") || "";
  const password = searchParams?.get("password") || ""; // Get password from query params
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const { register, handleSubmit, setValue, formState: { errors } } = useForm<ConfirmFormData>({
    resolver: zodResolver(confirmSchema),
  });

  const handleConfirmSignUp = async (data: ConfirmFormData) => {
    try {
      setLoading(true);
      setErrorMessage("");
      setSuccessMessage("");
      setIsInvalidCode(false);

      // Confirm sign-up
      await confirmSignUp({ username: email, confirmationCode: data.code });

      // Auto sign-in
      await signIn({ username: email, password });

      setLoading(false);
      setSuccessMessage("Account confirmed and signed in successfully! Redirecting to chats...");
      setTimeout(() => router.push("/chats"), 1000);
    } catch (error) {
      setLoading(false);
      let errorMessage = "An error occurred. Please try again.";
      if (error instanceof Error && error.name === "CodeMismatchException") {
        errorMessage = "Incorrect confirmation code. Please try again.";
        setIsInvalidCode(true);
      } else if (error instanceof Error) {
        errorMessage = "Error: " + error.message;
      }
      setErrorMessage(errorMessage);
      console.log("Error details:", error);
    }
  };

  const handleResendCode = async () => {
    try {
      setLoading(true);
      await resendSignUpCode({ username: email });
      setLoading(false);
      setSuccessMessage("Verification code has been resent to your email!");
      setIsInvalidCode(false);
    } catch (error) {
      setLoading(false);
      setErrorMessage("Error resending code: " + (error as { message: string }).message);
    }
  };

  const handleCodeChange = (index: number, value: string) => {
    if (!/^\d*$/.test(value)) {
      inputRefs.current[index]!.value = "";
      return;
    }

    if (value.length > 1) return;
    const newCode = inputRefs.current.map((input) => input?.value || "").join("");
    setValue("code", newCode);
    setIsInvalidCode(false);

    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    } else if (!value && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (index: number, e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").trim();

    if (!/^\d+$/.test(pastedData) || pastedData.length > 6) return;

    const digits = pastedData.split("");
    digits.forEach((digit, i) => {
      if (inputRefs.current[i]) {
        inputRefs.current[i]!.value = digit;
      }
    });

    const newCode = inputRefs.current.map((input) => input?.value || "").join("");
    setValue("code", newCode);
    setIsInvalidCode(false);

    const lastFilledIndex = Math.min(digits.length - 1, 5);
    inputRefs.current[lastFilledIndex]?.focus();
  };

  useEffect(() => {
    inputRefs.current[0]?.focus();
  }, []);

  return (
    <main className="h-full flex flex-col">
      {isDesktop ? (
        // Desktop Layout
        <div className="h-full flex">
          {/* Left side */}
          <div className="hidden md:flex w-full md:w-1/2 flex-col justify-start h-full px-8 lg:px-16 xl:px-28 2xl:px-[140px] py-8 lg:pt-32 lg:pb-[258px] bg-white overflow-hidden rounded-3xl">
            <div className="flex">
              <p className="text-[#FFB300] text-lg font-medium mb-2">jin</p>
              <p className="text-[#4187FF] text-lg font-medium mb-2">I</p>
              <p className="text-[#D93C85] text-lg font-medium mb-2">X</p>
            </div>
            <h2 className="text-start text-3xl text-[#0F172A] mb-1">Verify your email</h2>
            <p className="text-start text-[14px] font-[500] text-gray-600 mb-6 leading-relaxed">
              For your security, we’ve sent a verification code to your email at{" "}
              <span className="text-blue-600">{email}</span>. Please enter the code below to continue.
            </p>
            <form onSubmit={handleSubmit(handleConfirmSignUp)}>
              <div className="mb-3 flex justify-start space-x-2">
                {[...Array(6)].map((_, index) => (
                  <input
                    key={index}
                    type="text"
                    maxLength={1}
                    className={`w-full max-w-[60px] h-[60px] sm:max-w-[70px] sm:h-[70px] md:max-w-[76px] md:h-[76px] text-center text-lg border rounded-xl focus:outline-none bg-white shadow-sm
                      ${loading ? "bg-gray-100 border-gray-200 text-gray-400" : isInvalidCode ? "border-2 border-red-500 rounded-md" : "border-gray-200 focus:border-blue-600"}`}
                    ref={(el) => {
                      inputRefs.current[index] = el;
                    }}
                    onChange={(e) => handleCodeChange(index, e.target.value)}
                    onPaste={(e) => handlePaste(index, e)}
                    onKeyDown={(e) => {
                      if (e.key === "Backspace" && !inputRefs.current[index]?.value && index > 0) {
                        inputRefs.current[index - 1]?.focus();
                      }
                    }}
                    disabled={loading}
                  />
                ))}
                <input type="hidden" {...register("code")} />
              </div>
              {errors.code && <p className="text-red-500 text-sm mb-4 text-center">{errors.code.message}</p>}
              {errorMessage && <p className="text-red-500 text-sm mb-4 text-start">{errorMessage}</p>}
              {successMessage && <p className="text-green-500 text-sm mb-4 text-start">{successMessage}</p>}
              <div className="text-start mb-6">
                <button
                  type="button"
                  onClick={handleResendCode}
                  className="text-gray-600 text-sm hover:underline"
                  disabled={loading}
                >
                  Didn’t get it?{" "}
                  <span className="text-blue-600 font-medium hover:underline">Resend code</span>
                </button>
              </div>
              <CustomButton
                type="submit"
                loading={loading}
                style={{ maxWidth: 494, width: "100%" }}
              >
                Sign Up
              </CustomButton>
            </form>
          </div>
          {/* Right side */}
          <RightSide />
        </div>
      ) : (
        // Mobile Layout
        <div className="md:hidden flex flex-col items-center justify-start min-h-screen h-screen max-h-screen p-5 pt-[68px] overflow-hidden bg-white">
          <div className="absolute top-5 left-5 mb-8">
            <ArrowLeft className="w-6 h-6 cursor-pointer" onClick={() => router.back()} />
          </div>
          <div className="w-full max-w-md">
            <h2 className="text-start text-3xl text-[#0F172A] mb-4">Verify your email</h2>
            <p className="text-start text-[14px] font-[500] text-gray-600 mb-8 leading-relaxed max-w-[390px] w-full">
              For your security, we’ve sent a verification code to your email at{" "}
              <span className="text-blue-600">{email}</span>. Please enter the code below to continue.
            </p>
            <form onSubmit={handleSubmit(handleConfirmSignUp)}>
              <div className="mb-4 flex justify-start space-x-2">
                {[...Array(6)].map((_, index) => (
                  <input
                    key={index}
                    type="text"
                    maxLength={1}
                    className={`w-full max-w-[58px] h-[61px] xs:max-w-[52px] xs:h-[52px] sm:max-w-[58px] sm:h-[61px] text-center text-lg border rounded-xl focus:outline-none bg-white shadow-sm
                      ${loading ? "bg-gray-100 border-gray-200 text-gray-400" : isInvalidCode ? "border-2 border-red-500 rounded-md" : "border-gray-200 focus:border-blue-600"}`}
                    ref={(el) => {
                      inputRefs.current[index] = el;
                    }}
                    onChange={(e) => handleCodeChange(index, e.target.value)}
                    onPaste={(e) => handlePaste(index, e)}
                    onKeyDown={(e) => {
                      if (e.key === "Backspace" && !inputRefs.current[index]?.value && index > 0) {
                        inputRefs.current[index - 1]?.focus();
                      }
                    }}
                    disabled={loading}
                  />
                ))}
                <input type="hidden" {...register("code")} />
              </div>
              {errors.code && <p className="text-red-500 text-sm mb-4 text-center">{errors.code.message}</p>}
              {errorMessage && <p className="text-red-500 text-sm mb-4 text-start">{errorMessage}</p>}
              {successMessage && <p className="text-green-500 text-sm mb-4 text-start">{successMessage}</p>}
              <div className="text-start mb-6">
                <button
                  type="button"
                  onClick={handleResendCode}
                  className="text-gray-600 text-sm hover:underline"
                  disabled={loading}
                >
                  Didn’t get it?{" "}
                  <span className="text-blue-600 font-medium hover:underline">Resend code</span>
                </button>
              </div>
              <CustomButton
                type="submit"
                loading={loading}
                className="w-full max-w-[390px] h-[52px] xs:max-w-[340px] xs:h-[52px] sm:max-w-[390px] sm:h-[52px]"
              >
                Sign Up
              </CustomButton>
            </form>
          </div>
        </div>
      )}
    </main>
  );
};