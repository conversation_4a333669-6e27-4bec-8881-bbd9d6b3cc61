"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { resetPassword } from "aws-amplify/auth";
import { useWindowSize } from "@/hooks/useWindowSize";
import RightSide from "@/components/auth/RightSide";
import CustomButton from "@/components/ui/CustomButton";
import { ArrowLeft } from "lucide-react";

interface ForgotPasswordForm {
  email: string;
}

export default function ForgotPasswordPage() {
  const isDesktop = useWindowSize();
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");
  const router = useRouter();

  const { register, handleSubmit, formState: { errors } } = useForm<ForgotPasswordForm>();

  const onSubmit = async (data: ForgotPasswordForm) => {
    try {
      setLoading(true);
      setErrorMessage("");
      setSuccessMessage("");

      await resetPassword({ username: data.email });

      setLoading(false);
      setSuccessMessage("Password reset link has been sent to your email.");
      setTimeout(() => router.push(`/reset-password?email=${encodeURIComponent(data.email)}`), 2000);
    } catch (error) {
      setLoading(false);
      setErrorMessage("Error: " + (error as { message: string }).message);
    }
  };

  return (
    <main className="h-full flex flex-col">
      {isDesktop ? (
        // Desktop Layout
        <div className="flex h-full">
          {/* Left side */}
          <div className="hidden md:flex w-full md:w-1/2 flex-col justify-start h-full px-8 lg:px-16 xl:px-28 2xl:px-[140px] py-8 lg:pt-32 lg:pb-[258px] bg-white overflow-hidden rounded-3xl">
            <div className="flex">
              <p className="text-[#FFB300] text-lg font-medium mb-2">jin</p>
              <p className="text-[#4187FF] text-lg font-medium mb-2">I</p>
              <p className="text-[#D93C85] text-lg font-medium mb-2">X</p>
            </div>
            
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Forgot Password</h2>
         
            
            {errorMessage && <p className="text-red-500 text-sm mb-4">{errorMessage}</p>}
            {successMessage && <p className="text-green-500 text-sm mb-4">{successMessage}</p>}
            
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="mb-6">
                <label htmlFor="email" className="block text-sm text-gray-600 mb-2">
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  className="w-full p-3 rounded-full bg-white border border-gray-300 focus:outline-none focus:border-blue-500 text-gray-900"
                  {...register("email", { required: "Email is required" })}
                  placeholder="Enter your email"
                />
                {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
              </div>
              
              <CustomButton
                type="submit"
                loading={loading}
              >
                Send Reset Code
              </CustomButton>
            </form>
            
            <div className="mt-4 text-center">
              <a href="/login" className="text-blue-600 text-sm hover:underline">
                Back to Login
              </a>
            </div>
          </div>

          {/* Right side */}
          <RightSide />
        </div>
      ) : (
        // Mobile Layout
        <div className="md:hidden flex flex-col items-center justify-start min-h-screen h-screen max-h-screen p-5 pt-[68px] overflow-hidden bg-white">
           <div className="absolute top-5 left-5 mb-8">
                        <ArrowLeft className="w-6 h-6 cursor-pointer" onClick={() => router.back()} />
                    </div>
          <div className="w-full max-w-md">
            <h2 className="text-start text-3xl text-gray-900 mb-2">Forgot Password</h2>
            <p className="text-start text-xs text-gray-600 mb-8 leading-relaxed">
              Enter your email to receive a password reset code.
            </p>
            
            {errorMessage && <p className="text-red-500 text-sm mb-4">{errorMessage}</p>}
            {successMessage && <p className="text-green-500 text-sm mb-4">{successMessage}</p>}
            
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <input
                  id="email"
                  type="email"
                  className="w-full p-3 h-[45px] rounded-3xl bg-[#F5F5F54D] border border-gray-300 focus:outline-none focus:border-blue-500 text-gray-900"
                  {...register("email", { required: "Email is required" })}
                  placeholder="Enter your email"
                />
                {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>}
              </div>
              
              <CustomButton
                type="submit"
                loading={loading}
              >
                Send Reset Code
              </CustomButton>
            </form>
            
            <div className="mt-6 text-center">
              <a href="/login" className="text-blue-600 text-sm hover:underline">
                Back to Login
              </a>
            </div>
          </div>
        </div>
      )}
    </main>
  );
}