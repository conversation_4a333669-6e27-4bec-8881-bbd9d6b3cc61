import type { Metadata } from "next";
import localFont from "next/font/local";
import "../globals.css";

import { UserProvider } from "@/hooks/useUsers";
import { MedicalRecordProvider } from "@/hooks/useMedicalRecord";

const figtree = localFont({
  src: [
    {
      path: "../../public/fonts/Figtree-VariableFont_wght.ttf",
      weight: "100 900",
      style: "normal",
    },
    {
      path: "../../public/fonts/Figtree-Italic-VariableFont_wght.ttf",
      weight: "100 900",
      style: "italic",
    },
  ],
  variable: "--font-figtree",
});

export const metadata: Metadata = {
  title: "Jinix",
  description: "Generated by create next app",
  icons: {
    icon: "/jinix.png",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div
      className={`${figtree.className} ${figtree.variable} md:m-[6px] h-[calc(100vh-12px)] bg-[#E9F0FF] md:bg-[url('/layoutbackground.png')] md:bg-no-repeat md:bg-cover md:bg-center overflow-hidden`}
    >
      <div className="md:rounded-3xl h-full">
        <UserProvider>
          <MedicalRecordProvider>
            <div className="h-full">{children}</div>
          </MedicalRecordProvider>
        </UserProvider>
      </div>
    </div>
  );
}
