// useMap.tsx
import React, { useEffect, useState } from "react";
import { generateClient } from "aws-amplify/data";
import { Amplify } from "aws-amplify";
import outputs from "@/amplify_outputs.json";
import { type Schema } from "@/amplify/data/resource";
import { MedicalEntity } from "@/types/auth";
import { useSearchParams, useRouter, usePathname } from "next/navigation";

Amplify.configure(outputs);

let client: ReturnType<typeof generateClient<Schema>>;
try {
  client = generateClient<Schema>();
  console.log("Amplify Data client initialized successfully");
} catch (err) {
  console.error("Failed to generate Amplify Data client:", err);
}

export function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 3958.8;
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) * Math.cos(lat2 * (Math.PI / 180)) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

const zipCodeMap: Record<string, Record<string, { lat: number; long: number }>> = {
  USA: {
    "48202": { lat: 42.363, long: -83.076 },
  },
  Ukraine: {
    "01001": { lat: 50.4501, long: 30.5234 },
    "79000": { lat: 49.8397, long: 24.0297 },
  },
};

export const useMap = (
  initialLat: number,
  initialLong: number,
  zoom: number,
  initialCountry: string = "",
  initialZipCode: string = "",
  shouldFetch: boolean = true
) => {
  const [error, setError] = useState<Error | null>(null);
  const [isListOpen, setIsListOpen] = useState<boolean>(false);
  const [lat, setLat] = useState<number>(initialLat);
  const [long, setLong] = useState<number>(initialLong);
  const [geoError, setGeoError] = useState<string | null>(null);
  const [zipCode, setZipCode] = useState<string>(initialZipCode);
  const [country, setCountry] = useState<string>(initialCountry);
  const [selectedEntity, setSelectedEntity] = useState<MedicalEntity | null>(null);
  const [entitiesWithinRadius, setEntitiesWithinRadius] = useState<MedicalEntity[]>([]);
  const [timeoutId, setTimeoutId] = useState<NodeJS.Timeout | null>(null);
  const [showZipInput, setShowZipInput] = useState<boolean>(!!initialCountry);
  const [loading, setLoading] = useState<boolean>(true);

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Функція для оновлення URL з новими параметрами
  const updateUrlParams = (newZipCode: string, newCountry: string) => {
    const params = new URLSearchParams(searchParams?.toString());
    if (newZipCode) {
      params.set("zipCode", newZipCode);
    } else {
      params.delete("zipCode");
    }
    if (newCountry) {
      params.set("country", newCountry);
    } else {
      params.delete("country");
    }
    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  };

  const fetchMedicalEntities = async () => {
    if (!client) {
      setError(new Error("Amplify Data client is not initialized. Check Amplify configuration."));
      setLoading(false);
      return;
    }
    if (!shouldFetch) {
      setLoading(false);
      return;
    }
    setLoading(true);
    try {
      const { data: alSCenters, errors } = await client.models.ALSCenter.list({
        selectionSet: [
          "ClinicName",
          "DoctorName",
          "WebsiteURL",
          "ZipCode",
          "Location.LatLng.Lat",
          "Location.LatLng.Lng",
          "Specialties",
          "Distance",
          "PhoneNumber"
        ],
      });
      if (errors) {
        throw new Error(errors.map((e) => e.message).join(", "));
      }
      console.log("Fetched ALSCenters:", alSCenters);
      const mappedEntities: MedicalEntity[] = alSCenters
        .filter(
          (center: any) =>
            center.Location?.LatLng?.Lat &&
            center.Location?.LatLng?.Lng &&
            (center.Specialties || []).length > 0 &&
            center.Distance !== null &&
            center.Distance !== undefined
        )
        .map((center: any) => ({
          name: center.DoctorName || center.ClinicName || "Unknown",
          type: center.DoctorName ? "doctor" : "hospital",
          lat: center.Location.LatLng.Lat,
          long: center.Location.LatLng.Lng,
          address: `${center.ClinicName || "Unknown Clinic"}, ${center.ZipCode || "Unknown Zip"}`,
          phone: center.PhoneNumber || "",
          hours:  "",
          WebsiteURL: center.WebsiteURL,
          Specialties: center.Specialties,
        }));
      setEntitiesWithinRadius(mappedEntities);
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Failed to fetch medical entities"));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (shouldFetch) {
      fetchMedicalEntities();
    }
  }, [shouldFetch]);

  // Автоматично викликаємо handleZipChange, якщо zipCode і country є в URL
  useEffect(() => {
    if (initialZipCode && initialCountry) {
      handleZipChange({ target: { value: initialZipCode } } as React.ChangeEvent<HTMLInputElement>);
    }
  }, [initialZipCode, initialCountry]);

  useEffect(() => {
    if (!navigator.geolocation) return;

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const newLat = position.coords.latitude;
        const newLong = position.coords.longitude;
        if (newLat < -90 || newLat > 90 || newLong < -180 || newLong > 180) {
          setGeoError("Invalid geolocation coordinates. Default coordinates used.");
          setLat(initialLat);
          setLong(initialLong);
        } else {
          setLat(newLat);
          setLong(newLong);
          setGeoError(null);
        }
      },
      () => {
        setGeoError("Geolocation not allowed. Please enter postal code.");
        setLat(initialLat);
        setLong(initialLong);
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
    );
  }, [initialLat, initialLong]);

  const handleZipChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const zip = e.target.value.trim();
    setZipCode(zip);
    updateUrlParams(zip, country); // Оновлюємо URL

    if (timeoutId) clearTimeout(timeoutId);

    const newTimeoutId = setTimeout(async () => {
      if (zip && country) {
        const mappedCoords = zipCodeMap[country]?.[zip];
        if (mappedCoords) {
          setLat(mappedCoords.lat);
          setLong(mappedCoords.long);
          setGeoError(null);
          console.log(`Using mapped coordinates for ${zip || "Unknown"} in ${country || "Unknown"}: lat=${mappedCoords.lat}, long=${mappedCoords.long}`);
        } else {
          try {
            const response = await fetch(
              `https://api.opencagedata.com/geocode/v1/json?q=${encodeURIComponent(
                zip
              )},+${encodeURIComponent(country)}&key=${process.env.NEXT_PUBLIC_OPENCAGE_API_KEY}`
            );
            const data = await response.json();
            console.log("API Response:", data);
            if (data.results && data.results.length > 0) {
              const { lat: newLat, lng: newLong } = data.results[0].geometry;
              console.log(`Coordinates for ${zip} in ${country}: lat=${newLat}, long=${newLong}`);
              setLat(newLat);
              setLong(newLong);
              setGeoError(null);
            } else {
              setGeoError("No coordinates found for this postal code.");
            }
          } catch (err) {
            console.error("Fetch Error:", err);
            setGeoError("Failed to fetch coordinates.");
          }
        }
      } else if (zip && !country) {
        setGeoError("Please select a country before searching.");
      } else {
        setLat(initialLat);
        setLong(initialLong);
        setGeoError(null);
      }
    }, 500);

    setTimeoutId(newTimeoutId);
  };

  const handleCountryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newCountry = e.target.value;
    setCountry(newCountry);
    setShowZipInput(!!newCountry);
    updateUrlParams(zipCode, newCountry); // Оновлюємо URL

    if (zipCode && newCountry) {
      handleZipChange({ target: { value: zipCode } } as React.ChangeEvent<HTMLInputElement>);
    } else if (!newCountry) {
      setLat(initialLat);
      setLong(initialLong);
      setZipCode("");
      setGeoError(null);
      setShowZipInput(false);
      updateUrlParams("", ""); // Очищаємо URL
    }
  };

  const handleToggleList = () => setIsListOpen(!isListOpen);
  const handleCloseDetails = () => setSelectedEntity(null);

  return {
    error,
    geoError,
    zipCode,
    handleZipChange,
    country,
    handleCountryChange,
    showZipInput,
    isListOpen,
    handleToggleList,
    entitiesWithinRadius,
    selectedEntity,
    setSelectedEntity,
    handleCloseDetails,
    calculateDistance,
    lat,
    long,
    loading,
  };
};