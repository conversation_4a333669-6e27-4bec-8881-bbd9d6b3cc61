import { useState, useEffect, useRef } from "react";
import { signUp, confirmSignUp, getCurrentUser, signInWithRedirect, resendSignUpCode, signIn } from "@aws-amplify/auth";
import { useRouter } from "next/navigation";
import { FormData } from "@/types/auth";
import { Hub } from "aws-amplify/utils";

const useRegister = () => {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [confirmationCode, setConfirmationCode] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState(""); // Changed from useRef to useState
  const [isAuthenticated, setAuthenticated] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const user = await getCurrentUser();
        if (user) {
          setAuthenticated(true);
          setEmail(user.username);
          router.push("/chats");
        } else {
          setAuthenticated(false);
        }
      } catch {
        setAuthenticated(false);
      }
    };

    checkAuth();

    const hubListener = Hub.listen("auth", ({ payload }) => {
      const { event } = payload;
      if (event === "signedIn") {
        checkAuth();
      }
    });

    return () => {
      hubListener();
    };
  }, [router]);

  const handleRegister = async (data: FormData) => {
    try {
      setLoading(true);
      setEmail(data.email);
      setPassword(data.password); // Store password in state

      const { nextStep } = await signUp({
        username: data.email,
        password: data.password,
      });

      setLoading(false);

      if (nextStep.signUpStep === "CONFIRM_SIGN_UP") {
        setIsCodeSent(true);
        router.push(`/confirm-signup?email=${encodeURIComponent(data.email)}&password=${encodeURIComponent(data.password)}`);
      } else {
        alert("Registration successful! Please check your email for confirmation.");
      }
    } catch (error) {
      setLoading(false);
      setErrorMessage("Error: " + (error as { message: string }).message);
    }
  };

  const handleConfirmSignUp = async (confirmationCode: string) => {
    try {
      setLoading(true);
      
      await confirmSignUp({ username: email, confirmationCode });
      await signIn({ username: email, password });
      setLoading(false);
      
      router.push("/chats");
    } catch (error) {
      setLoading(false);
      let errorMessage = "An error occurred. Please try again.";
      if ((error as { message: string }).message.includes("CodeMismatchException")) {
        errorMessage = "Incorrect confirmation code. Please try again.";
      } else {
        errorMessage = "Error: " + (error as { message: string }).message;
      }
      setErrorMessage(errorMessage);
    }
  };

  const handleResendCode = async () => {
    try {
      setLoading(true);
      await resendSignUpCode({ username: email });
      alert("Verification code has been resent to your email!");
      setLoading(false);
    } catch (error) {
      setLoading(false);
      setErrorMessage("Error resending code: " + (error as { message: string }).message);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      await signInWithRedirect({ provider: "Google" });
      const user = await getCurrentUser();
      if (user) {
        setAuthenticated(true);
        setEmail(user.username);
        alert("You have successfully signed in via Google!");
      } else {
        alert("There was an error signing in. Please try again.");
      }
    } catch (error) {
      console.error("Google Sign-In Error:", error);
    }
  };

  const handleFacebookSignIn = async () => {
    try {
      await signInWithRedirect({ provider: "Facebook" });
      const user = await getCurrentUser();
      if (user) {
        setAuthenticated(true);
        setEmail(user.username);
        alert("You have successfully signed in via Facebook!");
      } else {
        alert("There was an error signing in. Please try again.");
      }
    } catch (error) {
      console.error("Facebook Sign-In Error:", error);
    }
  };

  return {
    loading,
    errorMessage,
    isCodeSent,
    confirmationCode,
    setConfirmationCode,
    email,
    password, // Expose password
    isAuthenticated,
    handleRegister,
    handleConfirmSignUp,
    handleResendCode,
    handleGoogleSignIn,
    handleFacebookSignIn,
  };
};

export default useRegister;