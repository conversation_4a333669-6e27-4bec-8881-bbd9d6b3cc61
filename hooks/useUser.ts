import { useState, useEffect } from 'react'
import { fetchAuthSession } from "aws-amplify/auth"

interface User {
  email: string
}

export function useUser() {
  const [currentUser, setCurrentUser] = useState<User | null>(null)

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const session = await fetchAuthSession()
        if (session.tokens?.idToken?.payload?.email) {
          const email = String(session.tokens.idToken.payload.email)
          setCurrentUser({ email })
        }
      } catch (error) {
        console.error("Error fetching user session:", error)
        setCurrentUser(null)
      }
    }

    fetchUser()
  }, [])

  return { currentUser }
}
