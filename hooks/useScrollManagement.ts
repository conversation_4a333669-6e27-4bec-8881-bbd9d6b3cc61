import { useRef, useEffect, useState, RefObject } from "react";
import { debounce } from "lodash";

const SCROLL_THRESHOLD = 20;
const IMMEDIATE_SCROLL_TIMEOUT = 0;
const STREAMING_SCROLL_TIMEOUT = 100;
const DEBOUNCE_DELAY = 50;

interface ScrollOptions {
  behavior: ScrollBehavior;
  block: ScrollLogicalPosition;
}

export function useScrollManagement() {
  const messageEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const wasAtBottomRef = useRef(true);
  const isFirstRenderRef = useRef(true);
  const prevMessagesLengthRef = useRef(0);
  const [prevFirstMessageId, setPrevFirstMessageId] = useState<string | null>(
    null
  );
  const forceScrollRef = useRef(false);

  const updateScrollPosition = () => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const { scrollTop, clientHeight, scrollHeight } = container;
    wasAtBottomRef.current =
      scrollTop + clientHeight >= scrollHeight - SCROLL_THRESHOLD;
  };

  const debouncedScrollHandler = useRef(
    debounce(() => {
      updateScrollPosition();
    }, DEBOUNCE_DELAY)
  ).current;

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    container.addEventListener("scroll", debouncedScrollHandler);
    updateScrollPosition();

    return () => {
      container.removeEventListener("scroll", debouncedScrollHandler);
      debouncedScrollHandler.cancel();
    };
  }, [debouncedScrollHandler]);

  const forceScrollToBottom = (
    options: ScrollOptions = { behavior: "smooth", block: "end" }
  ) => {
    forceScrollRef.current = true;
    setTimeout(
      () => {
        messageEndRef.current?.scrollIntoView(options);

        setTimeout(
          () => {
            forceScrollRef.current = false;
          },
          options.behavior === "auto" ? 50 : 300
        );
      },
      options.behavior === "auto"
        ? IMMEDIATE_SCROLL_TIMEOUT
        : STREAMING_SCROLL_TIMEOUT
    );
  };

  const scrollToBottom = (
    options: ScrollOptions = { behavior: "smooth", block: "end" }
  ) => {
    if (forceScrollRef.current) {
      forceScrollToBottom(options);
      return;
    }

    if (wasAtBottomRef.current || isFirstRenderRef.current) {
      setTimeout(
        () => {
          messageEndRef.current?.scrollIntoView(options);
        },
        options.behavior === "auto"
          ? IMMEDIATE_SCROLL_TIMEOUT
          : STREAMING_SCROLL_TIMEOUT
      );
    }
  };

  const scrollToMessage = (
    messageId: string,
    options: ScrollOptions = { behavior: "auto", block: "start" }
  ) => {
    const messageElement = document.getElementById(messageId);
    if (messageElement) {
      messageElement.scrollIntoView(options);
    }
  };

  return {
    messageEndRef,
    scrollContainerRef,
    wasAtBottomRef,
    isFirstRenderRef,
    prevMessagesLengthRef,
    prevFirstMessageId,
    setPrevFirstMessageId,
    scrollToBottom,
    forceScrollToBottom,
    scrollToMessage,
    updateScrollPosition,
  };
}
