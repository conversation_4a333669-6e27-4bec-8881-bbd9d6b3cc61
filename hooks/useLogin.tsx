// hooks/useLogin.tsx
"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  getCurrentUser,
  signIn,
  signInWithRedirect,
  signOut,
  fetchAuthSession,
  resetPassword,
  resendSignUpCode,
} from "@aws-amplify/auth";
import { FormData } from "@/types/auth";
import { Hub } from "aws-amplify/utils";


const useLogin = () => {
  const [loading, setLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [userEmail, setUserEmail] = useState<string | null>(null);

  const router = useRouter();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens) {
          const user = await getCurrentUser();
          if (user) {
            console.log("User authenticated successfully:", user.username);
            setUserEmail(user.username);
            setIsAuthenticated(true);
            
            // Add a small delay to ensure authentication is fully processed
            setTimeout(() => {
              router.push('/chats');
            }, 500);
          }
        }
      } catch (error) {
        console.error("Auth check failed:", error);
        setErrorMessage(`Authentication check failed: ${(error as Error).message}`);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();

    const hubListener = Hub.listen("auth", ({ payload }) => {
      const { event } = payload;
      if (event === "signedIn") {
        console.log("Auth hub detected sign-in event");
        checkAuth();
      } else if (event === "signedOut") {
        console.log("Auth hub detected sign-out event");
        setIsAuthenticated(false);
        setUserEmail(null);
      } else if (event === "tokenRefresh") {
        console.log("Auth token refreshed");
      }
    });

    return () => hubListener();
  }, [router]);

  const handleGoogleLogin = async () => {
    try {
      await signInWithRedirect({ provider: "Google" });
    } catch (error) {
      console.error("Google Sign-In Error:", error);
    }
  };

  const handleFacebookLogin = async () => {
    try {
      await signInWithRedirect({ provider: "Facebook" });
    } catch (error) {
      console.error("Facebook Sign-In Error:", error);
    }
  };

  const handleLogout = async () => {
    try {
      await signOut();
      
      setIsAuthenticated(false);
      setUserEmail(null);
      router.push("/login"); // Перенаправлення на головну сторінку після виходу
    } catch (error) {
      setErrorMessage("Error during logout: " + (error as { message: string }).message);
    }
  };

  const handleLogin = async (data: FormData) => {
    const { email, password } = data;

    try {
      setLoading(true);
      setErrorMessage("");
      console.log("Attempting login for:", email);
      
      const user = await signIn({
        username: email,
        password: password,
      });

      if (user.nextStep?.signInStep === "CONFIRM_SIGN_UP") {
        await resendSignUpCode({ username: email });
        router.push(`/confirm-signup?email=${encodeURIComponent(email)}`);
      } else {
        console.log("Login successful, fetching session");
        // Explicitly initialize session before redirecting
        const session = await fetchAuthSession();
        
        if (session.tokens?.idToken?.payload?.sub) {
          console.log("Session initialized with user ID:", session.tokens.idToken.payload.sub);
          // Session initialized correctly
          setIsAuthenticated(true);
          setUserEmail(email);
          
          // Add a small delay to ensure authentication is fully processed
          setTimeout(() => {
            router.push("/chats");
          }, 500);
        } else {
          // Handle case when user ID is missing
          console.error("User ID not found in token after email login");
          setErrorMessage("Authentication error: User session could not be established");
        }
      }
    } catch (error) {
      const errorMsg = "Login error: " + (error as { message: string }).message;
      console.error(errorMsg, error);
      setErrorMessage(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    try {
      const email = prompt("Please enter your email address:");
      if (!email) return;

      setLoading(true);
      await resetPassword({ username: email });
      setLoading(false);
      router.push(`/reset-password?email=${encodeURIComponent(email)}`);
    } catch (error) {
      setLoading(false);
      setErrorMessage("Password reset error: " + (error as { message: string }).message);
    }
  };

  return {
    loading,
    errorMessage,
    isAuthenticated,
    isLoading,
    userEmail,
    handleGoogleLogin,
    handleFacebookLogin,
    handleLogin,
    handleLogout,
    handleForgotPassword,
    // Add a method to check if the session is valid
    checkSessionValid: async () => {
      try {
        const session = await fetchAuthSession();
        return !!session.tokens;
      } catch (error) {
        console.error("Session validation error:", error);
        return false;
      }
    }
  };
};

export default useLogin;