"use client";

/**
 * Medical Record Context
 *
 * This context provides a way to share medical record data between components,
 * particularly when navigating between different pages of the application.
 *
 * Usage:
 * 1. In the source page (e.g., medical-record/page.tsx), use setSelectedRecord to store the record
 *    before navigation:
 *    ```
 *    const { setSelectedRecord } = useMedicalRecord();
 *
 *    const handleRecordClick = (record) => {
 *      setSelectedRecord(record);
 *      router.push('/outpatient');
 *    }
 *    ```
 *
 * 2. In the destination page, use selectedRecord to access the data:
 *    ```
 *    const { selectedRecord } = useMedicalRecord();
 *
 *    // Use selectedRecord data to render the page
 *    ```
 *
 * This approach removes the need for sessionStorage or localStorage for passing data
 * between pages, maintaining a more React-idiomatic approach to state management.
 */

import { createContext, useContext, useState, ReactNode } from "react";
import { type MedicalRecord } from "@/lib/services/oneUpHealthService";

interface MedicalRecordContextType {
  selectedRecord: MedicalRecord | null;
  setSelectedRecord: (record: MedicalRecord | null) => void;
}

// Create context with default values
const MedicalRecordContext = createContext<MedicalRecordContextType>({
  selectedRecord: null,
  setSelectedRecord: () => {},
});

// Provider component
export function MedicalRecordProvider({ children }: { children: ReactNode }) {
  const [selectedRecord, setSelectedRecord] = useState<MedicalRecord | null>(
    null
  );

  return (
    <MedicalRecordContext.Provider
      value={{ selectedRecord, setSelectedRecord }}
    >
      {children}
    </MedicalRecordContext.Provider>
  );
}

// Custom hook to use the medical record context
export function useMedicalRecord() {
  return useContext(MedicalRecordContext);
}
