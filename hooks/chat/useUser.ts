import { useState, useEffect } from "react";
import { ensureUserExists } from "@/app/utils/auth";
import { ERROR_AUTH_FAILED } from "@/constants/chat";

export function useUser() {
  const [userId, setUserId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchUserId() {
      try {
        setIsLoading(true);
        const id = await ensureUserExists();
        setUserId(id);

        return id;
      } catch (err) {
        console.error("Failed to get user ID:", err);
        setError(ERROR_AUTH_FAILED);
        return null;
      } finally {
        setIsLoading(false);
      }
    }

    fetchUserId();
  }, []);

  return {
    userId,
    isLoading,
    error,
  };
}
