import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";

const client = generateClient<Schema>();

interface OneUpHealthTokenResponse {
  data?: {
    success?: boolean;
    error?: string | null;
    data?: {
      access_token: string;
      refresh_token?: string;
      expires_in?: number;
    };
  };
}

interface OneUpHealthAuthCodeResponse {
  data?: {
    success?: boolean;
    error?: string | null;
    data?: {
      code: string;
    };
  };
}

interface OneUpHealthFhirDataResponse {
  data?: {
    success?: boolean;
    error?: string | null;
    data?: {
      entry: any;
    };
  };
}

export interface OneUpHealthConfig {
  userId: string;
  clientId?: string;
  clientSecret?: string;
}

export interface PatientData {
  id: string;
  name: string;
  gender: string;
  birthDate: string;
  birthYear: string;
  address: string;
  phoneNumber: string;
  email: string;
  race: string;
  ethnicity: string;
  region: string;
}

export interface MedicalRecord {
  id: string;
  date: string;
  type:
    | "Outpatient Visit"
    | "Symptom"
    | "Medication Reaction"
    | "Lab Result"
    | "Medication";
  location?: string;
  doctor?: string;
  description: string;
  isClickable: boolean;
  rawData?: Record<string, unknown>;
  relatedResources?: Record<string, unknown>[];
}

export interface ConnectedSystem {
  id: string;
  name: string;
  systemId: string;
}

export class OneUpHealthService {
  private config: OneUpHealthConfig;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private tokenExpiration: Date | null = null;
  private patientId: string | null = null;

  constructor(config: OneUpHealthConfig) {
    this.config = config;
    this.loadStoredTokens();
  }

  private loadStoredTokens() {
    if (typeof window !== "undefined") {
      this.accessToken = localStorage.getItem(
        `oneup_access_token_${this.config.userId}`
      );
      this.refreshToken = localStorage.getItem(
        `oneup_refresh_token_${this.config.userId}`
      );
      const expirationString = localStorage.getItem(
        `oneup_token_expiration_${this.config.userId}`
      );

      if (expirationString) {
        this.tokenExpiration = new Date(expirationString);
      }
    }
  }

  private storeTokens(
    accessToken: string,
    refreshToken?: string,
    expiresIn?: number
  ) {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        `oneup_access_token_${this.config.userId}`,
        accessToken
      );

      if (refreshToken) {
        localStorage.setItem(
          `oneup_refresh_token_${this.config.userId}`,
          refreshToken
        );
        this.refreshToken = refreshToken;
      }

      if (expiresIn) {
        const expirationDate = new Date(Date.now() + (expiresIn - 300) * 1000);
        localStorage.setItem(
          `oneup_token_expiration_${this.config.userId}`,
          expirationDate.toISOString()
        );
        this.tokenExpiration = expirationDate;
      }
    }
    this.accessToken = accessToken;
  }

  private clearTokens() {
    if (typeof window !== "undefined") {
      localStorage.removeItem(`oneup_access_token_${this.config.userId}`);
      localStorage.removeItem(`oneup_refresh_token_${this.config.userId}`);
      localStorage.removeItem(`oneup_token_expiration_${this.config.userId}`);
    }
    this.accessToken = null;
    this.refreshToken = null;
    this.tokenExpiration = null;
  }

  private isTokenExpired(): boolean {
    if (!this.tokenExpiration) return true;
    return new Date() >= this.tokenExpiration;
  }

  private async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) {
      console.error("No refresh token available");
      return await this.getNewAuthCodeAndTokens();
    }

    try {
      console.log("Refreshing access token...");

      const response = await client.mutations.refreshOneUpHealthToken({
        refreshToken: this.refreshToken,
      });

      const typedResponse = response as unknown as OneUpHealthTokenResponse;

      if (!typedResponse.data?.success) {
        console.error("Failed to refresh token:", typedResponse.data?.error);
        return await this.getNewAuthCodeAndTokens();
      }

      const tokenData = typedResponse.data.data;

      if (tokenData?.access_token) {
        console.log("Access token refreshed successfully");
        this.storeTokens(
          tokenData.access_token,
          tokenData.refresh_token || this.refreshToken,
          tokenData.expires_in
        );
        return true;
      } else {
        console.error("No access token in refresh response");

        return await this.getNewAuthCodeAndTokens();
      }
    } catch (error) {
      console.error("Error refreshing access token:", error);

      return await this.getNewAuthCodeAndTokens();
    }
  }

  private async getNewAuthCodeAndTokens(): Promise<boolean> {
    try {
      console.log("Getting new auth code and tokens...");

      const authCodeResponse = await client.mutations.getOneUpHealthAuthCode({
        appUserId: `jinix_${this.config.userId}`,
      });

      const typedAuthCodeResponse =
        authCodeResponse as unknown as OneUpHealthAuthCodeResponse;

      if (
        !typedAuthCodeResponse.data?.success ||
        !typedAuthCodeResponse.data?.data?.code
      ) {
        console.error(
          "Failed to get auth code:",
          typedAuthCodeResponse.data?.error
        );
        this.clearTokens();
        return false;
      }

      console.log("Got new auth code, exchanging for tokens...");

      const tokenResponse = await client.mutations.handleOneUpHealthCallback({
        provider: "1uphealth",
      });

      const typedTokenResponse =
        tokenResponse as unknown as OneUpHealthTokenResponse;

      if (
        !typedTokenResponse.data?.success ||
        !typedTokenResponse.data?.data?.access_token
      ) {
        console.error(
          "Failed to exchange auth code for tokens:",
          typedTokenResponse.data?.error
        );
        this.clearTokens();
        return false;
      }

      const tokenData = typedTokenResponse.data.data;

      if (tokenData.access_token) {
        console.log("Successfully got new tokens");
        this.storeTokens(
          tokenData.access_token,
          tokenData.refresh_token,
          tokenData.expires_in
        );
        return true;
      } else {
        console.error("No access token in token response:", tokenData);
        this.clearTokens();
        return false;
      }
    } catch (error) {
      console.error("Error getting new auth code and tokens:", error);
      this.clearTokens();
      return false;
    }
  }

  private async ensureValidToken(): Promise<boolean> {
    if (!this.accessToken) {
      console.log("No access token available");
      return false;
    }

    if (this.isTokenExpired()) {
      console.log("Token expired, attempting refresh...");
      return await this.refreshAccessToken();
    }

    return true;
  }

  async initialize(): Promise<boolean> {
    try {
      const response = await client.mutations.createOneUpHealthUser({
        appUserId: `jinix_${this.config.userId}`,
      });

      return !!response.data;
    } catch (error) {
      console.error("Failed to initialize 1upHealth service:", error);
      return false;
    }
  }

  async authorize(): Promise<boolean> {
    try {
      return await this.getNewAuthCodeAndTokens();
    } catch (error) {
      console.error("Authorization failed:", error);
      return false;
    }
  }

  async fetchPatientData(): Promise<PatientData | null> {
    return await this.fetchPatientDataFromDatabase();
  }

  async fetchMedicalRecords(): Promise<MedicalRecord[]> {
    return await this.fetchMedicalRecordsFromDatabase();
  }

  private processPatientResponse(responseData: any): PatientData | null {
    let entryData = responseData.data.entry;

    if (typeof entryData === "string") {
      entryData = JSON.parse(entryData);
    }

    if (!Array.isArray(entryData)) {
      if (entryData?.entry) {
        entryData = entryData.entry;
      } else if (entryData?.resource) {
        entryData = [entryData];
      }
    }

    const patientEntry = entryData.find(
      (entry: any) => entry?.resource?.resourceType === "Patient"
    );

    if (patientEntry) {
      return this.transformPatientData(patientEntry.resource);
    }

    return null;
  }

  private transformPatientData(patient: any): PatientData {
    let name = "Unknown";
    if (patient.name && patient.name.length > 0) {
      const nameData = patient.name[0];
      const given = nameData.given ? nameData.given.join(" ") : "";
      const family = nameData.family || "";
      name = `${given} ${family}`.trim() || "Unknown";
    }

    let address = "No address";
    let region = "Not specified";
    if (patient.address && patient.address.length > 0) {
      const addr = patient.address[0];
      const line = addr.line ? addr.line.join(", ") : "";
      const city = addr.city || "";
      const state = addr.state || "";
      const postalCode = addr.postalCode || "";
      const country = addr.country || "";
      address =
        `${line} ${city} ${state} ${postalCode} ${country}`.trim() ||
        "No address";
      region = country || state || "Not specified";
    }

    let phone = "No phone";
    let email = "No email";
    if (patient.telecom && patient.telecom.length > 0) {
      const phoneContact = patient.telecom.find(
        (t: any) => t.system === "phone"
      );
      const emailContact = patient.telecom.find(
        (t: any) => t.system === "email"
      );
      phone = phoneContact?.value || "No phone";
      email = emailContact?.value || "No email";
    }

    let race = "Not specified";
    const raceExtension = patient.extension?.find(
      (ext: any) =>
        ext.url ===
        "http://hl7.org/fhir/us/core/StructureDefinition/us-core-race"
    );
    if (raceExtension) {
      race =
        raceExtension.extension?.find((e: any) => e.url?.includes("text"))
          ?.valueString ||
        raceExtension.extension?.find((e: any) =>
          e.url?.includes("ombCategory")
        )?.valueCoding?.display ||
        "Not specified";
    }

    let ethnicity = "Not specified";
    const ethnicityExtension = patient.extension?.find(
      (ext: any) =>
        ext.url ===
        "http://hl7.org/fhir/us/core/StructureDefinition/us-core-ethnicity"
    );
    if (ethnicityExtension) {
      ethnicity =
        ethnicityExtension.extension?.find((e: any) => e.url?.includes("text"))
          ?.valueString || "Not specified";
    }

    const birthYear = patient.birthDate
      ? new Date(patient.birthDate).getFullYear().toString()
      : "Not specified";

    return {
      id: patient.id,
      name,
      gender: patient.gender || "Not specified",
      birthDate: patient.birthDate || "Not specified",
      birthYear,
      address,
      phoneNumber: phone,
      email,
      race,
      ethnicity,
      region,
    };
  }

  private transformMedicalRecord(resource: any): MedicalRecord | null {
    const resourceType = resource.resourceType;
    const date = this.extractDate(resource);

    if (!date) return null;

    switch (resourceType) {
      case "Encounter":
        return {
          id: resource.id,
          date: this.formatDate(date),
          type: "Outpatient Visit",
          location:
            resource.location?.[0]?.location?.display || "Unknown Location",
          doctor:
            resource.participant?.find(
              (p: any) => p.type?.[0]?.coding?.[0]?.code === "ATND"
            )?.individual?.display || "Unknown Doctor",
          description:
            resource.reasonCode?.[0]?.text ||
            resource.type?.[0]?.text ||
            "Routine visit",
          isClickable: true,
          rawData: resource,
        };

      case "Condition":
        return {
          id: resource.id,
          date: this.formatDate(date),
          type: "Symptom",
          description:
            resource.code?.text ||
            resource.code?.coding?.[0]?.display ||
            "Medical condition",
          isClickable: true,
          rawData: resource,
        };

      case "MedicationRequest":
        return {
          id: resource.id,
          date: this.formatDate(date),
          type: "Medication",
          description:
            resource.medicationCodeableConcept?.text ||
            resource.medicationCodeableConcept?.coding?.[0]?.display ||
            "Medication prescribed",
          isClickable: true,
          rawData: resource,
        };

      case "AllergyIntolerance":
        return {
          id: resource.id,
          date: this.formatDate(date),
          type: "Medication Reaction",
          description: `${resource.code?.text || resource.code?.coding?.[0]?.display || "Allergy"} - ${resource.reaction?.[0]?.manifestation?.[0]?.text || "Reaction noted"}`,
          isClickable: true,
          rawData: resource,
        };

      case "Observation":
        return {
          id: resource.id,
          date: this.formatDate(date),
          type: "Lab Result",
          description: `${resource.code?.text || resource.code?.coding?.[0]?.display || "Lab result"}: ${resource.valueQuantity?.value || resource.valueString || "Result available"}`,
          isClickable: true,
          rawData: resource,
        };

      default:
        return null;
    }
  }

  private extractDate(resource: any): Date | null {
    const dateFields = [
      "effectiveDateTime",
      "authoredOn",
      "recordedDate",
      "onsetDateTime",
      "period.start",
      "issued",
      "date",
    ];

    for (const field of dateFields) {
      const value = this.getNestedValue(resource, field);
      if (value) {
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
    }

    return null;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split(".").reduce((current, key) => current?.[key], obj);
  }

  private formatDate(date: Date): string {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  }

  isAuthorized(): boolean {
    return !!this.accessToken && !this.isTokenExpired();
  }

  logout() {
    this.clearTokens();
  }

  /**
   * Checks if an access token is available
   * @returns boolean indicating if access token exists
   */
  hasAccessToken(): boolean {
    return Boolean(this.accessToken);
  }

  /**
   * Get the patient resource ID.
   * This is the first step in checking connected health systems.
   */
  private async getPatientResourceId(): Promise<string | null> {
    if (this.patientId) {
      return this.patientId;
    }

    const hasValidToken = await this.ensureValidToken();
    if (!hasValidToken) {
      throw new Error("Not authorized. Please authorize first.");
    }

    try {
      const response = await client.queries.fetchOneUpHealthFhirData({
        accessToken: this.accessToken!,
        endpoint: "Patient",
        patientId: undefined,
        provider: undefined,
      });

      const typedResponse = response as unknown as OneUpHealthFhirDataResponse;

      if (!typedResponse.data?.success || !typedResponse.data.data?.entry) {
        return null;
      }

      let entryData = typedResponse.data.data.entry;

      if (typeof entryData === "string") {
        entryData = JSON.parse(entryData);
      }

      if (!Array.isArray(entryData)) {
        if (entryData?.entry) {
          entryData = entryData.entry;
        } else if (entryData?.resource) {
          entryData = [entryData];
        }
      }

      const patientEntry = entryData.find(
        (entry: any) => entry?.resource?.resourceType === "Patient"
      );

      if (patientEntry?.resource?.id) {
        this.patientId = patientEntry.resource.id;
        return this.patientId;
      }

      return null;
    } catch (error) {
      console.error("Failed to get patient resource ID:", error);
      throw error;
    }
  }

  /**
   * Query Provenance resources to identify connected health systems.
   * @returns Array of connected health systems with their information
   */
  async getConnectedHealthSystems(): Promise<ConnectedSystem[]> {
    const hasValidToken = await this.ensureValidToken();
    if (!hasValidToken) {
      throw new Error("Not authorized. Please authorize first.");
    }

    try {
      const patientId = await this.getPatientResourceId();
      if (!patientId) {
        console.log("No patient ID found");
        return [];
      }

      const response = await client.queries.fetchOneUpHealthFhirData({
        accessToken: this.accessToken!,
        endpoint: `Provenance?target=Patient/${patientId}`,
        patientId: undefined,
        provider: undefined,
      });

      const typedResponse = response as unknown as OneUpHealthFhirDataResponse;

      if (!typedResponse.data?.success || !typedResponse.data.data?.entry) {
        return [];
      }

      let entryData = typedResponse.data.data.entry;

      if (typeof entryData === "string") {
        entryData = JSON.parse(entryData);
      }

      if (!Array.isArray(entryData)) {
        if (entryData?.entry) {
          entryData = entryData.entry;
        } else if (entryData?.resource) {
          entryData = [entryData];
        } else {
          return [];
        }
      }

      const orgMap = new Map<string, ConnectedSystem>();

      entryData.forEach((entry: any) => {
        if (entry?.resource?.resourceType !== "Provenance") {
          return;
        }

        const agents = entry.resource.agent || [];
        agents.forEach((agent: any) => {
          if (
            agent.who?.reference &&
            agent.who.reference.startsWith("Organization/")
          ) {
            const orgId = agent.who.reference.replace("Organization/", "");

            if (!orgMap.has(orgId)) {
              orgMap.set(orgId, {
                id: orgId,
                name: agent.who.display || "Unknown Organization",
                systemId:
                  agent.onBehalfOf?.reference?.replace("Organization/", "") ||
                  "",
              });
            }
          }
        });
      });

      return Array.from(orgMap.values());
    } catch (error) {
      console.error("Failed to get connected health systems:", error);
      throw error;
    }
  }

  /**
   * Check if a specific health system is connected
   * @param systemId The ID of the health system to check
   * @returns Boolean indicating if the system is connected
   */
  async isHealthSystemConnected(systemId: string): Promise<boolean> {
    try {
      const connectedSystems = await this.getConnectedHealthSystems();
      return connectedSystems.some(
        (system) => system.id === systemId || system.systemId === systemId
      );
    } catch (error) {
      console.error(
        `Failed to check if system ${systemId} is connected:`,
        error
      );
      return false;
    }
  }

  setAccessToken(token: string) {
    this.accessToken = token;
    this.storeTokens(token);
  }

  /**
   * Fetch medical records from the database instead of 1upHealth
   * This provides faster access and works offline
   */
  async fetchMedicalRecordsFromDatabase(): Promise<MedicalRecord[]> {
    try {
      const { data: records } = await client.models.UsersMedicalRecords.list({
        filter: {
          resourceType: { ne: "Patient" },
        },
      });

      return records.map((record) => this.transformDatabaseRecord(record));
    } catch (error) {
      console.error("Failed to fetch medical records from database:", error);
      throw error;
    }
  }

  /**
   * Transform database record to MedicalRecord format
   */
  private transformDatabaseRecord(record: any): MedicalRecord {
    const resourceType = record.resourceType;

    // Parse rawData if it's a JSON string
    let rawData;
    try {
      rawData =
        typeof record.rawData === "string"
          ? JSON.parse(record.rawData)
          : record.rawData;
    } catch (parseError) {
      console.error("Error parsing rawData:", parseError);
      rawData = record.rawData;
    }

    let type: MedicalRecord["type"] = "Lab Result";
    switch (resourceType) {
      case "Condition":
        type = "Symptom";
        break;
      case "AllergyIntolerance":
        type = "Medication Reaction";
        break;
      case "MedicationRequest":
      case "Medication":
        type = "Medication";
        break;
      case "Encounter":
      case "Observation":
        type = "Outpatient Visit";
        break;
      default:
        type = "Lab Result";
    }

    let description = "Medical record";
    if (rawData) {
      switch (resourceType) {
        case "Condition":
          description =
            rawData.code?.text ||
            rawData.code?.coding?.[0]?.display ||
            "Medical condition";
          break;
        case "Medication":
        case "MedicationRequest":
          description =
            rawData.medicationCodeableConcept?.text ||
            rawData.medicationCodeableConcept?.coding?.[0]?.display ||
            "Medication";
          break;
        case "Observation":
          description =
            rawData.code?.text ||
            rawData.code?.coding?.[0]?.display ||
            "Observation";
          break;
        case "AllergyIntolerance":
          description =
            rawData.code?.text ||
            rawData.code?.coding?.[0]?.display ||
            "Allergy";
          break;
        case "Encounter":
          description =
            rawData.type?.[0]?.text ||
            rawData.type?.[0]?.coding?.[0]?.display ||
            "Healthcare visit";
          break;
        default:
          description =
            rawData.code?.text || rawData.text?.div || "Medical record";
      }
    }

    return {
      id: record.recordId,
      date: record.date
        ? new Date(record.date).toISOString()
        : new Date(record.lastUpdated).toISOString(),
      type,
      location: rawData?.serviceProvider?.display,
      doctor: this.extractDoctorName(rawData),
      description,
      isClickable: true,
      rawData: rawData,
    };
  }

  /**
   * Fetch patient data from the database
   */
  async fetchPatientDataFromDatabase(): Promise<PatientData | null> {
    try {
      const { data: records } = await client.models.UsersMedicalRecords.list({
        filter: {
          resourceType: { eq: "Patient" },
        },
      });

      if (records.length === 0) {
        return null;
      }

      const latestRecord = records.sort(
        (a, b) =>
          new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime()
      )[0];

      // Parse rawData if it's a JSON string
      let rawData;
      try {
        rawData =
          typeof latestRecord.rawData === "string"
            ? JSON.parse(latestRecord.rawData)
            : latestRecord.rawData;
      } catch (parseError) {
        console.error("Error parsing patient rawData:", parseError);
        rawData = latestRecord.rawData;
      }

      return this.transformPatientData(rawData);
    } catch (error) {
      console.error("Failed to fetch patient data from database:", error);
      return null;
    }
  }

  /**
   * Extract doctor name from FHIR resource
   */
  private extractDoctorName(resource: any): string | undefined {
    if (!resource) return undefined;

    const practitioners =
      resource.practitioner || resource.performer || resource.participant;
    if (
      practitioners &&
      Array.isArray(practitioners) &&
      practitioners.length > 0
    ) {
      return practitioners[0].display || practitioners[0].individual?.display;
    }

    const requester = resource.requester || resource.recorder;
    if (requester?.display) {
      return requester.display;
    }

    return undefined;
  }

  /**
   * Get sync status and last sync date
   */
  async getSyncStatus(): Promise<{
    lastSync: Date | null;
    recordCount: number;
  }> {
    try {
      const { data: records } = await client.models.UsersMedicalRecords.list({
        selectionSet: ["lastUpdated"],
      });

      if (records.length === 0) {
        return { lastSync: null, recordCount: 0 };
      }

      const lastSync = records
        .map((r) => new Date(r.lastUpdated))
        .sort((a, b) => b.getTime() - a.getTime())[0];

      return { lastSync, recordCount: records.length };
    } catch (error) {
      console.error("Failed to get sync status:", error);
      return { lastSync: null, recordCount: 0 };
    }
  }
}
