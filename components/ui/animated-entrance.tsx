'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

type EntranceDirection = 'up' | 'down' | 'left' | 'right' | 'scale';
type EntranceStyle = 'fade' | 'slide' | 'spring' | 'bounce';

interface AnimatedEntranceProps {
  children: ReactNode;
  direction?: EntranceDirection;
  style?: EntranceStyle;
  delay?: number;
  duration?: number;
  className?: string;
}

export function AnimatedEntrance({
  children,
  direction = 'up',
  style = 'spring',
  delay = 0,
  duration = 0.5,
  className = '',
}: AnimatedEntranceProps) {
  // Define initial states based on direction
  const getInitialState = () => {
    switch (direction) {
      case 'up':
        return { y: 30, opacity: 0 };
      case 'down':
        return { y: -30, opacity: 0 };
      case 'left':
        return { x: 30, opacity: 0 };
      case 'right':
        return { x: -30, opacity: 0 };
      case 'scale':
        return { scale: 0.9, opacity: 0 };
      default:
        return { opacity: 0 };
    }
  };

  // Define transition based on style
  const getTransition = () => {
    switch (style) {
      case 'fade':
        return { duration, delay, ease: 'easeOut' };
      case 'slide':
        return { duration, delay, ease: [0.16, 1, 0.3, 1] };
      case 'spring':
        return { 
          type: 'spring', 
          stiffness: 400, 
          damping: 30, 
          delay 
        };
      case 'bounce':
        return { 
          type: 'spring', 
          stiffness: 300, 
          damping: 10, 
          delay 
        };
      default:
        return { duration, delay };
    }
  };

  return (
    <motion.div
      initial={getInitialState()}
      animate={{ x: 0, y: 0, scale: 1, opacity: 1 }}
      transition={getTransition()}
      className={className}
    >
      {children}
    </motion.div>
  );
}
