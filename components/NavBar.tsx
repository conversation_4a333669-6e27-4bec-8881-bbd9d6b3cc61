"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import Me from "./me/Me";
import { useWindowSize } from "../hooks/useWindowSize";

export default function Navbar() {
  const pathname = usePathname();
  const router = useRouter();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalRendered, setModalRendered] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  // Use the hook - it returns true for desktop, false for mobile
  const isDesktop = useWindowSize();
  const isMobile = !isDesktop;

  useEffect(() => {
    if (isModalOpen && !isMobile) {
      setModalRendered(true);
      // Timeout to allow the element to be rendered before applying visible classes
      const timer = setTimeout(() => {
        setModalVisible(true);
      }, 10); // Small delay
      return () => clearTimeout(timer);
    } else {
      setModalVisible(false);
      // Timeout to allow fade-out animation to complete before removing from DOM
      const timer = setTimeout(() => {
        setModalRendered(false);
      }, 300); // Should match the transition duration
      return () => clearTimeout(timer);
    }
  }, [isModalOpen, isMobile]);

  const isActive = (path: string) =>
    pathname === path || pathname?.startsWith(path + "/");

  // Special check for discovery that also includes community paths - handle both spellings
  const isDiscoveryActive = () => {
    return (
      isActive("/discovery") ||
      pathname?.startsWith("/community") ||
      pathname?.startsWith("/comunity")
    );
  };

  const handleMeClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (isMobile) {
      // On mobile, navigate to the Me page
      router.push("/me");
    } else {
      // On desktop, open the modal
      setIsModalOpen(true);
    }
  };

  return (
    <div className="max-sm:bg-white max-sm:px-6 max-sm:py-4 md:h-full">
      <nav
        className="
         md:static
          md:bg-[#E9F0FF]
          md:left-0 md:top-0 md:bottom-0 
          bottom-[16px] left-[24px] right-[24px] 
          bg-[#F5F5F5]
          flex md:flex-col flex-row 
          justify-around md:justify-center items-center 
          md:w-[100px] w-full md:h-full h-[72px] 
          py-2 md:py-6 
          md:space-y-8 space-y-0 space-x-6 md:space-x-0 
          z-20 rounded-3xl md:rounded-none
          max-sm:left-0 max-sm:right-0
        "
        style={{
          backdropFilter: "blur(40px)",
          WebkitBackdropFilter: "blur(40px)",
        }}
      >
        <Link
          href="/chats"
          className={`flex md:flex-col flex-row items-center transition-all duration-500 ease-in-out ${isActive("/chats") ? "bg-[#5185FF] p-3 px-6 md:p-2 rounded-2xl md:w-[67px] md:h-[75px] md:flex md:justify-center" : "p-2 hover:scale-110"}`}
        >
          <Image
            src={isActive("/chats") ? "/vectir.svg" : "/bubble-chat.svg"}
            alt="Chats"
            width={24}
            height={24}
            className="transition-transform duration-500 ease-in-out"
            style={{
              filter: !isActive("/chats")
                ? "invert(67%) sepia(16%) saturate(2605%) hue-rotate(198deg) brightness(103%) contrast(101%)"
                : "none",
            }}
          />
          <span
            className={`md:text-sm md:mt-1 ml-2 md:ml-0 ${
              isActive("/chats")
                ? "text-white font-medium"
                : "text-[#7DA4FF] md:block hidden"
            } transition-all duration-500 ease-in-out`}
          >
            Chats
          </span>
        </Link>

        <Link
          href="/health"
          className={`flex md:flex-col flex-row items-center transition-all duration-500 ease-in-out ${isActive("/health") ? "bg-[#5185FF] p-3 px-6 md:p-2 rounded-2xl md:w-[67px] md:h-[75px] md:flex md:justify-center" : "p-2 hover:scale-110"}`}
        >
          <Image
            src={
              isActive("/health")
                ? "/lightblueheart.svg"
                : "/lightblueheart.svg"
            }
            alt="Health"
            width={24}
            height={24}
            className={`transition-transform duration-500 ease-in-out ${isActive("/health") ? "text-[#2567FF]" : ""}`}
          />
          <span
            className={`md:text-sm md:mt-1 ml-2 md:ml-0 ${
              isActive("/health")
                ? "text-white font-medium"
                : "text-[#7DA4FF] md:block hidden"
            } transition-all duration-500 ease-in-out`}
          >
            Health
          </span>
        </Link>

        <Link
          href="/discovery"
          className={`flex md:flex-col flex-row items-center transition-all duration-500 ease-in-out ${
            isDiscoveryActive()
              ? "bg-[#5185FF] p-3 px-6 md:p-2 rounded-2xl md:w-[67px] md:h-[75px] md:flex md:justify-center"
              : "p-2 hover:scale-110"
          }`}
          onClick={() =>
            console.log("Discovery clicked, isActive:", isDiscoveryActive())
          }
        >
          <Image
            src={isDiscoveryActive() ? "/compassw.svg" : "/compasss.svg"}
            alt="Discovery"
            width={24}
            height={24}
            className="transition-transform duration-500 ease-in-out"
            style={{
              filter: !isDiscoveryActive()
                ? "invert(67%) sepia(16%) saturate(2605%) hue-rotate(198deg) brightness(103%) contrast(101%)"
                : "none",
            }}
          />
          <span
            className={`md:text-sm md:mt-1 ml-2 md:ml-0 ${
              isDiscoveryActive()
                ? "text-white font-medium"
                : "text-[#7DA4FF] md:block hidden"
            } transition-all duration-500 ease-in-out`}
          >
            Discovery
          </span>
        </Link>

        <a
          onClick={handleMeClick}
          className={`flex md:flex-col flex-row items-center cursor-pointer transition-all duration-500 ease-in-out ${isActive("/me") ? "bg-[#5185FF] p-3 px-6 md:p-2 rounded-2xl md:w-[67px] md:h-[75px] md:flex md:justify-center" : "p-2 hover:scale-110"}`}
        >
          <Image
            src={isActive("/me") ? "/userw.svg" : "/user.svg"}
            alt="Me"
            width={24}
            height={24}
            className="transition-transform duration-500 ease-in-out"
            style={{
              filter: !isActive("/me")
                ? "invert(67%) sepia(16%) saturate(2605%) hue-rotate(198deg) brightness(103%) contrast(101%)"
                : "none",
            }}
          />
          <span
            className={`md:text-sm md:mt-1 ml-2 md:ml-0 ${
              isActive("/me")
                ? "text-white font-medium"
                : "text-[#7DA4FF] md:block hidden"
            } transition-all duration-500 ease-in-out`}
          >
            Me
          </span>
        </a>
      </nav>

      {/* Modal for desktop only */}
      {modalRendered && !isMobile && (
        <div
          className={`fixed inset-0 flex items-center justify-center z-50 transition-opacity duration-300 ${
            modalVisible ? "opacity-100" : "opacity-0"
          }`}
          onClick={() => setIsModalOpen(false)}
        >
          {/* Backdrop */}
          <div
            className={`absolute inset-0 bg-black/30 backdrop-blur-sm transition-opacity duration-300 ${
              modalVisible ? "opacity-100" : "opacity-0"
            }`}
          ></div>

          {/* Modal content */}
          <div
            className={`
              relative bg-white rounded-lg
              w-[95%] max-w-[800px] 
              h-[90vh] max-h-[600px] 
              overflow-hidden
              transform transition-all duration-300 shadow-xl
              ${modalVisible ? "opacity-100 scale-100" : "opacity-0 scale-95"}
            `}
            onClick={(e) => e.stopPropagation()}
          >
            <Me setIsModalOpen={setIsModalOpen} />
          </div>
        </div>
      )}
    </div>
  );
}
