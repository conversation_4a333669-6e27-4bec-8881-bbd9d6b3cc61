import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useState } from "react";
import { UserType } from "../../types/contacts/types";
import ContactRequestConfirmation from "../friends/ContactRequestConfirmation";

interface UserItemProps {
  user: UserType;
  onSendRequest: (userId: string) => Promise<void>;
  onRespondToRequest: (
    requestId: string,
    status: "ACCEPTED" | "DECLINED"
  ) => Promise<void>;
}

export default function UserItem({
  user,
  onSendRequest,
  onRespondToRequest,
}: UserItemProps) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [requestSent, setRequestSent] = useState(false);

  const handleSendRequest = async (userId: string) => {
    await onSendRequest(userId);
    setRequestSent(true);
    setShowConfirmation(true);
  };

  return (
    <>
      <div className="py-3 flex items-center">
        <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center mr-3 overflow-hidden">
          <Image
            src="/Avatar.png"
            alt={user.email || "user"}
            width={40}
            height={40}
          />
        </div>
        <span className="font-medium flex-1 truncate">{user.email}</span>

        {/* Friend request button states */}
        {!user.friendRequestStatus && !requestSent && (
          <Button
            className="bg-blue-500 hover:bg-blue-600 text-white rounded-full"
            onClick={() => handleSendRequest(user.userId)}
          >
            Add
          </Button>
        )}

        {(user.friendRequestStatus?.type === "sent" &&
          user.friendRequestStatus.status === "PENDING") ||
        requestSent ? (
          <Button
            variant="ghost"
            className="text-gray-700 p-0 cursor-default"
            disabled
          >
            Request Sent
          </Button>
        ) : null}

        {((user.friendRequestStatus?.type === "sent" &&
          user.friendRequestStatus.status === "ACCEPTED") ||
          (user.friendRequestStatus?.type === "received" &&
            user.friendRequestStatus.status === "ACCEPTED")) && (
          <Button
            variant="ghost"
            className="text-gray-700 p-0 cursor-default"
            disabled
          >
            Added
          </Button>
        )}

        {user.friendRequestStatus?.type === "received" &&
          user.friendRequestStatus.status === "PENDING" && (
            <div className="flex space-x-2">
              <Button
                variant="ghost"
                className="text-green-500 p-0"
                onClick={() => {
                  if (user.friendRequestStatus?.requestId) {
                    onRespondToRequest(
                      user.friendRequestStatus.requestId,
                      "ACCEPTED"
                    );
                  }
                }}
              >
                Accept
              </Button>
              <Button
                variant="ghost"
                className="text-red-500 p-0"
                onClick={() => {
                  if (user.friendRequestStatus?.requestId) {
                    onRespondToRequest(
                      user.friendRequestStatus.requestId,
                      "DECLINED"
                    );
                  }
                }}
              >
                Decline
              </Button>
            </div>
          )}
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <ContactRequestConfirmation
          onClose={() => setShowConfirmation(false)}
        />
      )}
    </>
  );
}
