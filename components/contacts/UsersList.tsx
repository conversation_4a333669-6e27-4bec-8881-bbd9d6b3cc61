import { UserType } from "../../types/contacts/types";
import UserItem from "./UserItem";

interface UsersListProps {
  users: UserType[];
  loading: boolean;
  onSendRequest: (userId: string) => Promise<void>;
  onRespondToRequest: (requestId: string, status: "ACCEPTED" | "DECLINED") => Promise<void>;
}

export default function UsersList({ users, loading, onSendRequest, onRespondToRequest }: UsersListProps) {
  if (loading) {
    return (
      <div className="flex justify-center p-8 ">
        <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  return (
    <div className="px-4 ">
      {users.length === 0 ? (
        <div className="bg-white rounded-lg p-8 text-center">
          <p className="text-gray-500">No users found</p>
        </div>
      ) : (
        <div className="bg-white rounded-lg pb-4">
          {users.map((user) => (
            <UserItem 
              key={user.userId} 
              user={user} 
              onSendRequest={onSendRequest}
              onRespondToRequest={onRespondToRequest}
            />
          ))}
        </div>
      )}
    </div>
  );
}
