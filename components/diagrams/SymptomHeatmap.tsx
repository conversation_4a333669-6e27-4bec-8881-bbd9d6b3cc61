import Image from "next/image";
import React, { useEffect, useState } from "react";
import { OneUpHealthService, MedicalRecord } from "@/lib/services/oneUpHealthService";

const heatColors = ["#FFE9A7", "#FFD47B", "#FFB76B", "#FF9E6B", "#FF6B6B"];

export default function SymptomHeatmap({ userId }: { userId: string }) {
  const [symptomLabels, setSymptomLabels] = useState<string[]>([]);
  const [dayLabels, setDayLabels] = useState<string[]>([]);
  const [symptomHeatmapData, setSymptomHeatmapData] = useState<number[][]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const service = new OneUpHealthService({ userId });
      try {
        const records: MedicalRecord[] = await service.fetchMedicalRecordsFromDatabase();
        const symptoms = records.filter(r => r.type === "Symptom");
        // Group by symptom and day (simulate days)
        const labels = Array.from(new Set(symptoms.map(s => s.description)));
        setSymptomLabels(labels);
        const days = Array.from({ length: 11 }, (_, i) => `D${i + 1}`);
        setDayLabels(days);
        // Simulate heatmap values
        const heatmap = labels.map(() =>
          days.map(() => Math.floor(Math.random() * 5))
        );
        setSymptomHeatmapData(heatmap);
      } catch (e) {
        setSymptomLabels([]);
        setDayLabels([]);
        setSymptomHeatmapData([]);
      }
      setLoading(false);
    };
    fetchData();
  }, [userId]);

  return (
    <div className="w-full md:w-[100%] mx-auto mb-4 bg-gray-100 p-3 rounded-[28px] border border-gray-200">
      <div className="p-4 bg-white rounded-[28px] shadow-sm border border-gray-200 flex flex-col">
        <div className="flex items-center mb-2">
          <Image
            src="/outpatient/heart-rate-monitor-blue.svg"
            alt="Heatmap Icon"
            width={24}
            height={24}
            className="mr-2"
          />
          <span className="font-semibold text-lg text-[#FFA800]">
            Presence and Severity of Common Symptoms
          </span>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full border-separate" style={{ borderSpacing: 8 }}>
            <thead>
              <tr>
                <th className="text-left text-sm text-gray-500"></th>
                {dayLabels.map((d) => (
                  <th key={d} className="text-center text-xs text-gray-500 font-normal">
                    {d}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr><td className="text-gray-400 text-center py-4">Loading...</td></tr>
              ) : symptomLabels.length === 0 ? (
                <tr><td className="text-gray-400 text-center py-4">No symptom data found</td></tr>
              ) : (
                symptomLabels.map((symptom, i) => (
                  <tr key={symptom}>
                    <td className="text-sm text-gray-700 font-medium">{symptom}</td>
                    {symptomHeatmapData[i].map((val, j) => (
                      <td key={j} className="text-center">
                        <div
                          style={{
                            width: 32,
                            height: 24,
                            borderRadius: 8,
                            background: val > 0 ? heatColors[val - 1] : "#F6F6F6",
                            display: "inline-block",
                            margin: "0 auto",
                          }}
                        />
                      </td>
                    ))}
                  </tr>
                ))
              )}
            </tbody>
          </table>
          <div className="flex items-center justify-end gap-1 mt-2 text-xs text-gray-500">
            <span>Less</span>
            {heatColors.map((c, i) => (
              <span
                key={i}
                style={{
                  background: c,
                  width: 18,
                  height: 12,
                  borderRadius: 4,
                  display: "inline-block",
                  marginLeft: 2,
                }}
              />
            ))}
            <span>More</span>
          </div>
        </div>
      </div>
    </div>
  );
}
