import React, { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { ChevronRightIcon } from "lucide-react";
import { useWindowSize } from "@/hooks/useWindowSize";
import { motion, AnimatePresence } from "framer-motion";
import {
  type MedicalRecord,
  type ConnectedSystem,
} from "@/lib/services/oneUpHealthService";
import GeneralInfoPanel from "./MedicalRecord/GeneralInfoPanel";
import TabSelector from "./MedicalRecord/TabSelector";
import RecordDetails, { EmptyState } from "./MedicalRecord/RecordDetails";
import TimelinePanel from "./MedicalRecord/TimelinePanel";
import { formatDate } from "./MedicalRecord/utils";

const defaultUserInfo = {
  name: "-",
  birthDate: "-",
  birthYear: "-",
  gender: "-",
  region: "-",
  race: "-",
  ethnicity: "-",
  maritalStatus: "-",
  email: "-",
  mobilePhone: "-",
  workPhone: "-",
  address: "-",
  primaryLanguage: "-",
  secondaryLanguage: "-",
};

interface MedicalRecordContentProps {
  isDataConnected: boolean;
  isLoading: boolean;
  userInfo?: {
    name: string;
    birthDate: string;
    birthYear: string;
    gender: string;
    region: string;
    race: string;
    ethnicity: string;
    maritalStatus: string;
    email: string;
    mobilePhone: string;
    workPhone: string;
    address: string;
    primaryLanguage: string;
    secondaryLanguage: string;
  };
  activeTab: string;
  displayRecords: MedicalRecord[];
  connectedSystems: ConnectedSystem[];
  onTabChange: (tab: string) => void;
  onRecordClick: (record: MedicalRecord) => void;
  selectedRecord?: MedicalRecord | null;
  isModal?: boolean;
  onSyncMedicalData?: () => Promise<void>;
  isSyncing?: boolean;
}

const MedicalRecordContent: React.FC<MedicalRecordContentProps> = (props) => {
  const {
    isDataConnected,
    isLoading,
    userInfo = defaultUserInfo,
    activeTab,
    displayRecords,
    connectedSystems,
    onTabChange,
    onRecordClick,
    selectedRecord = null,
    isModal = false,
  } = props;

  const router = useRouter();
  const isDesktop = useWindowSize();
  const [showGeneralInfo, setShowGeneralInfo] = useState(false);
  const safeUserInfo = userInfo || defaultUserInfo;

  const handleRecordClick = (record: MedicalRecord) => {
    setShowGeneralInfo(false);
    onRecordClick(record);
  };

  const toggleGeneralInfo = () => {
    if (!isDesktop && !isModal) {
      router.push("/medical-record/general-info");
      return;
    }
    if (showGeneralInfo) {
      setShowGeneralInfo(false);
    } else {
      setShowGeneralInfo(true);
      if (selectedRecord) {
        onRecordClick(null as any);
      }
    }
  };

  // Modal view implementation
  if (isModal) {
    return (
      <div className="flex flex-1 overflow-hidden">
        {/* Left Panel - Timeline */}
        <TimelinePanel
          activeTab={activeTab}
          onTabChange={onTabChange}
          displayRecords={displayRecords}
          isLoading={isLoading}
          onRecordClick={handleRecordClick}
          selectedRecord={selectedRecord}
          toggleGeneralInfo={toggleGeneralInfo}
          isGeneralInfoActive={showGeneralInfo}
          isModal={true}
        />

        {/* Right Panel - Record Details or General Info */}
        <div className="w-2/3 flex flex-col overflow-hidden h-[88vh]">
          <div className="flex-1 p-5 overflow-auto">
            <AnimatePresence mode="wait">
              {showGeneralInfo ? (
                <motion.div
                  key="general-info"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <GeneralInfoPanel
                    userInfo={safeUserInfo}
                    isDataConnected={isDataConnected}
                    connectedSystems={connectedSystems}
                  />
                </motion.div>
              ) : selectedRecord ? (
                <motion.div
                  key="record-details"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="flex justify-between items-center mb-5">
                    <div>
                      <h3 className="text-xl font-medium">
                        {selectedRecord.type}
                      </h3>
                      <p className="text-gray-500">
                        {formatDate(selectedRecord.date)}
                      </p>
                    </div>
                  </div>
                  <RecordDetails record={selectedRecord} />
                </motion.div>
              ) : (
                <EmptyState />
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    );
  }

  // Non-modal view implementation
  return (
    <div className="h-full px-6 bg-white rounded-3xl pb-5 pt-5  md:overflow-auto">
      {/* General Info Button */}
      <div
        className={`rounded-3xl h-[56px] pt-5 px-5 pb-5 border border-gray-200 cursor-pointer ${
          showGeneralInfo ? "bg-[#F5F5F5]" : "bg-white"
        } flex items-center`}
        onClick={toggleGeneralInfo}
      >
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <Image
              src="/medical-record/inputt.svg"
              alt="Info"
              width={24}
              height={24}
              className="mr-2"
            />
            <h2 className="text-blue-500 font-medium">General Info</h2>
          </div>
          <ChevronRightIcon className="h-5 w-5 text-black ml-2" />
        </div>
      </div>

      {/* Show the general info panel as a modal/drawer only on desktop */}
      <AnimatePresence>
        {isDesktop && showGeneralInfo && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-30 z-40"
            onClick={toggleGeneralInfo}
          />
        )}
        {isDesktop && showGeneralInfo && (
          <motion.div
            initial={{ right: -350, opacity: 0 }}
            animate={{ right: 0, opacity: 1 }}
            exit={{ right: -350, opacity: 0 }}
            className="fixed right-0 top-0 bottom-0 w-[350px] bg-white shadow-lg z-50 overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">General Info</h3>
                <button
                  onClick={toggleGeneralInfo}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
              <GeneralInfoPanel
                userInfo={safeUserInfo}
                isDataConnected={isDataConnected}
                connectedSystems={connectedSystems}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {isDataConnected && (
        <TabSelector activeTab={activeTab} onTabChange={onTabChange} />
      )}

      <TimelinePanel
        activeTab={activeTab}
        onTabChange={onTabChange}
        displayRecords={displayRecords}
        isLoading={isLoading}
        onRecordClick={onRecordClick}
        selectedRecord={selectedRecord}
        toggleGeneralInfo={toggleGeneralInfo}
        isGeneralInfoActive={showGeneralInfo}
      />
    </div>
  );
};

export default MedicalRecordContent;
