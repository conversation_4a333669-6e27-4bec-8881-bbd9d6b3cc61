'use client';

import { useState } from 'react';
import Image from 'next/image';

interface MedicationReactionModalProps {
  onClose: () => void;
  date?: string;
  reactionText?: string;
  medications?: Array<{
    name: string;
    dateRange: string;
    dosage: string;
    frequency: string;
    route: string;
    administrator: string;
    physician: string;
  }>;
}

const MedicationReactionModal = ({ 
  onClose, 
  date = "March 24, 2025",
  reactionText = "Patient reported occasional fatigue. Vitals within normal range. Routine labs ordered.",
  medications = [
    {
      name: "Hydroxychloroquine",
      dateRange: "March 14, 2025 - April 15, 2025",
      dosage: "500",
      frequency: "2",
      route: "Oral",
      administrator: "Self",
      physician: "Dr. <PERSON>"
    },
    {
      name: "Hydroxychloroquine",
      dateRange: "March 14, 2025 - April 15, 2025",
      dosage: "500",
      frequency: "2",
      route: "Oral",
      administrator: "Self",
      physician: "Dr. <PERSON>"
    }
  ]
}: MedicationReactionModalProps) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl w-[600px] h-[730px] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 ">
          <div className="flex items-center">
            <h2 className="text-[16px] font-semibold">Medication Reaction</h2>
            <span className="text-gray-500 ml-3">{date}</span>
          </div>
          <button onClick={onClose}>
            <Image src="/xrestuk.svg" alt="Close" width={24} height={24} />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-6">
          {/* Reaction Details */}
          <div className="mb-5 p-4   rounded-2xl border ">
            <div className="flex items-center mb-2">
              <div className="mr-2">
                <Image 
                  src="/outpatient/heart-rate-monitor.svg" 
                  alt="Reaction" 
                  width={28} 
                  height={28} 
                />
              </div>
              <h3 className="text-[#FF5C00] font-medium">Medication Reaction</h3>
            </div>
            <p className="text-gray-700">{reactionText}</p>
          </div>

          {/* Related Prescription */}
          <div className="border p-4 rounded-2xl ">
            <div className="flex items-center mb-4 ">
              <div className="mr-2">
                <Image 
                  src="/outpatient/bluecapsule.svg" 
                  alt="Prescription" 
                  width={28} 
                  height={28} 
                />
              </div>
              <h3 className="text-[#00A3FF] font-medium">Related Prescription</h3>
            </div>

            {/* Medication Cards */}
            {medications.map((med, index) => (
              <div key={index} className="mb-4 p-6 bg-[#F8F8F8] rounded-2xl">
                <h4 className="text-xl font-medium mb-1">{med.name}</h4>
                <p className="text-gray-500 text-sm mb-4">{med.dateRange}</p>
                
                <div className="grid grid-cols-2 gap-x-6 gap-y-4">
                  <div>
                    <p className="text-[#FF4D8D] text-xs mb-1">Dosage</p>
                    <p className="font-medium">{med.dosage} <span className="text-gray-500 text-sm">mmg</span></p>
                  </div>
                  
                  <div>
                    <p className="text-[#5F46FF] text-xs mb-1">Frequency</p>
                    <p className="font-medium">{med.frequency}<span className="text-gray-500 text-sm">/day</span></p>
                  </div>
                  
                  <div>
                    <p className="text-[#00A3FF] text-xs mb-1">Route</p>
                    <p className="font-medium">{med.route}</p>
                  </div>
                  
                  <div>
                    <p className="text-[#31C48D] text-xs mb-1">Administrator</p>
                    <p className="font-medium">{med.administrator}</p>
                  </div>
                  
                  <div className="col-span-2">
                    <p className="text-[#FF8AD8] text-xs mb-1">Prescribing Physician</p>
                    <p className="font-medium">{med.physician}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MedicationReactionModal;
