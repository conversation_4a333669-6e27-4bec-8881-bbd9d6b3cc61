'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import CustomButton from '@/components/ui/CustomButton';

interface AddMedicationModalProps {
  onClose: () => void;
  onSave: (medicationData: any) => void;
}

const AddMedicationModal = ({ onClose, onSave }: AddMedicationModalProps) => {
  const [noEndDate, setNoEndDate] = useState(false);
  const [formData, setFormData] = useState({
    medicationName: '',
    dosage: '',
    startDate: '',
    endDate: '',
    route: '',
    administrator: '',
    prescribingPhysician: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckbox = () => {
    setNoEndDate(!noEndDate);
    if (!noEndDate) {
      setFormData(prev => ({ ...prev, endDate: '' }));
    }
  };

  const handleSubmit = () => {
    onSave(formData);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl w-[600px] h-[610px] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 ">
          <h2 className="text-base font-medium">Add Medication</h2>
          <button onClick={onClose}>
            <Image src="/xrestuk.svg" alt="Close" width={24} height={24} />
          </button>
        </div>

        {/* Form fields */}
        <div className="px-6 overflow-y-auto">
          {/* Two column layout with specific full-width fields */}
          <div className="grid grid-cols-2 gap-x-4">
            {/* Medication Name */}
            <div className="mb-6">
              <label className="block mb-2 font-medium">Medication Name</label>
              <Input
                type="text"
                name="medicationName"
                value={formData.medicationName}
                onChange={handleChange}
                placeholder="e.g., Hydroxychloroquine"
                className="w-full bg-[#F5F5F5] border-none"
              />
            </div>

            {/* Dosage */}
            <div className="mb-5">
              <label className="block mb-2 font-medium">Dosage</label>
              <Input
                type="text"
                name="dosage"
                value={formData.dosage}
                onChange={handleChange}
                placeholder="e.g., 500 mmg"
                className="w-full bg-[#F5F5F5] border-none"
              />
            </div>

            {/* Start Date */}
            <div className="mb-5">
              <label className="block mb-2 font-medium">Start date</label>
              <div className="relative">
                <Input
                  type="text"
                  style={{height: '51px'}}
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleChange}
                  placeholder="MM / DD / YYYY"
                  className="w-full bg-[#F5F5F5] border-none"
                />
                <Image
                  src="/health/calender.svg"
                  alt="Calendar"
                  width={24}
                  height={24}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                />
              </div>
            </div>

            {/* End Date */}
            <div className="mb-2">
              <label className="block mb-2 font-medium">End date</label>
              <div className="relative" >
                <Input
                style={{height: '51px'}}
                  type="text"
                  name="endDate"
                  value={formData.endDate}
                  onChange={handleChange}
                  placeholder="MM / DD / YYYY"
                  disabled={noEndDate}
                  className={`w-full bg-[#F5F5F5] border-none ${noEndDate ? 'opacity-50' : ''}`}
                />
                <Image
                  src="/health/calender.svg"
                  alt="Calendar"
                  width={24}
                  height={24}
                  className={`absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer ${noEndDate ? 'opacity-50' : ''}`}
                />
              </div>
            </div>

            {/* No End Date Checkbox - in the second column */}
            <div className="">
              {/* Empty div to ensure proper spacing in first column */}
            </div>
            <div className=" flex items-center">
              <input
                type="checkbox"
                id="noEndDate"
                checked={noEndDate}
                onChange={handleCheckbox}
                className="mr-2 h-5 w-5 accent-blue-500"
              />
              <label htmlFor="noEndDate" className="text-gray-700">No end date</label>
            </div>
            
            {/* Route - spans full width */}
            <div className="col-span-2 mb-5">
              <label className="block mb-2 font-medium">Route</label>
              <Input
                type="text"
                name="route"
                value={formData.route}
                onChange={handleChange}
                placeholder="e.g., Oral"
                className="w-full bg-[#F5F5F5] border-none"
              />
            </div>

            {/* Administrator */}
            <div className="mb-6">
              <label className="block mb-2 font-medium">Administrator</label>
              <Input
                type="text"
                name="administrator"
                value={formData.administrator}
                onChange={handleChange}
                placeholder="e.g., Self"
                className="w-full bg-[#F5F5F5] border-none"
              />
            </div>

            {/* Prescribing Physician */}
            <div className="mb-6">
              <label className="block mb-2 font-medium">Prescribing Physician</label>
              <Input
                type="text"
                name="prescribingPhysician"
                value={formData.prescribingPhysician}
                onChange={handleChange}
                placeholder="e.g., Dr. Sarah Lee"
                className="w-full bg-[#F5F5F5] border-none"
              />
            </div>
          </div>

          {/* Save Button */}
          <div className="mt-5  mb-6">
            <CustomButton 
              onClick={handleSubmit}
            >
              Save
            </CustomButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddMedicationModal;
