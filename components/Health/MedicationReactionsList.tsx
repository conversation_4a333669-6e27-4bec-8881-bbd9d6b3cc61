'use client';

import Image from 'next/image';
import { useState } from 'react';
import MedicationReactionModal from './MedicationReactionModal';

// Define interfaces for our data structures
interface Medication {
  name: string;
  dateRange: string;
  dosage: string;
  frequency: string;
  route: string;
  administrator: string;
  physician: string;
}

interface Reaction {
  id: number;
  date: string;
  description: string;
  medications: Medication[];
}

// Update the component to accept an onClose prop
import React from 'react';

// Add onClose to the props interface
interface MedicationReactionsListProps {
  onClose: () => void;
}

const MedicationReactionsList: React.FC<MedicationReactionsListProps> = ({ onClose }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedReaction, setSelectedReaction] = useState<Reaction | null>(null);

  // Enhanced sample reaction data to match the screenshot
  const reactions: Reaction[] = [
    {
      id: 1,
      date: 'March 3, 2025',
      description: 'Patient reported occasional fatigue. Vitals within normal range. Routine labs ordered. Flu vaccine administered.',
      medications: [
        {
          name: "Hydroxychloroquine",
          dateRange: "March 14, 2025 - April 15, 2025",
          dosage: "500",
          frequency: "2",
          route: "Oral",
          administrator: "Self",
          physician: "Dr. <PERSON> <PERSON>"
        },
        {
          name: "Hydroxychloroquine",
          dateRange: "March 14, 2025 - April 15, 2025",
          dosage: "500",
          frequency: "2",
          route: "Oral",
          administrator: "Self",
          physician: "Dr. Sarah <PERSON>"
        }
      ]
    },
    {
      id: 2,
      date: 'March 24, 2025',
      description: 'Reaction description',
      medications: []
    },
    {
      id: 3,
      date: 'March 24, 2025',
      description: 'Reaction description',
      medications: []
    }
  ];

  const handleOpenModal = (reaction: Reaction) => {
    setSelectedReaction(reaction);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedReaction(null);
  };

  return (
    <div className="p-4">
      {/* Timeline with reaction entries */}
      <div className="ml-4 relative">
        {reactions.map((reaction, index) => (
          <div key={reaction.id} className="relative mb-8">
            {/* Timeline line */}
            {index < reactions.length - 1 && (
              <div className="absolute left-0 top-3 w-[2px] h-[calc(100%+32px)] bg-gray-200" 
                   style={{ transform: 'translateX(-50%)' }}></div>
            )}
            
            {/* Timeline dot - white circle with light border */}
            <div className="absolute left-0 top-3 w-3 h-3 bg-white border-2 border-gray-300 rounded-full z-10"
                 style={{ transform: 'translateX(-50%)' }}></div>
            
            {/* Date label */}
            <div className="ml-6 mb-2">
              <span className="bg-blue-100 text-blue-600 px-3 py-1 rounded-full text-xs">
                {reaction.date}
              </span>
            </div>
            
            {/* Reaction card with fixed height and proper content containment */}
            <div 
              className="ml-6 bg-white rounded-3xl border border-gray-100 shadow-sm h-[95px] flex flex-col overflow-hidden cursor-pointer"
              onClick={() => handleOpenModal(reaction)}
            >
              {/* Header with title and arrow */}
              <div className="flex justify-between items-center px-4 pt-3 pb-2 flex-shrink-0">
                <h3 className="font-medium text-base truncate">Medication Reaction</h3>
                <Image
                  src="/health/Arrow-right.svg"
                  alt="View"
                  width={20}
                  height={20}
                  className="flex-shrink-0"
                />
              </div>
              
              {/* Description container with properly contained content */}
              <div className="px-4 pb-3 flex-1 flex">
                <div className="bg-gray-100 p-2 rounded-lg flex items-center gap-2 w-full overflow-hidden">
                  <Image
                    src="/medical-record/note.svg"
                    alt="Description"
                    width={16}
                    height={16}
                    className="flex-shrink-0"
                  />
                  <span className="text-sm text-gray-600 truncate">{reaction.description}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Render the modal when open */}
      {isModalOpen && selectedReaction && (
        <MedicationReactionModal
          onClose={handleCloseModal}
          date={selectedReaction.date}
          reactionText={selectedReaction.description}
          medications={selectedReaction.medications}
        />
      )}
    </div>
  );
};

export default MedicationReactionsList;
