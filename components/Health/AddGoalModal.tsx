'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import CustomButton from '@/components/ui/CustomButton';
import { motion, AnimatePresence } from 'framer-motion';

interface AddGoalModalProps {
  onClose: () => void;
  onSave?: (goalData: any) => void;
}

const AddGoalModal = ({ onClose, onSave }: AddGoalModalProps) => {
  const [title, setTitle] = useState('');
  const [target, setTarget] = useState('');
  const [frequency, setFrequency] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [withoutEndDate, setWithoutEndDate] = useState(false);
  const [showFrequencyDropdown, setShowFrequencyDropdown] = useState(false);
  
  const frequencyOptions = ['day', 'week', 'month', 'year'];

  const handleSave = () => {
    const goalData = {
      title,
      target,
      frequency,
      startDate,
      endDate: withoutEndDate ? null : endDate,
      withoutEndDate
    };
    
    if (onSave) {
      onSave(goalData);
    }
    onClose();
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.97, y: 30 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.97, y: 30 }}
        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
        className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[1001]"
      >
        <div className="bg-white rounded-2xl w-[600px] h-[530px] ">
          {/* Header */}
          <div className="flex justify-between items-center px-6 py-4 sticky top-0 bg-white z-10 rounded-t-2xl">
            <h2 className="text-xl font-medium">Set Goal</h2>
            <button onClick={onClose}>
              <Image src="/xrestuk.svg" alt="Close" width={24} height={24} />
            </button>
          </div>

  
          <div className="px-6 py-5 " style={{ height: 'calc(520px - 66px)' }}>
        
            <div className="mb-6">
              <label className="block mb-2 font-medium">Title</label>
              <Input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder='e.g., "Improve My Sleep Routine"'
                className="w-full bg-[#F5F5F5] border-none"
              />
            </div>

            {/* Target - 2 Columns */}
            <div className="mb-6">
              <label className="block mb-2 font-medium">Target</label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-sm mb-2 text-[#929292]">I want to</div>
                  <Input
                    type="text"
                    value={target}
                    onChange={(e) => setTarget(e.target.value)}
                    placeholder="e.g., walk 8000 steps"
                    className="w-full bg-[#F5F5F5] border-none"
                  />
                </div>
                
                <div>
                  <div className="text-sm mb-2">per</div>
                  <div className="relative">
                    <div 
                      className="w-full bg-[#F5F5F5] border-none rounded-3xl  h-[56px] py-2 px-3 flex justify-between items-center cursor-pointer"
                      onClick={() => setShowFrequencyDropdown(!showFrequencyDropdown)}
                    >
                      <span className="text-gray-500">{frequency || 'Select an option'}</span>
                      <Image
                        src="/health/chevron-down.svg"
                        alt="Select"
                        width={24}
                        height={24}
                      />
                    </div>
                    
                    {showFrequencyDropdown && (
                      <div className="absolute z-10 mt-1 w-full bg-white rounded-lg shadow-lg border">
                        {frequencyOptions.map((option) => (
                          <div 
                            key={option}
                            className="p-2 hover:bg-gray-100 cursor-pointer"
                            onClick={() => {
                              setFrequency(option);
                              setShowFrequencyDropdown(false);
                            }}
                          >
                            {option}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Date Fields - 2 Columns */}
            <div className="grid grid-cols-2 gap-4 mb-3">
              {/* Start date */}
              <div>
                <label className="block mb-2 font-medium">Start date</label>
                <div className="relative">
                  <Input
                    type="text"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    placeholder="MM / DD / YYYY"
                    className="w-full bg-[#F5F5F5] border-none pr-10"
                  />
                  <Image
                    src="/health/calender.svg"
                    alt="Calendar"
                    width={24}
                    height={24}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  />
                </div>
              </div>

              {/* End date */}
              <div>
                <label className="block mb-2 font-medium">End date</label>
                <div className="relative">
                  <Input
                    type="text"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    placeholder="MM / DD / YYYY"
                    disabled={withoutEndDate}
                    className={`w-full bg-[#F5F5F5] border-none pr-10 ${withoutEndDate ? 'opacity-50' : ''}`}
                  />
                  <Image
                    src="/health/calender.svg"
                    alt="Calendar"
                    width={24}
                    height={24}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  />
                </div>
                
                {/* Without end date checkbox */}
                <div className="mt-2 flex items-center">
                  <input
                    type="checkbox"
                    id="withoutEndDate"
                    checked={withoutEndDate}
                    onChange={() => setWithoutEndDate(!withoutEndDate)}
                    className="mr-2 h-5 w-5 accent-blue-500"
                  />
                  <label htmlFor="withoutEndDate" className="text-gray-700">
                    Without end date
                  </label>
                </div>
              </div>
            </div>

            {/* Save Button */}
            <CustomButton onClick={handleSave}>
              Save
            </CustomButton>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AddGoalModal;
