import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';

type GoalCardProps = {
  goalId: string;
  title: string;
  dateRange: string;
  targetLabel: string;
  targetValue: React.ReactNode;
  selectedGoalId: string | null;
  onOpenMenu: (goalId: string) => void;
  onDeleteClick: (goalId: string) => void;
  menuVariants: any;
};

export default function GoalCard({
  goalId,
  title,
  dateRange,
  targetLabel,
  targetValue,
  selectedGoalId,
  onOpenMenu,
  onDeleteClick,
  menuVariants,
}: GoalCardProps) {
  return (
    <div className="bg-gray-100 rounded-xl p-4 mb-4 relative h-[130px]">
      <div className="flex justify-between h-[19px]">
        <h3 className="font-medium text-[16px] mb-1 h-[19px]">{title}</h3>
        <div className="relative">
          <button onClick={() => onOpenMenu(goalId)}>
            <div className="flex flex-col items-center justify-center w-7 h-7">
              <div className="w-1.5 h-1.5 rounded-full bg-gray-700 mb-0.5"></div>
              <div className="w-1.5 h-1.5 rounded-full bg-gray-700 mb-0.5"></div>
              <div className="w-1.5 h-1.5 rounded-full bg-gray-700"></div>
            </div>
          </button>
          <AnimatePresence mode="wait">
            {selectedGoalId === goalId && (
              <motion.div 
                className="absolute right-0 mt-2 w-40 bg-white rounded-3xl shadow-lg z-10"
                variants={menuVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                <div className="p-4 flex items-center cursor-pointer hover:bg-gray-50">
                  <Image 
                    src="/health/edit.svg" 
                    alt="Edit" 
                    width={20} 
                    height={20}
                    className="mr-4" 
                  />
                  <span className="text-lg">Edit</span>
                </div>
                <div 
                  className="p-4 flex items-center border-t cursor-pointer hover:bg-gray-50"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteClick(goalId);
                  }}
                >
                  <Image 
                    src="/health/trash.svg" 
                    alt="Delete" 
                    width={20} 
                    height={20}
                    className="mr-4" 
                  />
                  <span className="text-lg">Delete</span>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
      <p className="text-[14px] font-medium text-[#929292] mb-3">{dateRange}</p>
      <div>
        <p className="text-[14px] text-[#4285F4]">{targetLabel}</p>
        <p className="font-medium text-[20px]">{targetValue}</p>
      </div>
    </div>
  );
}
