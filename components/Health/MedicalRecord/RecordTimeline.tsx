import { motion } from "framer-motion";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { type MedicalRecord } from "@/lib/services/oneUpHealthService";
import RecordCard from "./RecordCard";
import HealthSystemSettings from "@/components/Health/HealthSystemSettings";
import { useWindowSize } from "@/hooks/useWindowSize";

interface RecordTimelineProps {
  records: MedicalRecord[];
  isLoading: boolean;
  selectedRecord: MedicalRecord | null;
  onRecordClick: (record: MedicalRecord) => void;
  isModal?: boolean;
  userId?: string;
}

const RecordTimeline: React.FC<RecordTimelineProps> = ({
  records,
  isLoading,
  selectedRecord,
  onRecordClick,
  isModal = false,
  userId,
}) => {
  const router = useRouter();
  const [showHealthSettings, setShowHealthSettings] = useState(false);
  const isDesktop = useWindowSize();

  if (isLoading) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center h-[300px]"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"></div>
        <p className="text-gray-500">Loading medical records...</p>
      </motion.div>
    );
  }

  if (records.length === 0) {
    return (
      <motion.div
        className="flex flex-col items-center justify-center h-[300px]"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <p className="text-gray-500 mb-6">There's no data found</p>
        <button
          onClick={() => {
            if (isDesktop) {
              // For desktop, show the modal
              setShowHealthSettings(true);
            } else {
              // For mobile, navigate to settings page
              router.push("/setting");
            }
          }}
          className="bg-blue-500 text-white font-medium py-3 px-6 rounded-2xl w-full max-w-md"
        >
          Connect Medical Data
        </button>

        {showHealthSettings && (
          <HealthSystemSettings onClose={() => setShowHealthSettings(false)} />
        )}
      </motion.div>
    );
  }

  return (
    <div
      className={`flex  flex-col space-y-4 ${isModal ? "" : "md:overflow-auto overflow-auto h-[65vh]"}`}
    >
      {records.map((record, index) => (
        <RecordCard
          key={record.id}
          record={record}
          isSelected={selectedRecord?.id === record.id}
          index={index}
          onClick={onRecordClick}
          isModal={isModal}
        />
      ))}
    </div>
  );
};

export default RecordTimeline;
