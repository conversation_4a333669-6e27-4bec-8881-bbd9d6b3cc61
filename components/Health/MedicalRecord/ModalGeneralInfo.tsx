import { motion } from "framer-motion";
import { type ConnectedSystem } from "@/lib/services/oneUpHealthService";
import Image from "next/image";

interface ModalGeneralInfoProps {
  userInfo: {
    birthYear: string;
    gender: string;
    region: string;
    race: string;
    ethnicity: string;
  };
  isDataConnected: boolean;
  connectedSystems: ConnectedSystem[];
  onClose: () => void;
}

const ModalGeneralInfo: React.FC<ModalGeneralInfoProps> = ({
  userInfo,
  isDataConnected, 
  connectedSystems,
  onClose
}) => {
  // Helper function to display value or dash
  const displayValue = (value?: string): string => {
    return value && value !== "-" ? value : "-";
  };

  return (
    <>
      <motion.div 
        initial={{ opacity: 0 }} 
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-30 z-40"
        onClick={onClose}
      />
      <motion.div 
        initial={{ right: -350, opacity: 0 }} 
        animate={{ right: 0, opacity: 1 }}
        exit={{ right: -350, opacity: 0 }}
        className="fixed right-0 top-0 bottom-0 w-[350px] bg-white shadow-lg z-50 overflow-y-auto"
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">General Info</h3>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-y-4">
              <span className="text-gray-500">Birth Year</span>
              <span className="text-right font-medium">
                {displayValue(userInfo.birthYear)}
              </span>

              <span className="text-gray-500">Gender</span>
              <span className="text-right font-medium">
                {displayValue(userInfo.gender)}
              </span>

              <span className="text-gray-500">Region</span>
              <span className="text-right font-medium">
                {displayValue(userInfo.region)}
              </span>

              <span className="text-gray-500">Race</span>
              <span className="text-right font-medium">
                {displayValue(userInfo.race)}
              </span>

              <span className="text-gray-500">Ethnicity</span>
              <span className="text-right font-medium">
                {displayValue(userInfo.ethnicity)}
              </span>
            </div>
          </div>

          {isDataConnected && connectedSystems.length > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center mb-3">
                <Image
                  src="/heart-beat.svg"
                  alt="Connected Systems"
                  width={24}
                  height={24}
                  className="mr-2"
                />
                <h3 className="text-blue-500 font-medium">
                  Connected Health Systems
                </h3>
              </div>
              <div className="space-y-2">
                {connectedSystems.map((system) => (
                  <div
                    key={system.id}
                    className="flex items-center bg-white rounded-lg p-2 border border-gray-100"
                  >
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-sm">{system.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </>
  );
};

export default ModalGeneralInfo;
