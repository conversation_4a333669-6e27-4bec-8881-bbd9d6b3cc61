import Image from "next/image";
import { type ConnectedSystem } from "@/lib/services/oneUpHealthService";

interface GeneralInfoPanelProps {
  userInfo: {
    name: string;
    birthDate: string;
    birthYear: string;
    gender: string;
    region: string;
    race: string;
    ethnicity: string;
    maritalStatus: string;
    email: string;
    mobilePhone: string;
    workPhone: string;
    address: string;
    primaryLanguage: string;
    secondaryLanguage: string;
  };
  isDataConnected: boolean;
  connectedSystems: ConnectedSystem[];
}

const GeneralInfoPanel: React.FC<GeneralInfoPanelProps> = ({
  userInfo,
  isDataConnected,
  connectedSystems,
}) => {
  // Helper function to display value or dash
  const displayValue = (value: string): string => {
    return value && value !== "-" ? value : "-";
  };
  console.log("GeneralInfoPanel userInfo:", userInfo);
  return (
    <div className="space-y-6">
      {/* Summary */}
      <div className="bg-[#F5F5F5] rounded-3xl p-6">
        <div className="mb-2">
          <span className="font-semibold text-[16px]">Summary</span>
        </div>
        <div className="grid grid-cols-2 gap-y-2 text-[15px]">
          {/* Left column */}
          <div>
            <div className="text-gray-500">Name</div>
            <div className="font-semibold">{displayValue(userInfo.name)}</div>
          </div>
          <div>
            <div className="text-gray-500">Gender</div>
            <div className="font-semibold">{displayValue(userInfo.gender)}</div>
          </div>
          <div>
            <div className="text-gray-500">Date of Birth</div>
            <div className="font-semibold">{displayValue(userInfo.birthDate)}</div>
          </div>
          <div>
            <div className="text-gray-500">Marital Status</div>
            <div className="font-semibold">{displayValue(userInfo.maritalStatus)}</div>
          </div>
          <div>
            <div className="text-gray-500">Alive</div>
            <div className="font-semibold">Yes</div>
          </div>
          <div>
            <div className="text-gray-500">Region</div>
            <div className="font-semibold">{displayValue(userInfo.region)}</div>
          </div>
          <div>
            <div className="text-gray-500">Race</div>
            <div className="font-semibold">{displayValue(userInfo.race)}</div>
          </div>
          <div>
            <div className="text-gray-500">Ethnicity</div>
            <div className="font-semibold">{displayValue(userInfo.ethnicity)}</div>
          </div>
        </div>
      </div>

      {/* Contact & Address */}
      <div className="bg-[#F5F5F5] rounded-3xl p-6">
        <div className="mb-2">
          <span className="font-semibold text-[16px]">Contact & Address</span>
        </div>
        <div className="space-y-2 text-[15px]">
          <div>
            <div className="text-gray-500">Email</div>
            <div className="font-semibold break-all">{displayValue(userInfo.email)}</div>
          </div>
          <div>
            <div className="text-gray-500">Mobile Phone</div>
            <div className="font-semibold">{displayValue(userInfo.mobilePhone)}</div>
          </div>
          <div>
            <div className="text-gray-500">Work Phone</div>
            <div className="font-semibold">{displayValue(userInfo.workPhone)}</div>
          </div>
          <div>
            <div className="text-gray-500">Home Address</div>
            <div className="font-semibold whitespace-pre-line">{displayValue(userInfo.address)}</div>
          </div>
        </div>
      </div>

      {/* Communication Preferences */}
      <div className="bg-[#F5F5F5] rounded-3xl p-6">
        <div className="mb-2">
          <span className="font-semibold text-[16px]">Communication Preferences</span>
        </div>
        <div className="space-y-2 text-[15px]">
          <div>
            <div className="text-gray-500">Primary Language</div>
            <div className="font-semibold">{displayValue(userInfo.primaryLanguage)}</div>
          </div>
          <div>
            <div className="text-gray-500">Secondary Language</div>
            <div className="font-semibold">{displayValue(userInfo.secondaryLanguage)}</div>
          </div>
          <div>
            
          </div>
        </div>
      </div>

      {/* Connected Health Systems */}
      {isDataConnected && connectedSystems.length > 0 && (
        <div className="bg-[#F5F5F5] rounded-3xl p-6">
          <div className="flex items-center mb-3">
            <Image
              src="/heart-beat.svg"
              alt="Connected Systems"
              width={24}
              height={24}
              className="mr-2"
            />
            <h3 className="text-blue-500 font-medium">
              Connected Health Systems
            </h3>
          </div>
          <div className="space-y-2">
            {connectedSystems.map((system) => (
              <div
                key={system.id}
                className="flex items-center bg-white rounded-lg p-2 border border-gray-100"
              >
                <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                <span className="text-sm">{system.name}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default GeneralInfoPanel;
