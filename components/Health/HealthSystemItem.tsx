import { useState, Component, ErrorInfo } from "react";
import Image from "next/image";
import { Skeleton } from "@/components/ui/skeleton";

interface HealthSystem {
  id: string;
  name: string;
  logo?: string;
  ehr: string;
}

interface HealthSystemItemProps {
  system: HealthSystem;
  connectingSystem: string | null;
  handleConnectSystem: (systemId: string) => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
}

// Error Boundary Component
class HealthSystemItemErrorBoundary extends Component<
  { children: React.ReactNode },
  ErrorBoundaryState
> {
  state: ErrorBoundaryState = { hasError: false };

  static getDerivedStateFromError(_: Error): ErrorBoundaryState {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Error in HealthSystemItem:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="flex flex-row sm:justify-between sm:items-center bg-gray-50 rounded-lg p-4 min-h-[65px] gap-3 sm:gap-0">
          <div className="flex-1 flex items-center gap-3">
            <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />
            <div className="min-w-0">
              <div className="font-medium text-gray-500">
                Error loading system
              </div>
              <div className="text-gray-400 text-sm">Please try again</div>
            </div>
          </div>
          <button
            className="px-5 py-2 rounded-full text-sm w-auto bg-gray-300 text-white cursor-not-allowed"
            disabled
          >
            Connect
          </button>
        </div>
      );
    }
    return this.props.children;
  }
}

const HealthSystemItem = ({
  system,
  connectingSystem,
  handleConnectSystem,
}: HealthSystemItemProps) => {
  const [isImageBroken, setIsImageBroken] = useState(false);

  // Ensure name and ehr have fallback values
  const displayName = system.name || "Unknown System";
  const displayEhr = system.ehr || "N/A";

  return (
    <HealthSystemItemErrorBoundary>
      <div
        className="flex flex-row sm:justify-between sm:items-center bg-gray-50 rounded-lg p-4 min-h-[65px] gap-3 sm:gap-0 w-full"
        role="listitem"
      >
        <div className="flex-1 flex items-center gap-3 min-w-0">
          {system.logo && !isImageBroken ? (
            <Image
              src={system.logo}
              alt={displayName}
              width={32}
              height={32}
              className="rounded-full flex-shrink-0"
              onError={() => setIsImageBroken(true)}
              unoptimized
            />
          ) : (
            <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />
          )}
          <div className="min-w-0 flex-1">
            <div className="font-medium truncate" title={displayName}>
              {displayName}
            </div>
            <div className="text-gray-500 text-sm truncate" title={displayEhr}>
              {displayEhr}
            </div>
          </div>
        </div>
        <button
          className={`px-5 py-2 rounded-full text-sm w-auto flex-shrink-0 ${
            connectingSystem === system.id
              ? "bg-blue-300 text-white cursor-not-allowed"
              : "bg-blue-500 hover:bg-blue-600 text-white"
          }`}
          onClick={() => handleConnectSystem(system.id)}
          disabled={connectingSystem === system.id}
          aria-label={`Connect to ${displayName}`}
        >
          {connectingSystem === system.id ? "Connecting..." : "Connect"}
        </button>
      </div>
    </HealthSystemItemErrorBoundary>
  );
};

export default HealthSystemItem;
