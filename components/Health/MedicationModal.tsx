'use client';

import { useState } from 'react';
import Image from 'next/image';
import MedicationReactionsList from './MedicationReactionsList';
import MedicationDetailsList from './MedicationDetailsList';
import AddMedicationReactionModal from './AddMedicationReactionModal';

interface MedicationModalProps {
  onClose: () => void;
}

const MedicationModal = ({ onClose }: MedicationModalProps) => {
  const [showReactionModal, setShowReactionModal] = useState(false);

  const handleSaveReaction = (reactionData: any) => {
    console.log('Reaction data:', reactionData);
    // Here you would save the reaction data
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-end items-stretch z-50 py-6 pr-6">
      {/* Left Panel with Reactions */}
      <div className="bg-white w-[940px] h-full rounded-2xl overflow-hidden flex">
        <div className="w-[360px] border-r overflow-hidden flex flex-col">
          {/* Left header */}
          <div className="flex items-center justify-between p-4">
            <h1 className="text-base font-medium">Medication</h1>
            {/* Add Reaction button */}
            <button 
              className="flex items-center space-x-1"
              onClick={() => setShowReactionModal(true)}
            >
              <Image
                src="/plus.svg"
                alt="Add Reaction"
                width={24}
                height={24}
                className="w-6 h-6"
              />
              <span>Reaction</span>
            </button>
          </div>
          
          {/* Reactions list */}
          <div className="flex-1 overflow-y-auto">
            <MedicationReactionsList onClose={onClose} />
          </div>
        </div>

        {/* Right Panel with Medication Details */}
        <div className="flex-1 overflow-hidden flex flex-col">
          {/* Right header */}
          <div className="flex justify-end p-4">
            <button onClick={onClose}>
              <Image src="/xrestuk.svg" alt="Close" width={24} height={24} />
            </button>
          </div>
          
          {/* Medication details */}
          <div className="flex-1 overflow-y-auto">
            <MedicationDetailsList onClose={onClose} />
          </div>
        </div>
      </div>

      {/* Reaction Modal */}
      {showReactionModal && (
        <AddMedicationReactionModal 
          onClose={() => setShowReactionModal(false)} 
          onSave={handleSaveReaction}
        />
      )}
    </div>
  );
};

export default MedicationModal;
