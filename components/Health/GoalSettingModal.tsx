'use client';

import { useState } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import CustomButton from '@/components/ui/CustomButton';
import AddGoalModal from './AddGoalModal';
import DeleteConfirmationModal from './DeleteConfirmationModal';

interface GoalSettingModalProps {
  onClose: () => void;
}

const GoalSettingModal = ({ onClose }: GoalSettingModalProps) => {
  const [selectedGoalId, setSelectedGoalId] = useState<string | null>(null);
  const [showAddGoalModal, setShowAddGoalModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [goalToDelete, setGoalToDelete] = useState<string | null>(null);
  
  const handleOpenMenu = (goalId: string) => {
    setSelectedGoalId(selectedGoalId === goalId ? null : goalId);
  };

  const handleDeleteClick = (goalId: string) => {
    console.log(`Delete clicked for goal: ${goalId}`);
    setGoalToDelete(goalId);
    setShowDeleteModal(true);
    setSelectedGoalId(null); // Close the menu
  };

  const handleConfirmDelete = () => {
    // Here you would implement the actual deletion logic
    console.log(`Deleting goal: ${goalToDelete}`);
    setShowDeleteModal(false);
    setGoalToDelete(null);
  };

  // Animation variants for staggered appearance
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.18,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.35 } },
  };

  return (
    <>
      <AnimatePresence mode="wait">
        <motion.div
          key="goal-setting-modal-overlay"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.25 }}
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[1000]"
        >
          <motion.div
            key="goal-setting-modal-box"
            variants={containerVariants}
            initial="hidden"
            animate="show"
            exit="hidden"
            className="bg-white rounded-2xl w-[800px] h-[800px] overflow-hidden flex flex-col"
          >
            {/* Header */}
            <motion.div
              variants={itemVariants}
              className="flex justify-between items-center px-6 py-4 sticky top-0 bg-white z-10 rounded-t-2xl"
            >
              <h1 className="text-[16px] font-medium">Set Goal</h1>
              <button onClick={onClose} className="z-[101]">
                <Image src="/xrestuk.svg" alt="Close" width={24} height={24} />
              </button>
            </motion.div>

            {/* Content */}
            <motion.div
              variants={itemVariants}
              className="flex-1 px-6 py-5 overflow-y-auto"
              style={{ height: 'calc(750px - 78px)' }}
            >
              {/* Current Goals Section */}
              <motion.div
                variants={itemVariants}
                className="mb-6"
              >
                <div className="flex items-center mb-4">
                  <div className="text-[#27A8E4] mr-2">
                    <Image
                      src="/health/target.svg" 
                      alt="Current Goals"
                      width={24}
                      height={24}
                      className="w-6 h-6"
                    />
                  </div>
                  <h2 className="text-[#27A8E4] text-[16px] font-semibold">Current Goals</h2>
                </div>

                {/* Goal 1 */}
                <div className="bg-gray-100 rounded-xl px-[18px] py-4 mb-6 ">
                  <div className="flex justify-between">
                    <h3 className="font-medium text-[16px]">Get More Walk</h3>
                    <div className="relative">
                      <button 
                        onClick={() => handleOpenMenu('goal1')}
                        type="button"
                        className="p-1"
                      >
                        <Image
                          src="/menu.svg"
                          alt="Menu"
                          width={24}
                          height={24}
                        />
                      </button>
                      
                      {selectedGoalId === 'goal1' && (
                        <div className="absolute right-0 top-full mt-1 bg-white rounded-3xl shadow-lg z-10 w-[140px] overflow-hidden">
                          <div className="flex items-center p-3 hover:bg-gray-50 cursor-pointer">
                            <Image
                              src="/health/edit.svg"
                              alt="Edit"
                              width={20}
                              height={20}
                              className="mr-2"
                            />
                            <span>Edit</span>
                          </div>
                          <div 
                            className="flex items-center p-3 hover:bg-gray-50 cursor-pointer border-t"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent event bubbling
                              handleDeleteClick('goal1');
                            }}
                          >
                            <Image
                              src="/health/trash.svg"
                              alt="Delete"
                              width={20}
                              height={20}
                              className="mr-2"
                            />
                            <span>Delete</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <p className="text-[14px] text-gray-500 mb-3">March 14, 2025 - April 15, 2025</p>
                  
                  <div>
                    <p className="text-[14px] text-[#4285F4]">Target</p>
                    <p className="font-medium text-[20px] ">Walk 3 times, 20 mins each <span className="text-gray-400">per day</span></p>
                  </div>
                </div>

                {/* Goal 2 */}
                <div className="bg-gray-100 rounded-xl p-4 mb-4 ">
                  <div className="flex justify-between">
                    <h3 className="font-medium text-lg">Get Better Sleep</h3>
                    <div className="relative">
                      <button 
                        onClick={() => handleOpenMenu('goal2')}
                        type="button"
                        className="p-1"
                      >
                        <Image
                          src="/menu.svg"
                          alt="Menu"
                          width={24}
                          height={24}
                        />
                      </button>
                      
                      {selectedGoalId === 'goal2' && (
                        <div className="absolute right-0 top-full mt-1 bg-white rounded-3xl shadow-lg z-10 w-[140px] overflow-hidden">
                          <div className="flex items-center p-3 hover:bg-gray-50 cursor-pointer">
                            <Image
                              src="/health/edit.svg"
                              alt="Edit"
                              width={20}
                              height={20}
                              className="mr-2"
                            />
                            <span>Edit</span>
                          </div>
                          <div 
                            className="flex items-center p-3 hover:bg-gray-50 cursor-pointer border-t"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent event bubbling
                              handleDeleteClick('goal2');
                            }}
                          >
                            <Image
                              src="/health/trash.svg"
                              alt="Delete"
                              width={20}
                              height={20}
                              className="mr-2"
                            />
                            <span>Delete</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-gray-500 mb-3">March 14, 2025 - April 15, 2025</p>
                  
                  <div>
                    <p className="text-sm text-[#4285F4]">Target</p>
                    <p className="font-medium">8 hours of sleep <span className="text-gray-400">per day</span></p>
                  </div>
                </div>
                
                {/* Add More Button */}
                <button 
                  onClick={() => setShowAddGoalModal(true)}
                  className="flex items-center justify-center py-2 px-4 bg-white rounded-full shadow-md mt-4"
                  type="button"
                >
                  <Image
                    src="/plus.svg"
                    alt="Add More"
                    width={20}
                    height={20}
                    className="mr-2"
                  />
                  <span className="font-medium">Add More</span>
                </button>
              </motion.div>
              
              {/* Recommended Section */}
              <motion.div
                variants={itemVariants}
                className="mt-8"
              >
                <div className="flex items-center mb-4">
                  <div className="text-[#27A8E4] mr-2">
                    <Image
                      src="/health/sparkles.svg" 
                      alt="Recommended"
                      width={24}
                      height={24}
                      className="w-6 h-6"
                    />
                  </div>
                  <h2 className="text-[#27A8E4] text-lg font-medium">Recommended</h2>
                </div>

                {/* Recommended Goal Card */}
                <div className="bg-[#EDF2FD] rounded-xl p-4 mb-4 h-[199px]">
                  <div className="flex items-center mb-2">
                    <Image
                      src="/health/sparkles.svg"
                      alt="Sparkles"
                      width={20}
                      height={20}
                      className="mr-2"
                    />
                    <h3 className="font-medium text-lg">Get Better Sleep</h3>
                  </div>
                  <p className="text-sm text-gray-500 mb-3">March 14, 2025 - April 15, 2025</p>
                  
                  <div className="mb-4">
                    <p className="text-sm text-[#4285F4]">Target</p>
                    <p className="font-medium">8 hours of sleep <span className="text-gray-400">per day</span></p>
                  </div>
                  
                  <CustomButton
                    onClick={() => setShowAddGoalModal(true)}
                  >
                    Add Goal
                  </CustomButton>
                </div>
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      </AnimatePresence>

      {/* Modal for adding a new goal */}
      {showAddGoalModal && (
        <AddGoalModal onClose={() => setShowAddGoalModal(false)} />
      )}

      {/* Delete confirmation modal */}
      {showDeleteModal && (
        <DeleteConfirmationModal 
          onConfirm={handleConfirmDelete}
          onCancel={() => setShowDeleteModal(false)}
        />
      )}
    </>
  );
};

export default GoalSettingModal;
