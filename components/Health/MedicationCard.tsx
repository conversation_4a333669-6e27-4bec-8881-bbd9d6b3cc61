import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";

type MedicationCardProps = {
  medicationId: string;
  name: string;
  dateRange: string;
  dosage: string;
  dosageUnit: string;
  frequency: string;
  frequencyUnit: string;
  route: string;
  administrator: string;
  physician: string;
  selectedMedicationId: string | null;
  onOpenMenu: (medicationId: string) => void;
  onDeleteClick: (medicationId: string) => void;
  menuVariants: any;
};

export default function MedicationCard({
  medicationId,
  name,
  dateRange,
  dosage,
  dosageUnit,
  frequency,
  frequencyUnit,
  route,
  administrator,
  physician,
  selectedMedicationId,
  onOpenMenu,
  onDeleteClick,
  menuVariants,
}: MedicationCardProps) {
  return (
    <div className="bg-gray-100 rounded-xl p-4 mb-4 h-[243px]">
      <div className="flex justify-between h-[19px] mb-1">
        <h3 className="font-medium text-[16px]">{name}</h3>
        <div className="relative">
          <button onClick={() => onOpenMenu(medicationId)}>
            <div className="flex flex-col items-center justify-center w-7 h-7">
              <div className="w-1.5 h-1.5 rounded-full bg-gray-700 mb-0.5"></div>
              <div className="w-1.5 h-1.5 rounded-full bg-gray-700 mb-0.5"></div>
              <div className="w-1.5 h-1.5 rounded-full bg-gray-700"></div>
            </div>
          </button>
          <AnimatePresence mode="wait">
            {selectedMedicationId === medicationId && (
              <motion.div
                className="absolute right-0 mt-2 w-40 bg-white rounded-3xl shadow-lg z-10"
                variants={menuVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
              >
                <div className="p-4 flex items-center cursor-pointer hover:bg-gray-50">
                  <Image
                    src="/health/edit.svg"
                    alt="Edit"
                    width={20}
                    height={20}
                    className="mr-4"
                  />
                  <span className="text-lg">Edit</span>
                </div>
                <div
                  className="p-4 flex items-center border-t cursor-pointer hover:bg-gray-50"
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteClick(medicationId);
                  }}
                >
                  <Image
                    src="/health/trash.svg"
                    alt="Delete"
                    width={20}
                    height={20}
                    className="mr-4"
                  />
                  <span className="text-lg">Delete</span>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
      <p className="text-sm text-gray-500 mb-3">{dateRange}</p>
      <div className="grid grid-cols-2 gap-3 mb-3">
        <div>
          <p className="text-sm text-[#EC4899]">Dosage</p>
          <p className="font-medium text-[20px] h-[25px]">
            {dosage} <span className=" text-sm text-gray-500">{dosageUnit}</span>
          </p>
        </div>
        <div>
          <p className="text-sm text-[#6466F1]">Frequency</p>
          <p className="font-medium text-[20px] h-[25px]">
            {frequency}
            <span className="text-sm text-gray-500">{frequencyUnit}</span>
          </p>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-3 mb-3">
        <div>
          <p className="text-sm text-[#27A8E4]">Route</p>
          <p className="font-medium text-[20px] h-[25px]">{route}</p>
        </div>
        <div>
          <p className="text-sm text-[#19C5BB]">Administrator</p>
          <p className="font-medium text-[20px] h-[25px]">{administrator}</p>
        </div>
      </div>
      <div>
        <p className="text-sm text-[#FF6D85]">Prescribing Physician</p>
        <p className="font-medium text-[20px] h-[25px]">{physician}</p>
      </div>
    </div>
  );
}
