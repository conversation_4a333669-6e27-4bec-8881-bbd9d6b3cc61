import { useState, useEffect, useMemo, useRef, useCallback } from "react";
import { toast } from "sonner";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { useUser } from "@/hooks/useUsers";
import { debounce, throttle } from "lodash";
import { Skeleton } from "@/components/ui/skeleton";
import HealthSystemItem from "./HealthSystemItem";

const client = generateClient<Schema>();

interface HealthSystem {
  id: string;
  name: string;
  logo?: string;
  ehr: string;
}

interface Props {
  searchQuery: string;
  setSearchQuery: (q: string) => void;
}

const DEFAULT_HEALTH_SYSTEMS: HealthSystem[] = [
  { id: "8364847", name: "Epic Systems", ehr: "" },
  { id: "4728", name: "Cerner", ehr: "" },
  { id: "8364849", name: "Allscripts", ehr: "" },
];

const HealthSystemAvailableList = ({ searchQuery, setSearchQuery }: Props) => {
  const { currentUser } = useUser();
  const [availableSystems, setAvailableSystems] = useState<HealthSystem[]>([]);
  const [isLoadingSystems, setIsLoadingSystems] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [connectingSystem, setConnectingSystem] = useState<string | null>(null);
  const [offset, setOffset] = useState<number>(0);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const observerRef = useRef<HTMLDivElement>(null);
  const lastQueryRef = useRef<string>("");

  const fetchAvailableSystems = async (
    query: string,
    offset: number = 0,
    isLoadMore = false
  ) => {
    if (isLoadMore && !hasMoreData) return;

    if (!currentUser?.userId) {
      setError("User ID is required to fetch health systems");
      toast.error("User ID is required");
      return;
    }

    if (isLoadMore) {
      setIsLoadingMore(true);
    } else {
      setIsLoadingSystems(true);
      setError(null);
    }

    try {
      const response = await client.mutations.searchMedicalSystems({
        query: query.trim() || "",
        offset,
        system_type: "Payer Patient Access",
      });

      const mappedSystems: HealthSystem[] = (response.data || [])
        .filter(
          (item): item is NonNullable<typeof item> =>
            item !== null && item !== undefined
        )
        .map((item) => ({
          id: String(item.id ?? 0),
          name: item.name ?? "",
          logo: item.logo ?? "",
          ehr: item.ehr ?? "",
        }));

      if (isLoadMore) {
        setAvailableSystems((prev) => [...prev, ...mappedSystems]);
      } else {
        setAvailableSystems(mappedSystems);
      }

      setHasMoreData(mappedSystems.length > 0);
      lastQueryRef.current = query;
    } catch (error: any) {
      console.error("Error fetching health systems:", error);
      const errorMessage =
        error.message === "Query parameter is required"
          ? "Please enter a valid search term"
          : "Failed to fetch health systems";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      if (isLoadMore) {
        setIsLoadingMore(false);
      } else {
        setIsLoadingSystems(false);
      }
    }
  };

  const loadMoreData = useCallback(
    throttle(
      () => {
        if (!isLoadingMore && !isLoadingSystems && hasMoreData) {
          setOffset((prev) => prev + 20);
          fetchAvailableSystems(searchQuery, offset + 20, true);
        }
      },
      1000,
      { leading: true, trailing: false }
    ),
    [isLoadingMore, isLoadingSystems, hasMoreData, searchQuery, offset]
  );

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !isLoadingMore && hasMoreData) {
          loadMoreData();
        }
      },
      {
        threshold: 0.1,
        rootMargin: "200px",
      }
    );

    const currentObserver = observerRef.current;
    if (currentObserver) {
      observer.observe(currentObserver);
    }

    return () => {
      if (currentObserver) {
        observer.unobserve(currentObserver);
      }
      loadMoreData.cancel();
    };
  }, [loadMoreData]);

  const handleConnectSystem = async (systemId: string) => {
    if (!currentUser?.userId) {
      toast.error("User ID is required to connect to a health provider");
      return;
    }

    setConnectingSystem(systemId);

    try {
      const authUrlResponse = await client.mutations.getOneUpHealthAuthUrl({
        systemId: String(systemId),
      });

      if (
        authUrlResponse?.data?.success &&
        authUrlResponse?.data?.data?.authorization_url
      ) {
        window.location.href = authUrlResponse.data.data.authorization_url;
      } else {
        const errorMessage =
          authUrlResponse?.data?.error ||
          "Failed to get authorization URL. Please try again.";
        toast.error(errorMessage);
        console.error("Authorization URL error:", authUrlResponse);
      }
    } catch (error) {
      console.error("Error connecting to provider:", error);
      let errorMessage = "Connection failed. Please try again.";
      if (error instanceof Error) {
        errorMessage = `Connection failed: ${error.message}`;
      }
      toast.error(errorMessage);
    } finally {
      setConnectingSystem(null);
    }
  };

  const debouncedFetchSystems = useMemo(
    () =>
      debounce((query: string) => {
        const trimmedQuery = query.trim();
        if (trimmedQuery !== "") {
          if (trimmedQuery !== lastQueryRef.current) {
            setOffset(0);
            setHasMoreData(true);
            setAvailableSystems([]);
            fetchAvailableSystems(trimmedQuery);
          }
        } else {
          setAvailableSystems([]);
          setOffset(0);
          setHasMoreData(false);
        }
      }, 300),
    []
  );

  useEffect(() => {
    debouncedFetchSystems(searchQuery.trim());
    return () => {
      debouncedFetchSystems.cancel();
    };
  }, [searchQuery, debouncedFetchSystems]);

  useEffect(() => {
    lastQueryRef.current = "";
    setAvailableSystems([]);
    setOffset(0);
    setHasMoreData(true);
    setError(null);
  }, [currentUser?.userId]);

  const renderSkeleton = (count: number = 5) => (
    <div className="space-y-3">
      {[...Array(count)].map((_, index) => (
        <div
          key={`skeleton-${index}`}
          className="flex flex-row sm:justify-between sm:items-center bg-gray-50 rounded-lg p-4 min-h-[65px] gap-3"
        >
          <div className="flex-1 flex items-center gap-3">
            <Skeleton className="h-8 w-8 rounded-full" />
            <div className="flex-1 space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          </div>
          <Skeleton className="h-8 w-24 rounded-full" />
        </div>
      ))}
    </div>
  );

  return (
    <div className="flex-1 md:px-6 py-4 md:py-6 overflow-y-auto">
      <h3 className="text-lg font-medium mb-4">Add Health System</h3>

      <div className="relative mb-6">
        <input
          type="text"
          placeholder="Search health systems"
          className="w-full bg-gray-100 h-[56px] border border-gray-300 rounded-2xl pl-10 pr-4 py-2"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <div className="absolute inset-y-0 left-3 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <circle cx="11" cy="11" r="8" />
            <line x1="21" y1="21" x2="16.65" y2="16.65" />
          </svg>
        </div>
      </div>

      {searchQuery.trim() === "" ? (
        <div className="space-y-3">
          {DEFAULT_HEALTH_SYSTEMS.map((system) => (
            <HealthSystemItem
              key={system.id}
              system={system}
              connectingSystem={connectingSystem}
              handleConnectSystem={handleConnectSystem}
            />
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-8 text-red-500">{error}</div>
      ) : isLoadingSystems ? (
        renderSkeleton()
      ) : availableSystems.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No health systems found for "{searchQuery}"</p>
        </div>
      ) : (
        <div className="space-y-3">
          {availableSystems.map((system) => (
            <HealthSystemItem
              key={system.id}
              system={system}
              connectingSystem={connectingSystem}
              handleConnectSystem={handleConnectSystem}
            />
          ))}
          {isLoadingMore && renderSkeleton(5)}
          {hasMoreData && (
            <div
              ref={observerRef}
              className="h-4 w-full"
              style={{ minHeight: "1px" }}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default HealthSystemAvailableList;
