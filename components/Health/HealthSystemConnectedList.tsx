import { useState, useEffect } from "react";
import Image from "next/image";
import { toast } from "sonner";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { useUser } from "@/hooks/useUsers";

const client = generateClient<Schema>();

interface ConnectedSystem {
  id: string;
  name: string;
  logo?: string | null;
  ehr: string | null;
}

interface Props {
  setSearchQuery: (q: string) => void;
  onAddMoreClick?: () => void;
}

const HealthSystemConnectedList = ({
  setSearchQuery,
  onAddMoreClick,
}: Props) => {
  const { currentUser } = useUser();
  const [connectedSystems, setConnectedSystems] = useState<ConnectedSystem[]>(
    []
  );
  const [isLoading, setIsLoading] = useState(false);
  const [disconnectingSystemId, setDisconnectingSystemId] = useState<
    string | null
  >(null);

  // Fetch connected systems for the current user
  const fetchConnectedSystems = async () => {
    if (!currentUser?.userId) return;

    setIsLoading(true);
    try {
      const response = await client.models.UserConnectedSystem.list({
        filter: { userId: { eq: currentUser.userId } },
        selectionSet: [
          "systemId",
          "system.id",
          "system.name",
          "system.logo",
          "system.ehr",
        ],
      });

      if (response.data) {
        const connectedSystemsData = response.data.map((ucs) => ({
          id: ucs.systemId,
          name: ucs.system?.name ?? "Unknown System",
          logo: ucs.system?.logo ?? null,
          ehr: ucs.system?.ehr ?? null,
        }));
        setConnectedSystems(connectedSystemsData);
      }
    } catch (error) {
      console.error("Error fetching connected systems:", error);
      toast.error("Failed to load your connected health systems");
    } finally {
      setIsLoading(false);
    }
  };

  // Disconnect from a health system
  const handleDisconnectSystem = async (systemId: string) => {
    if (!currentUser?.userId) {
      toast.error("User ID is required to disconnect from a health system");
      return;
    }

    setDisconnectingSystemId(systemId);
    try {
      await client.models.UserConnectedSystem.delete({
        userId: currentUser.userId,
        systemId,
      });

      setConnectedSystems((prev) =>
        prev.filter((system) => system.id !== systemId)
      );
    } catch (error) {
      console.error("Error disconnecting from system:", error);
      toast.error("Failed to disconnect from health system");
    } finally {
      setDisconnectingSystemId(null);
    }
  };

  // Initial load of connected systems
  useEffect(() => {
    fetchConnectedSystems();
  }, [currentUser?.userId]);

  return (
    <div className="w-full md:w-[360px] md:border-r overflow-y-auto md:p-6 flex flex-col relative">
      <div className="flex items-center mb-3">
        <div className="text-white p-1 rounded flex items-center justify-center w-8 h-8">
          <Image
            src="/health/keyboard.svg"
            alt="Health System"
            width={24}
            height={24}
          />
        </div>
        <span className="text-blue-500 font-medium">Health System</span>
        <button
          className="ml-auto bg-blue-500 text-white text-xs px-4 py-1.5 rounded-full"
          onClick={onAddMoreClick ?? (() => setSearchQuery(""))}
        >
          Add More
        </button>
      </div>

      <p className="text-gray-400 text-xs mb-6">
        Select healthcare providers to connect to your health records
      </p>

      {/* Connected systems list */}
      <div className="space-y-3 mb-4">
        {isLoading ? (
          <div className="text-center py-4 text-gray-500">
            <p>Loading your connected systems...</p>
          </div>
        ) : connectedSystems.length > 0 ? (
          connectedSystems.map((system) => (
            <div key={system.id} className="bg-white shadow-sm rounded-lg p-3">
              <div className="flex items-center gap-2">
                {system.logo && (
                  <Image
                    src={system.logo}
                    alt={system.name}
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                )}
                <div className="font-medium">{system.name}</div>
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="text-green-500 text-xs font-medium">
                  Connected
                </span>
                <button
                  className="text-red-500 text-xs font-medium disabled:opacity-50"
                  onClick={() => handleDisconnectSystem(system.id)}
                  disabled={disconnectingSystemId === system.id}
                >
                  {disconnectingSystemId === system.id
                    ? "Disconnecting..."
                    : "Disconnect"}
                </button>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-4 text-gray-500">
            <p>No connected health systems</p>
            <p className="text-sm">
              Connect to a health system to see your medical records
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default HealthSystemConnectedList;
