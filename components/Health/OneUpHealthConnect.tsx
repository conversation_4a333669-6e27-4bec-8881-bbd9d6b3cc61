"use client";

import { useState, useEffect } from "react";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { Button } from "../ui/button";
import { useUser } from "@/hooks/useUsers";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card";
import { toast } from "sonner";

const client = generateClient<Schema>();

interface ConnectionProps {
  userId: string;
}

const PROVIDERS = [
  { id: "epic", name: "Epic", systemId: "8364847" },
  { id: "cerner", name: "<PERSON><PERSON>", systemId: "8364848" },
  { id: "allscripts", name: "Allscripts", systemId: "8364849" },
  { id: "athena", name: "Athenahealth", systemId: "8364850" },
];

export default function OneUpHealthConnect({ userId }: ConnectionProps) {
  const [loading, setLoading] = useState(false);

  const { currentUser } = useUser();

  const connectToProvider = async (provider: (typeof PROVIDERS)[0]) => {
    try {
      setLoading(true);

      const authUrlResponse: any = await client.mutations.getOneUpHealthAuthUrl(
        {
          systemId: provider.systemId,
        }
      );

      // Check for success and access the nested data structure
      if (
        authUrlResponse.data?.success &&
        authUrlResponse.data?.data?.authorization_url
      ) {
        window.location.href = authUrlResponse.data.data.authorization_url;
      } else {
        const errorMessage =
          authUrlResponse.data?.error || "Failed to get authorization URL";
        toast.error(errorMessage);
        console.error("Authorization URL error:", errorMessage);
      }
    } catch (error) {
      console.error("Connection failed:", error);
      toast.error("Connection failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Connect to Your Health Records</CardTitle>
          <CardDescription>
            Connect to your healthcare providers to authorize data access
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {PROVIDERS.map((provider) => (
              <div
                key={provider.id}
                className={`p-4 border rounded-lg flex items-center justify-between ${
                  currentUser?.is_1healthup_connected
                    ? "border-green-500 bg-green-50"
                    : "border-gray-200"
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 flex items-center justify-center bg-gray-100 rounded-md">
                    <span className="text-xs font-medium">
                      {provider.name.slice(0, 2)}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium">{provider.name}</p>
                    <p className="text-xs text-gray-500">
                      {currentUser?.is_1healthup_connected
                        ? "Connected"
                        : "Not connected"}
                    </p>
                  </div>
                </div>
                <Button
                  variant={
                    currentUser?.is_1healthup_connected ? "outline" : "default"
                  }
                  size="sm"
                  onClick={() => connectToProvider(provider)}
                  disabled={loading}
                >
                  {loading ? "Connecting..." : "Connect"}
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
