interface MomentContentProps {
  text: string;
  content?: string;
  image?: string | null;
  imageGrid?: string[];
}

export default function MomentContent({ text, content, image, imageGrid }: MomentContentProps) {
  // Instead of using state for error handling, let's use a simpler approach
  // that won't interfere with rendering
  
  return (
    <>
      <div className="">
        <h3 className="text-[#2567FF] font-semibold text-[16px] tracking-normal w-full overflow-hidden leading-tight break-all hyphens-auto">
          {text}
        </h3>
      </div>

      {content && (
        <div className="mt-1">
          <p className="text-gray-800 text-[14px]">{content}</p>
        </div>
      )}

      {image && (
        <div className="w-full mt-2 h-[200px]">
          {image.startsWith('data:') ? (
            <img
              src={image}
              alt="Post image"
              className="w-full object-cover rounded-lg h-full"
            />
          ) : (
            <img
              src={image}
              alt="Post image"
              className="w-full object-cover rounded-lg h-full"
            />
          )}
        </div>
      )}

      {imageGrid && (
        <div className="grid grid-cols-3 gap-1 mt-2">
          {imageGrid.map((img, index) => (
            <div key={index} className="aspect-square">
              <img
                src={img}
                alt={`Grid image ${index}`}
                className="w-full h-full object-cover rounded-lg"
              />
            </div>
          ))}
        </div>
      )}
    </>
  )
}
