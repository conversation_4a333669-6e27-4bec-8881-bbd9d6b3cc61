"use client";

import RecommendedGroup from "./RecommendedGroup";

type Community = {
  id: string;
  name: string;
  description: string;
  members: number;
  avatar: string;
};

type CommunitySectionProps = {
  compact?: boolean;
  communities: Community[];
  isLoading: boolean;
  onRefresh: () => void;
};

export default function CommunitySection({ 
  compact = false, 
  communities, 
  isLoading, 
  onRefresh 
}: CommunitySectionProps) {
  
  if (compact) {
    return (
      <>
        {isLoading ? (
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map(i => (
              <div key={i} className="h-16 bg-gray-200 animate-pulse rounded-lg"></div>
            ))}
          </div>
        ) : communities.length > 0 ? (
          <div className="space-y-4">
            {communities.slice(0, 5).map((community) => (
              <RecommendedGroup 
                key={community.id} 
                group={{
                  name: community.name,
                  description: community.description,
                  members: community.members,
                  avatar: community.avatar,
                  id: community.id
                }} 
                onJoin={onRefresh}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-4 text-gray-500">
            <p>No communities found</p>
          </div>
        )}
      </>
    );
  }
  
  return (
    <div className="">
      {isLoading ? (
        <div className="space-y-4">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <div key={i} className="h-20 bg-gray-200 animate-pulse rounded-lg"></div>
          ))}
        </div>
      ) : communities.length > 0 ? (
        <div className="space-y-4">
          {communities.map((community) => (
            <RecommendedGroup 
              key={community.id} 
              group={{
                name: community.name,
                description: community.description,
                members: community.members,
                avatar: community.avatar,
                id: community.id
              }} 
              onJoin={onRefresh}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <p>No communities found. Create your first community!</p>
        </div>
      )}
    </div>
  );
}
