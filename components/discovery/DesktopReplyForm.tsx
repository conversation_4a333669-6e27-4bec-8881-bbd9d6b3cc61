import { useState } from "react"
import { fetchAuthSession } from "aws-amplify/auth"
import { commentService } from "@/services/commentService"
import { GroupedComment } from "@/types/comment"
import { useUser } from "@/hooks/useUser"

interface DesktopReplyFormProps {
  parentComment: GroupedComment
  onClose: () => void
  onRefresh?: () => void
}

export function DesktopReplyForm({ 
  parentComment, 
  onClose, 
  onRefresh 
}: DesktopReplyFormProps) {
  const [content, setContent] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { currentUser } = useUser()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!content.trim() || isSubmitting) return

    setIsSubmitting(true)

    try {
      const session = await fetchAuthSession()
      const email = session.tokens?.idToken?.payload?.email
      
      if (!email) {
        console.error("User not authenticated")
        return
      }

      
      await commentService.createReply({
        content: content.trim(),
        userId: String(email),
        postId: parentComment.postId,
        parentCommentId: parentComment.id,
        likes: 0
      })

      // Clear form and close
      setContent("")
      onClose()
      
      // Call onRefresh to update the comments list
      if (onRefresh) {
        onRefresh()
      }
    } catch (error) {
      console.error("Error submitting reply:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="mt-3 pl-8">
      <div className="flex flex-col">
        <textarea
          className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-300"
          rows={2}
          placeholder="Write a reply..."
          value={content}
          onChange={(e) => setContent(e.target.value)}
        ></textarea>
        <div className="flex justify-end gap-2 mt-2">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-gray-600 rounded-lg"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!content.trim() || isSubmitting}
            className={`px-4 py-2 bg-blue-500 text-white rounded-lg ${
              !content.trim() || isSubmitting ? "opacity-50" : ""
            }`}
          >
            {isSubmitting ? "Submitting..." : "Reply"}
          </button>
        </div>
      </div>
    </form>
  )
}
