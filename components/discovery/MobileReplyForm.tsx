import { useState } from "react"
import { fetchAuthSession } from "aws-amplify/auth"
import { commentService } from "@/services/commentService"
import { GroupedComment } from "@/types/comment"
import Image from "next/image"

interface MobileReplyFormProps {
  parentComment: GroupedComment
  onClose: () => void
  onRefresh?: () => void
}

export function MobileReplyForm({ parentComment, onClose, onRefresh }: MobileReplyFormProps) {
  const [content, setContent] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!content.trim() || isSubmitting) return

    setIsSubmitting(true)

    try {
      const session = await fetchAuthSession()
      const email = session.tokens?.idToken?.payload?.email
      
      if (!email) {
        console.error("User not authenticated")
        return
      }

      await commentService.createReply({
        content: content.trim(),
        userId: String(email),
        postId: parentComment.postId,
        parentCommentId: parentComment.id,
        likes: 0
      })

      setContent("")
      onClose()
      
      if (onRefresh) {
        onRefresh()
      }
    } catch (error) {
      console.error("Error submitting reply:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Reply to this post"
        className="w-full p-3 mb-8 rounded-lg text-gray-700 outline-none"
        value={content}
        onChange={(e) => setContent(e.target.value)}
        autoFocus
      />

      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button type="button" className="text-gray-500">
            <Image src="/Utility.svg" alt="Utility" width={24} height={24} />
          </button>
          <button type="button" className="text-gray-500">
            <Image src="/video.svg" alt="Video" width={24} height={24} />
          </button>
        </div>
        <button 
          type="submit" 
          className="bg-blue-500 text-white  h-[36px] w-[71px] rounded-full text-base font-medium"
        >
          Reply
        </button>
      </div>
    </form>
  )
}
