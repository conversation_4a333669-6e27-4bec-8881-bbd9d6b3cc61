"use client";

import Image from "next/image";
import Link from "next/link";

interface MomentHeaderProps {
  id: string;
  userName: string;
  avatar: string;
  timeAgo: string;
}

export default function MomentHeader({
  id,
  userName,
  avatar,
  timeAgo,
}: MomentHeaderProps) {
  return (
    <div className="pb-3 flex items-center">
      <div className="w-8 h-8 rounded-full overflow-hidden flex items-center justify-center">
        <Image
          src={"/Avatar.png"}
          alt={userName}
          width={32}
          height={32}
          className="object-cover"
        />
      </div>
      <div className="ml-2 flex-1">
        <div className="flex justify-between items-center">
          <Link href={`/group/${id}`}>
            <h3 className="text-black font-medium hover:text-blue-500 cursor-pointer">
              {userName}
            </h3>
          </Link>
          <span className="text-gray-500 font-normal text-sm">{timeAgo}</span>
        </div>
      </div>
    </div>
  );
}
