"use client"

import { useState, useCallback } from "react"
import { Comment, GroupedComment } from "@/types/comment"
import { CommentItem } from "./CommentItem"
import { MobileReplyForm } from "./MobileReplyForm"

interface CommentsProps {
  comments: Comment[] | undefined | null
  level?: number
  onRefresh?: () => void
  onMobileReply?: () => void
  onMobileReplyClose?: () => void
}

export default function Comments({ comments = [], level = 0, onRefresh, onMobileReply, onMobileReplyClose }: CommentsProps) {
  const [activeReplyComment, setActiveReplyComment] = useState<GroupedComment | null>(null);
  const safeComments = comments ?? [];
  
  // Group comments logic
  const allComments = safeComments.reduce((acc, comment) => {
    acc[comment.id] = { ...comment, replies: [] };
    return acc;
  }, {} as Record<string, GroupedComment>);

  const groupedComments = safeComments.reduce((acc, comment) => {
    if (!comment.parentCommentId) {
      acc[comment.id] = allComments[comment.id];
    } else {
      const parentComment = allComments[comment.parentCommentId];
      if (parentComment) {
        parentComment.replies.push(allComments[comment.id]);
      }
    }
    return acc;
  }, {} as Record<string, GroupedComment>);

  const handleMobileReply = useCallback((comment: GroupedComment) => {
    setActiveReplyComment(comment);
    onMobileReply?.();
  }, [onMobileReply]);

  const handleCloseReply = () => {
    setActiveReplyComment(null);
    onMobileReplyClose?.();
  };

  return (
    <div className={`${level === 0 ? "" : ""}`}>
      {Object.values(groupedComments).map((comment) => (
        <CommentItem 
          key={comment.id} 
          comment={comment} 
          level={level} 
          onRefresh={onRefresh}
          onMobileReply={handleMobileReply}
          isReplying={activeReplyComment?.id === comment.id}
        />
      ))}

      {activeReplyComment && (
        <div className="fixed inset-x-0 bottom-0 z-50 bg-white border border-gray-100 shadow-lg md:hidden rounded-t-3xl">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm text-gray-500">
                Replying to {activeReplyComment.user?.name || activeReplyComment.userId}
              </span>
              <button onClick={handleCloseReply} className="text-gray-500">✕</button>
            </div>

            <MobileReplyForm 
              parentComment={activeReplyComment}
              onClose={handleCloseReply}
              onRefresh={onRefresh}
            />
          </div>
        </div>
      )}
    </div>
  );
}