"use client"

import { Heart, MessageCircle } from "lucide-react"
import { motion } from 'framer-motion'


interface MomentActionsProps {
  likesCount: number;
  commentsCount: number;
  isLiked: boolean;
  isBookmarked: boolean;
  onLikeClick: () => void;
  onLikesShow: () => void;
  onCommentClick: () => void;
  onBookmarkClick: () => void;
}

export default function MomentActions({
  likesCount,
  commentsCount,
  isLiked,
  isBookmarked,
  onLikeClick,
  onLikesShow,
  onCommentClick,
  onBookmarkClick
}: MomentActionsProps) {
  return (
    <div className="pt-3 flex items-center justify-between">
      <div className="flex items-center space-x-3">
        <button 
          className="flex items-center text-black"
          onClick={onLikeClick}
        >
          <motion.div
            whileTap={{ scale: 0.8 }}
            animate={{ 
              scale: isLiked ? [1, 1.2, 1] : 1,
              transition: { duration: 0.3 }
            }}
          >
            <Heart 
              className={`w-5 h-5 mr-1 ${isLiked ? "fill-current text-blue-500" : ""}`} 
            />
          </motion.div>
          <span 
            className="cursor-pointer hover:underline" 
            onClick={(e) => {
              e.stopPropagation();
              onLikesShow();
            }}
          >
            {likesCount}
          </span>
        </button>
        <button
          className="flex items-center text-black"
          onClick={onCommentClick}
        >
          <MessageCircle className="w-5 h-5 mr-1" />
          <span>{commentsCount}</span>
        </button>
      </div>
      
    </div>
  )
}
