import { FormData } from "@/types/auth";
import { UseFormRegister, FieldErrors, UseFormHandleSubmit } from "react-hook-form";



type LoginFormProps = {
  handleSubmit: UseFormHandleSubmit<FormData>;
  handleLogin: (data: FormData) => void;
  register: UseFormRegister<FormData>;
  errors: FieldErrors<FormData>;
  loading: boolean;
  errorMessage: string;
};

export default function LoginForm({ handleSubmit, handleLogin, register, errors, loading, errorMessage }: LoginFormProps) {
  return (
    <form onSubmit={handleSubmit(handleLogin)}>
      <div className="mb-6">
        <input
          type="email"
          {...register("email", { required: "Email is required" })}
          placeholder="Enter your email address"
          className="w-full py-3 px-6 border border-gray-600 bg-gray-800 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
        />
        {errors.email && <p className="text-red-500">{errors.email.message}</p>}
      </div>

      <div className="mb-6">
        <input
          type="password"
          {...register("password", { required: "Password is required" })}
          placeholder="Enter your password"
          className="w-full py-3 px-6 border border-gray-600 bg-gray-800 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
        />
        {errors.password && <p className="text-red-500">{errors.password.message}</p>}
      </div>

      {errorMessage && <p className="text-red-500 mb-4">{errorMessage}</p>}

      <button
        type="submit"
        disabled={loading}
        className="w-full py-3 px-6 bg-green-600 text-white rounded-lg shadow-lg hover:bg-green-700 transition-all"
      >
        {loading ? "Logging in..." : "Login"}
      </button>
    </form>
  );
}
