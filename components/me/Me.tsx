"use client";

import { useState, useEffect } from "react";
import { fetchAuthSession } from "@aws-amplify/auth";
import { useRouter } from "next/navigation";
import { Header } from "./Header";
import Image from "next/image";
import { MenuItem } from "./MenuItem";
import { useUser } from "@/hooks/useUsers";

interface MeProps {
  setIsModalOpen?: (isOpen: boolean) => void;
  isMobilePage?: boolean;
}

export default function Me({ setIsModalOpen, isMobilePage = false }: MeProps) {
  const [currentUser, setCurrentUser] = useState<{
    email: string;
    userId?: string;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeSection, setActiveSection] = useState("profile");
  const [showComingSoonFor, setShowComingSoonFor] = useState<string | null>(
    null
  );
  const router = useRouter();
  const { logout } = useUser();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const session = await fetchAuthSession();

        if (session.tokens?.idToken?.payload?.email) {
          const email = String(session.tokens.idToken.payload.email);
          const userId = String(session.tokens.idToken.payload.sub);
          setCurrentUser({ email, userId });
        } else {
          console.error("No email found in session tokens");
          setCurrentUser({ email: "<EMAIL>" });
        }
      } catch (error) {
        console.error("Error fetching auth session:", error);
        setCurrentUser({ email: "<EMAIL>" });
      } finally {
        setIsLoading(false);
      }
    };

    fetchUser();
  }, []);

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  const handleBack = () => {
    if (isMobilePage) {
      router.back();
    } else {
      setIsModalOpen && setIsModalOpen(false);
    }
  };

  const handleSignOut = async () => {
    try {
      await logout(); // Use the logout function from the user context
      if (setIsModalOpen) {
        setIsModalOpen(false);
      }
      // No need to push to /login as the logout function already handles redirection
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  // Function to handle clicks on "coming soon" items
  const handleComingSoonClick = (itemLabel: string) => {
    setShowComingSoonFor(itemLabel);

    // Hide the message after 2 seconds
    setTimeout(() => {
      setShowComingSoonFor(null);
    }, 2000);
  };

  // Sidebar menu item component for desktop modal
  const SidebarMenuItem = ({
    icon,
    label,
    isActive,
    onClick,
    comingSoon,
  }: {
    icon: string;
    label: string;
    isActive?: boolean;
    onClick?: () => void;
    comingSoon?: boolean;
  }) => (
    <div
      className={`flex items-center p-4 cursor-pointer relative ${isActive ? "bg-gray-300 rounded-2xl font-medium" : "text-gray-700"}`}
      onClick={comingSoon ? () => handleComingSoonClick(label) : onClick}
    >
      <Image src={icon} alt={label} width={24} height={24} />
      <span className="ml-3">{label}</span>
      {comingSoon && showComingSoonFor === label && (
        <span className="ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full animate-pulse">
          Coming Soon
        </span>
      )}
    </div>
  );

  // If it's the mobile page, show the original design
  if (isMobilePage) {
    return (
      <div className="flex flex-col justify-between h-full bg-[#E9F0FF]">
        <div className="flex flex-col">
          <Header onBack={handleBack} />

          {/* Profile section with circular backgrounds */}
          <div className="flex flex-col items-center mb-[32px] mt-8 mx-5">
            <div className="w-[160px] h-[160px] relative flex items-center justify-center">
              {/* Circular background layers */}
              <div className="absolute w-full h-full rounded-full bg-[#2567FF57] opacity-20"></div>
              <div className="absolute w-[80%] h-[80%] rounded-full bg-[#2567FF24] opacity-30"></div>
              <div className="absolute w-[60%] h-[60%] rounded-full bg-[#2567FF14] opacity-40"></div>

              {/* Avatar image */}
              <div className="relative z-10 w-[100px] h-[100px] overflow-hidden">
                <Image
                  src="/famele.png"
                  alt={currentUser?.email || "User"}
                  width={100}
                  height={100}
                  className="rounded-full"
                />
              </div>
            </div>
            <h2 className="text-2xl font-bold mb-3 mt-4">
              {currentUser?.email || ""}
            </h2>
          </div>
        </div>

        {/* White card containing menu items */}
        <div className=" bg-white rounded-t-3xl p-5 box-border h-full max-h-[530px] overflow-auto">
          <div className="space-y-4 ">
            <div className="rounded-2xl border border-gray-200 hover:shadow-md transition-shadow duration-300 ease-in-out overflow-hidden">
              <MenuItem
                href="/web3-wallet"
                icon="/walet.svg"
                label="Web3 Wallet"
                iconColor="text-blue-500"
                comingSoon={true}
              />
            </div>

            <div className="rounded-2xl border border-gray-200 hover:shadow-md transition-shadow duration-300 ease-in-out overflow-hidden">
              <MenuItem
                href="/contacts"
                icon="/address-book (1).svg"
                label="Contacts"
                iconColor="text-orange-500"
              />
            </div>

            <div className="bg-white rounded-2xl border border-gray-200 flex flex-col overflow-hidden">
              <MenuItem
                href="/favorites"
                icon="/heartstar.svg"
                label="Favorites"
                iconColor="text-pink-500"
                comingSoon={true}
              />
              <div className="border border-gray-200" />
              <MenuItem
                href="/moments"
                icon="/rainbo.svg"
                label="Community"
                iconColor="text-teal-500"
                comingSoon={true}
              />
            </div>

            <div className="rounded-2xl border border-gray-200 hover:shadow-md transition-shadow duration-300 ease-in-out overflow-hidden">
              <MenuItem
                href="/settings"
                icon="/swtibg.svg"
                label="Settings"
                iconColor="text-blue-500"
                comingSoon={true}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  // For desktop modal, show the new sidebar design
  return (
    <div className="flex max-h-[90vh] h-full rounded-lg bg-[#E9F0FF]">
      {/* Sidebar */}
      <div className="w-[300px] bg-gray-100 h-full rounded-l-lg flex flex-col justify-between px-5 py-4">
        <div>
          <SidebarMenuItem 
            icon="/greenuser.svg" 
            label="Profile" 
            isActive={activeSection === "profile"} 
            onClick={() => setActiveSection("profile")}
          />
          <SidebarMenuItem
            icon="/walet.svg"
            label="Web3 Wallet"
            isActive={false}
            comingSoon={true}
          />
          <SidebarMenuItem
            icon="/address-book (1).svg"
            label="Contacts"
            isActive={false}
            comingSoon={true}
          />
          <SidebarMenuItem
            icon="/heartstar.svg"
            label="Favorites"
            isActive={false}
            comingSoon={true}
          />
          <SidebarMenuItem
            icon="/rainbo.svg"
            label="Community"
            isActive={false}
            onClick={() => {
              setIsModalOpen && setIsModalOpen(false);
              router.push('/discovery');
            }}
          />
          <SidebarMenuItem
            icon="/swtibg.svg"
            label="Settings"
            isActive={false}
            comingSoon={true}
          />
        </div>

        {/* Sign out button */}
        <div
          className="flex items-center text-red-500 cursor-pointer p-5"
          onClick={handleSignOut}
        >
          <Image src="/sign-out.svg" alt="Sign Out" width={24} height={24} />
          <span className="ml-3">Sign Out</span>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex flex-col bg-white rounded-r-lg">
        {/* Header */}
        <div className="flex justify-between items-center p-6 ">
          <h1 className="text-xl font-medium">Profile</h1>
          <button onClick={handleBack} className="text-gray-500">
            <Image src="/xrestuk.svg" alt="Back" width={24} height={24} />
          </button>
        </div>

        {/* Profile Content */}
        <div className="flex-1 px-5 overflow-auto">
          <p className="text-[14px] font-normal mb-[10px] text-[#929292]">Your Profile</p>
          
          {/* Profile section with circular backgrounds */}
          <div className="flex flex-row items-center    bg-gray-100 px-[20px] rounded-xl h-[152px] w-[460px]">
            <div className="relative flex items-center justify-center mr-4">
              {/* Circular background layers */}
              <div className="absolute w-[100px] h-[100px] rounded-full bg-[#2567FF57] opacity-20"></div>
              <div className="absolute w-[80px] h-[80px] rounded-full bg-[#2567FF24] opacity-30"></div>
              <div className="absolute w-[60px] h-[60px] rounded-full bg-[#2567FF14] opacity-40"></div>

              {/* Avatar image */}
              <div className="relative z-10 w-[80px] h-[80px] overflow-hidden">
                <Image
                  src="/famele.png"
                  alt={currentUser?.email || "User"}
                  width={80}
                  height={80}
                  className="rounded-full"
                />
              </div>
            </div>

            <div className="flex flex-col">
              <h2 className="text-2xl font-bold mb-1 truncate max-w-[300px]">{currentUser?.email || "User name"}</h2>
              <p className="text-gray-500">{currentUser?.userId || "12345"}</p>

            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
