import Image from "next/image";
import { ChevronRight } from "lucide-react";

interface ProfileCardProps {
  email: string;
  userId: string;
}

export function ProfileCard({ email, userId }: ProfileCardProps) {
  return (
    <div className="bg-white rounded-2xl mb-4 p-5 h-[96px] flex items-center">
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center">
          <div className="relative w-16 max-sm:w-12 h-16 max-sm:h-12 mr-4 max-sm:mr-3">
            <Image
              src="/Avatar.svg"
              alt="User avatar"
              width={64}
              height={64}
              className="rounded-full object-cover"
            />
          </div>
          <div className="flex flex-col">
            <div className="flex items-center flex-wrap mb-2 max-sm:mb-1">
              <h2 className="text-xl max-sm:text-lg font-semibold mr-2 max-sm:mr-1 truncate">
                {email || "User Name"}
              </h2>
              <span className="inline-block bg-green-500 text-white text-xs max-sm:text-[10px] font-medium px-2 max-sm:px-1.5 py-0.5 rounded-full">
                Verified
              </span>
             
            </div>
            <div className="flex space-x-2 max-sm:space-x-1">
              <h2 className="text-sm mr-2 max-sm:mr-1 truncate">
                {userId || "User ID"}
              </h2>
            </div>
          </div>
        </div>
        <div className="flex flex-col items-center">
          <Image
            src="/qr-code.svg"
            alt="Verified"
            width={27}
            height={24}
            className="mb-1"
          />
          <ChevronRight className="text-gray-400 w-7 max-sm:w-6 h-6 max-sm:h-6" />
        </div>
      </div>
    </div>
  );
}
