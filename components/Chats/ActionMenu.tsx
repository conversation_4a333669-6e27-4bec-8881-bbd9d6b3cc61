"use client";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Image from "next/image";
import { Button } from "../ui/button";
import { useRouter, usePathname } from "next/navigation";
import { useState, useCallback, useContext, useEffect } from "react";
import { useWindowSize } from "@/hooks/useWindowSize";
import AddFriendsModal from "@/components/AddFriendsModal";
import NewChatFullDialog from "@/components/NewChat/NewChatFullDialog";
import { ChatContext } from "@/components/Chat/context/ChatContext";
import { useViewState } from "@/components/Chat/context/ViewStateContext";

export function ChatActionsMenu() {
  const router = useRouter();
  const pathname = usePathname();
  const isDesktop = useWindowSize();
  const [showAddFriends, setShowAddFriends] = useState(false);
  const [showNewChatDialog, setShowNewChatDialog] = useState(false);
  const [open, setOpen] = useState(false);
  const { setShowNewChatPage } = useContext(ChatContext);
  const { setActiveView } = useViewState();

  // Stabilize the handlers with useCallback to prevent unnecessary re-renders
  const handleOpenNewChatDialog = useCallback(() => {
    // Close the dropdown first to prevent re-renders during state changes
    setOpen(false);

    if (isDesktop) {
      // On desktop, simply show the dialog
      setShowNewChatDialog(true);
    } else {
      // For mobile, navigate to a dedicated new chat route
      router.push("/chats/new");
      // Also set the view state for backward compatibility
      setActiveView("NEW_CHAT");
      setShowNewChatPage(true);
    }
  }, [isDesktop, router, setActiveView, setShowNewChatPage]);

  const handleCloseNewChatDialog = useCallback(() => {
    setShowNewChatDialog(false);
  }, []);

  const handleOpenAddFriends = useCallback(() => {
    if (isDesktop) {
      setShowAddFriends(true);
    } else {
      router.push("/contacts");
    }
    setOpen(false);
  }, [isDesktop, router]);

  const handleCloseAddFriends = useCallback(() => {
    setShowAddFriends(false);
  }, []);

  return (
    <>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size={"icon"}
            className="rounded-full border-none bg-transparent shadow-none"
          >
            <Image alt="Add" width={24} height={24} src="/chats/plus.svg" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56 p-2 rounded-[24px]">
          <DropdownMenuItem
            onClick={handleOpenNewChatDialog}
            className="py-3 cursor-pointer"
          >
            <Image
              src="/chats/message-plus.svg"
              alt="New Chat"
              width={24}
              height={24}
              className="mr-3"
            />
            <span className="text-base">New Chat</span>
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleOpenAddFriends}
            className="py-3 cursor-pointer"
          >
            <Image
              src="/chats/users-plus.svg"
              alt="Add Contact"
              width={24}
              height={24}
              className="mr-3"
            />
            <span className="text-base">Add Contact</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      {isDesktop && showAddFriends && (
        <AddFriendsModal onClose={handleCloseAddFriends} />
      )}
      <NewChatFullDialog
        isOpen={showNewChatDialog}
        onClose={handleCloseNewChatDialog}
      />
    </>
  );
}
