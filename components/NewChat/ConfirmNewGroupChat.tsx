"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2 } from "lucide-react";

interface Props {
  selectedCount?: number;
  onConfirm?: (params: { chatName?: string }) => void;
  onClose?: () => void;
  isLoading?: boolean;
}

const ConfirmNewGroupChat = ({
  selectedCount: propSelectedCount,
  onConfirm: propOnConfirm,
  onClose: propOnClose,
  isLoading: propIsLoading,
}: Props) => {
  const [groupName, setGroupName] = useState("");

  // Use props with fallbacks
  const selectedCount = propSelectedCount ?? 0;
  const isLoading = propIsLoading ?? false;
  const onConfirm =
    propOnConfirm ??
    (() => {
      console.log("No confirm handler available");
    });
  const onClose =
    propOnClose ??
    (() => {
      console.log("No close handler available");
    });

  // Debug: log the props to see what we're receiving
  console.log("ConfirmNewGroupChat - Props:", {
    selectedCount,
    onConfirm: !!propOnConfirm,
    onClose: !!propOnClose,
    isLoading,
  });

  // Handle group creation
  const handleCreateGroup = async () => {
    console.log("handleCreateGroup called");

    if (!onConfirm) {
      console.error("No onConfirm callback provided");
      return;
    }

    const finalGroupName = groupName.trim() || `Group Chat (${selectedCount})`;
    console.log("Creating group chat with name:", finalGroupName);

    try {
      await onConfirm({
        chatName: finalGroupName,
      });
      console.log("onConfirm completed successfully");
    } catch (error) {
      console.error("Error creating group chat:", error);
    }
  };

  return (
    <div className="flex flex-col h-full bg-[#E9F0FF]">
      {/* Header */}
      <div className="p-4">
        <Button
          variant="ghost"
          onClick={onClose}
          className="text-gray-600 hover:bg-gray-100 rounded-full"
        >
          Cancel
        </Button>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col items-start bg-white rounded-t-[32px] p-2">
        <div className="flex flex-col w-full gap-3 px-6 pt-3">
          {/* Input Field */}
          <div className="bg-[#F5F5F5] rounded-[24px] p-2 py-5">
            <div className="flex flex-col justify-stretch items-stretch gap-2.5 px-2 flex-1">
              <div className="flex items-center w-full">
                <Input
                  className="w-full border-none shadow-none bg-transparent font-figtree text-[16px] font-medium text-[#171717] placeholder:text-[#A3A3A3] focus:ring-0 focus:border-none p-0 h-auto leading-[19.2px]"
                  placeholder="Enter Group Name"
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                  disabled={isLoading}
                />
              </div>
            </div>
          </div>

          {/* Button */}
          <div className="flex items-stretch w-full gap-[30px] px-0 py-2">
            <Button
              className="w-full bg-[#5185FF] hover:bg-[#4175EF] text-white rounded-full font-figtree font-medium text-[16px] px-4 py-3 h-auto shadow-[0px_3px_6px_0px_rgba(66,74,83,0.12)] transition-all"
              onClick={handleCreateGroup}
              disabled={isLoading}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Creating Group...
                </div>
              ) : (
                "Create Group"
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmNewGroupChat;
