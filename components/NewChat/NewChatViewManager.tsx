"use client";

import { useContext } from "react";
import { useViewState } from "../Chat/context/ViewStateContext";
import NewChatHeader from "./NewChatHeader";
import ConfirmNewGroupChat from "./ConfirmNewGroupChat";

const NewChatViewManager = () => {
  const { activeView } = useViewState();

  // Log the active view for debugging
  console.log("NewChatViewManager - Current activeView:", activeView);

  // Show the appropriate component based on view state
  const renderContent = () => {
    switch (activeView) {
      case "CONFIRM_GROUP_CHAT":
        console.log("Rendering ConfirmNewGroupChat component");
        return <ConfirmNewGroupChat />;
      case "NEW_CHAT":
      default:
        console.log("Rendering NewChatHeader component");
        return <NewChatHeader />;
    }
  };

  return <div className="flex flex-col h-full">{renderContent()}</div>;
};

export default NewChatViewManager;
