import { useEffect } from "react";
import { Checkbox } from "../ui/checkbox";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";

interface Props {
  userId: string; // Changed from id to userId
  name: string;
  avatarUrl?: string;
  selected: boolean;
  onSelect: (userId: string) => void;
}

const NewChatListItem = ({
  userId,
  name,
  avatarUrl = "/Avatar.png",
  selected,
  onSelect,
}: Props) => {
  // Log when props change to debug checkbox behavior
  useEffect(() => {
    console.log(`Item ${userId} (${name}) selected state:`, selected);
  }, [userId, name, selected]);

  const handleCheckboxChange = () => {
    console.log(`Checkbox ${userId} clicked, current state:`, selected);
    onSelect(userId);
  };

  return (
    <div className="flex items-center gap-3 px-3 py-2  cursor-pointer">
      <Checkbox
        id={`checkbox-${userId}`}
        checked={selected}
        onCheckedChange={handleCheckboxChange}
        className="w-4 h-4 rounded-full  "
      />
      <Avatar className="w-10 h-10 ring-1 ring-gray-200">
        <AvatarImage src={avatarUrl} alt={name} />
        <AvatarFallback>{name.charAt(0)}</AvatarFallback>
      </Avatar>
      <div className="flex-1 text-sm font-medium text-gray-900">{name}</div>
    </div>
  );
};

export default NewChatListItem;
