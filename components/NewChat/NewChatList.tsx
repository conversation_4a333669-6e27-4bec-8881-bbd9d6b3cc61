"use client";
import { useEffect, useState, useContext, useCallback } from "react";
import { generateClient } from "aws-amplify/api";
import { Schema } from "@/amplify/data/resource";
import { useRouter } from "next/navigation";
import NewChatListSection from "./NewChatListSection";
import NewC<PERSON>Header from "./NewChatHeader";
import NewChatSearchInput from "./NewChatSearchInput";
import { useUser } from "@/hooks/useUsers";
import {
  ERROR_FAILED_CREATE_CHAT,
  ERROR_MISSING_USER_ID,
} from "@/constants/chat";
import { Chat } from "@/types/chat";
import { Button } from "@/components/ui/button";
import { Avatar } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { X } from "lucide-react";
import {
  ChatContext,
  NewChatData,
} from "@/components/Chat/context/ChatContext";

const client = generateClient<Schema>();

export const createGroupChat = async (
  userId: string | null,
  participantUserIds: string[],
  chatName: string,
  questionnaireId?: string,
  description?: string,
  chatType: "AI" | "DIRECT" | "GROUP" = "GROUP"
): Promise<Chat | null> => {
  if (!userId) {
    console.error(ERROR_MISSING_USER_ID);
    return null;
  }

  if (participantUserIds.length === 0) {
    console.error("Cannot create a group chat with no participants.");
    return null;
  }

  const finalUserIds = participantUserIds.includes(userId)
    ? participantUserIds
    : [...participantUserIds, userId];

  try {
    const { data: createdChat } = await client.models.Chat.create({
      name: chatName || `Group Chat (${finalUserIds.length})`,
      description: description || "",
      questionnaireId,
      chatType,
    });

    if (!createdChat || !createdChat.id) {
      throw new Error(ERROR_FAILED_CREATE_CHAT);
    }

    await Promise.all(
      finalUserIds.map((participantUserId) =>
        client.models.ChatParticipant.create({
          id: `${createdChat.id}:${participantUserId}`,
          chatId: createdChat.id,
          userId: participantUserId,
          joinedAt: new Date().toISOString(),
        })
      )
    );

    const newChat: Chat = {
      id: createdChat.id,
      name: createdChat.name,
      description: createdChat.description,
      questionnaireId: createdChat.questionnaireId,
      chatType: createdChat.chatType || chatType, // Add fallback to the provided chatType parameter
    };

    return newChat;
  } catch (error: unknown) {
    console.error(
      `Failed to create group chat: ${
        error instanceof Error ? error.message : ERROR_FAILED_CREATE_CHAT
      }`
    );
    return null;
  }
};

interface NewChatListProps {
  inDialog?: boolean;
  onClose?: () => void;
  onSelectionChange?: (
    selectedUserIds: string[],
    selectedUserDetails: Array<{
      userId: string;
      name: string;
      email?: string;
    }>
  ) => void;
  hideHeader?: boolean;
  showDetailsPanel?: boolean;
}

const NewChatList = ({
  inDialog = false,
  onClose,
  onSelectionChange,
  hideHeader = false,
}: NewChatListProps) => {
  const [questionnaires, setQuestionnaires] = useState<
    Array<Schema["Questionnaire"]["type"]>
  >([]);
  const [users, setUsers] = useState<Array<Schema["User"]["type"]>>([]);
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [selectedQuestionnaireId, setSelectedQuestionnaireId] = useState<
    string | null
  >(null);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [isCreatingChat, setIsCreatingChat] = useState(false);
  const router = useRouter();
  const { currentUser, loading: isLoadingUser, error: userError } = useUser();
  const userId = currentUser?.userId || null;
  const { setNewChatData } = useContext(ChatContext);

  // Memoize functions to avoid dependency issues
  const handleClose = useCallback(() => {
    if (inDialog && onClose) {
      onClose();
    } else {
      router.back();
    }
  }, [inDialog, onClose, router]);

  const handleConfirmCreateChat = useCallback(
    async (params?: { chatName?: string }) => {
      if (selectedUserIds.length === 0 && !selectedQuestionnaireId) {
        console.warn(
          "Cannot create chat: Select at least one user or a questionnaire."
        );
        return;
      }

      if (!userId) {
        console.error("Cannot create chat: Current user ID is missing.");
        return;
      }

      const isAIChat = !!selectedQuestionnaireId;
      let participantIds: string[];
      let chatType: "AI" | "DIRECT" | "GROUP";

      if (isAIChat) {
        participantIds = [userId];
        chatType = "AI";
      } else if (selectedUserIds.length === 1) {
        participantIds = selectedUserIds;
        chatType = "DIRECT";
      } else {
        participantIds = selectedUserIds;
        chatType = "GROUP";
      }

      setIsCreatingChat(true);

      try {
        const createdChat = await createGroupChat(
          userId,
          participantIds,
          params?.chatName || `Group Chat (${selectedUserIds.length})`,
          selectedQuestionnaireId || undefined,
          undefined,
          chatType
        );

        console.log("Created chat:", createdChat);

        if (createdChat?.id) {
          console.log(
            "Chat created successfully, navigating to:",
            createdChat.id
          );
          // Call onClose first if we're in a dialog
          if (inDialog && onClose) {
            onClose();
          }
          router.push(`/chats/${createdChat.id}`);
        } else {
          console.error("Failed to create chat.");
        }
      } catch (error: unknown) {
        console.error("Error in chat creation:", error);
      } finally {
        setIsCreatingChat(false);
      }
    },
    [
      userId,
      selectedUserIds,
      selectedQuestionnaireId,
      inDialog,
      onClose,
      router,
    ]
  );

  // Update the context with the current selection state
  useEffect(() => {
    // Only update if component is mounted and context is available
    let isComponentMounted = true;

    if (setNewChatData && isComponentMounted) {
      // Use a stable reference to the chat data to prevent unnecessary re-renders
      const chatData = {
        selectedUserIds,
        selectedQuestionnaireId,
        isCreatingChat,
        totalUserCount: users.length,
        // Pass functions by reference to avoid recreation on each render
        handleConfirmCreateChat,
        handleClose,
      };

      setNewChatData(chatData);
    }

    // Clean up when component unmounts - but only if we're not going to CONFIRM_GROUP_CHAT
    return () => {
      isComponentMounted = false;
      // Don't clear newChatData here as it's needed for the confirm view
      // The data will be cleared when the entire new chat flow is completed
    };
  }, [
    // Only include primitive values as dependencies to prevent loops
    selectedUserIds,
    selectedQuestionnaireId,
    isCreatingChat,
    users.length,
    // Don't include function references in the dependency array
    setNewChatData,
    handleConfirmCreateChat,
    handleClose,
  ]);

  useEffect(() => {
    client.models.Questionnaire.list().then(({ data }) =>
      setQuestionnaires((data || []).filter(Boolean))
    );

    if (userId) {
      setLoadingUsers(true);
      client.models.User.list()
        .then(({ data }) => {
          setUsers(
            (data || []).filter(Boolean).filter((u) => u.userId !== userId)
          );
          console.log("Users loaded:", data);
        })

        .finally(() => setLoadingUsers(false));
    } else if (!isLoadingUser) {
      console.error("User ID not available.");
      setLoadingUsers(false);
    }
  }, [userId, isLoadingUser]);

  useEffect(() => {
    const idSet = new Set(users.map((u) => u.userId));
    if (idSet.size !== users.length) {
      console.warn("Warning: Duplicate user IDs detected!", users);
    }
  }, [users]);

  const mergedUsers = [
    ...users.map((u) => ({
      userId: u.userId,
      name: u.email || u.userId || u.name,
      email: u.email,
      isAI: false,
    })),
    ...questionnaires.map((q) => ({
      userId: `ai-${q.id}`,
      name: q.name || "Untitled Questionnaire",
      isAI: true,
      questionnaireId: q.id,
      description: (q as { description?: string }).description || "",
    })),
  ];

  useEffect(() => {
    if (onSelectionChange) {
      const selectedUserDetails = mergedUsers.filter((user) =>
        selectedUserIds.includes(user.userId)
      );
      onSelectionChange(selectedUserIds, selectedUserDetails);
    }
  }, [selectedUserIds, mergedUsers, onSelectionChange]);

  const groupUsersByLetter = (
    users: Array<{
      userId: string;
      name: string;
      email?: string;
      isAI?: boolean;
      questionnaireId?: string;
      description?: string;
    }>
  ) => {
    const grouped: Record<
      string,
      Array<{
        userId: string;
        name: string;
        email?: string;
        isAI?: boolean;
        questionnaireId?: string;
        description?: string;
      }>
    > = {};
    users.forEach((user) => {
      const displayText = user.email || user.name || "";
      const letter = (displayText[0] || "?").toUpperCase();
      if (!grouped[letter]) grouped[letter] = [];
      grouped[letter].push(user);
    });
    Object.keys(grouped).forEach((k) =>
      grouped[k].sort((a, b) =>
        (a.email || a.name || "").localeCompare(b.email || b.name || "")
      )
    );
    return grouped;
  };

  const handleUnifiedSelect = (
    userId: string,
    isAI: boolean,
    questionnaireId?: string
  ) => {
    if (isAI) {
      setSelectedUserIds([]);
      setSelectedQuestionnaireId(questionnaireId || null);
    } else {
      setSelectedQuestionnaireId(null);
      setSelectedUserIds((prev) =>
        prev.includes(userId)
          ? prev.filter((id) => id !== userId)
          : [...prev, userId]
      );
    }
  };

  interface SelectedUsersDisplayProps {
    selectedUserIds: string[];
    mergedUsers: Array<{
      userId: string;
      name: string;
      email?: string;
      isAI?: boolean;
      questionnaireId?: string;
      description?: string;
    }>;
    onCreateChat: () => void;
    onClose: () => void;
    isCreatingChat: boolean;
  }

  const SelectedUsersDisplay = ({
    selectedUserIds,
    mergedUsers,
    onCreateChat,
    onClose,
    isCreatingChat,
  }: SelectedUsersDisplayProps) => {
    // Filter to get only selected users' details
    const selectedUserDetails = mergedUsers.filter((user) =>
      selectedUserIds.includes(user.userId)
    );

    return (
      <div className="flex flex-col h-full overflow-hidden">
        {/* Header section */}
        <div className="flex items-center justify-between p-4  border-[#e5e5e5] flex-shrink-0">
          <h2 className="text-[20px] leading-[100%] tracking-[0%] font-normal text-[#171717]">
            New Chat
          </h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="h-8 w-8 rounded-full"
          >
            <span className="sr-only">Close</span>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Scrollable content area */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="mb-6">
            <div className="flex flex-wrap gap-4">
              {selectedUserDetails.length > 0 ? (
                selectedUserDetails.map((user) => (
                  <div key={user.userId} className="flex flex-col items-center">
                    <Avatar className="h-12 w-12 mb-2">
                      <img
                        src="/Avatar.png"
                        alt={user.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = "";
                          e.currentTarget.parentElement!.innerText = user.name
                            .charAt(0)
                            .toUpperCase();
                        }}
                      />
                    </Avatar>
                    <span className="text-sm text-[#737373] whitespace-nowrap truncate max-w-[80px]">
                      {user.name}
                    </span>
                  </div>
                ))
              ) : (
                <div className="text-gray-500 text-center py-4 w-full">
                  Select participants from the list
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer with buttons */}
        <div className="p-4 flex w-full gap-4 border-t border-[#e5e5e5] flex-shrink-0">
          <button
            type="button"
            className="rounded-2xl  flex-1 px-6 py-1.5 bg-[#E5E5E5] text-[#171717] font-medium  text-base hover:bg-[#e5e5e5] transition-colors"
            onClick={onClose}
            disabled={isCreatingChat}
            style={{
              boxShadow: "0px 1px 2px rgba(0, 0, 0, 0.05)",
              height: "40px",
            }}
          >
            Cancel
          </button>
          <button
            type="button"
            className="rounded-2xl px-6 py-1.5 flex-1 bg-[#4F86FF] text-white font-medium  text-base hover:bg-[#3A75FF] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={onCreateChat}
            disabled={selectedUserIds.length === 0 || isCreatingChat}
            style={{
              boxShadow: "0px 1px 2px rgba(0, 0, 0, 0.05)",
              height: "40px",
            }}
          >
            {isCreatingChat ? "Creating..." : "Create"}
          </button>
        </div>
      </div>
    );
  };

  if (isLoadingUser) {
    return <div>Loading user...</div>;
  }

  if (userError) {
    return (
      <div className="p-4 text-red-600 bg-red-100">
        Error loading user: {userError.message}
      </div>
    );
  }

  if (!userId) {
    return (
      <div className="p-4 text-orange-600 bg-orange-100">
        Please log in to create a chat.
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white md:min-h-[70vh] overflow-hidden">
      {/* Show header only on small screens if not explicitly hidden */}
      {!hideHeader && (
        <div className="max-sm:block hidden">
          <NewChatHeader
            selectedCount={selectedUserIds.length}
            totalCount={users.length}
            onConfirm={handleConfirmCreateChat}
            onClose={handleClose}
            questionnaireId={selectedQuestionnaireId}
            isLoading={isCreatingChat}
          />
        </div>
      )}

      <div className="flex flex-col md:flex-row h-full overflow-hidden">
        {/* Left panel - User list */}
        <div className="md:w-[300px] w-full flex flex-col h-full overflow-hidden">
          {loadingUsers && <div className="p-4">Loading users...</div>}
          {!loadingUsers && mergedUsers.length === 0 && (
            <div className="p-4">No users or AI found.</div>
          )}
          {!loadingUsers && mergedUsers.length > 0 && (
            <div className="flex flex-col h-full overflow-hidden ">
              <div className="flex-shrink-0">
                <NewChatSearchInput />
              </div>
              <div className="overflow-y-auto flex-1 h-full">
                {Object.entries(groupUsersByLetter(mergedUsers))
                  .sort(([a], [b]) => a.localeCompare(b))
                  .map(([letter, group]) => (
                    <NewChatListSection
                      key={letter}
                      letter={letter}
                      users={group}
                      selectedUserIds={
                        selectedQuestionnaireId
                          ? group
                              .filter(
                                (u) =>
                                  u.isAI &&
                                  u.questionnaireId === selectedQuestionnaireId
                              )
                              .map((u) => u.userId)
                          : selectedUserIds
                      }
                      onUserSelect={(userId) => {
                        const user = group.find((u) => u.userId === userId);
                        handleUnifiedSelect(
                          userId,
                          !!user?.isAI,
                          user?.questionnaireId
                        );
                      }}
                    />
                  ))}
              </div>
            </div>
          )}
        </div>

        {/* Right panel - Selected users */}
        <Separator orientation="vertical" className="h-full hidden md:block" />
        <div className="flex-1 md:block max-sm:hidden h-full overflow-hidden">
          <SelectedUsersDisplay
            selectedUserIds={selectedUserIds}
            mergedUsers={mergedUsers}
            onCreateChat={handleConfirmCreateChat}
            onClose={handleClose}
            isCreatingChat={isCreatingChat}
          />
        </div>
      </div>
    </div>
  );
};

export default NewChatList;
