"use client";

import { <PERSON><PERSON> } from "../ui/button";
import { useState, useContext, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Dialog, DialogContent, DialogHeader } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogContent } from "@/components/ui/alert-dialog";
import Image from "next/image";
import { Input } from "@/components/ui/input";
import { X } from "lucide-react";
import { ChatContext } from "../Chat/context/ChatContext";
import { useViewState } from "../Chat/context/ViewStateContext";
import MobileHeader from "../Chat/MobileHeader";

interface Props {
  selectedCount?: number;
  totalCount?: number;
  onConfirm?: (params: { chatName?: string }) => void;
  onClose?: () => void;
  questionnaireId?: string | null;
  isLoading?: boolean;
}

const NewChatHeader = ({
  selectedCount: propSelectedCount,
  totalCount: propTotalCount,
  onConfirm: propOnConfirm,
  onClose: propOnClose,
  questionnaireId: propQuestionnaireId,
  isLoading: propIsLoading,
}: Props) => {
  const { newChatData, setShowNewChatPage } = useContext(ChatContext);
  const { setActiveView } = useViewState();
  const router = useRouter();
  const [isMobile, setIsMobile] = useState(false);

  // Detect if device is mobile on client side
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener("resize", checkIfMobile);

    // Cleanup event listener
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  // Use either the props or data from context
  const selectedCount =
    propSelectedCount ?? newChatData?.selectedUserIds?.length ?? 0;
  const totalCount = propTotalCount ?? newChatData?.totalUserCount ?? 0;
  const questionnaireId =
    propQuestionnaireId ?? newChatData?.selectedQuestionnaireId ?? null;
  const isLoading = propIsLoading ?? newChatData?.isCreatingChat ?? false;

  // Use the callbacks from props or context
  const onConfirm =
    propOnConfirm ?? newChatData?.handleConfirmCreateChat ?? (() => {});

  // Enhanced onClose to set showNewChatPage to false and update view state
  const handleClose = () => {
    // Call original onClose if provided
    if (propOnClose) {
      propOnClose();
    } else if (newChatData?.handleClose) {
      newChatData.handleClose();
    }

    // Update both state systems for compatibility
    if (setShowNewChatPage) {
      setShowNewChatPage(false);
    }
    setActiveView("CHAT_LIST");

    // Navigate back to the main chats page
    router.push("/chats");
  };

  // Single dialog state for simplicity
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [groupName, setGroupName] = useState("");
  const canContinue = selectedCount > 0 || !!questionnaireId;

  const handleConfirm = () => {
    if (selectedCount > 1) {
      if (isMobile) {
        // For mobile, navigate to the confirm page
        console.log("Mobile view - Navigating to confirm page");
        router.push("/chats/new/confirm");
        setActiveView("CONFIRM_GROUP_CHAT");
        // Don't set view state here, let the layout handle it based on pathname
      } else {
        // For desktop, show the dialog
        setIsDialogOpen(true);
      }
    } else {
      onConfirm({});
    }
  };

  const handleCreateGroup = () => {
    setIsDialogOpen(false);

    onConfirm({
      chatName: groupName.trim() || `Group Chat (${selectedCount})`,
    });
  };

  // Extract header components for reuse
  const headerLeftContent = (
    <Button
      variant="ghost"
      size="icon"
      aria-label="Close"
      className="text-xl text-gray-400"
      onClick={handleClose}
      disabled={isLoading}
    >
      ×
    </Button>
  );

  const headerTitle = (
    <span className="font-semibold text-base text-neutral-900 truncate px-2">
      {selectedCount > 0 ? `Selected (${selectedCount})` : `New Chat `}
    </span>
  );

  const headerRightContent = (
    <Button
      className={`rounded-[16px] px-[22px] sm:px-[22px] py-[10px] text-base h-[38px] font-medium font-figtree transition-all shadow-[0px_3px_6px_0px_rgba(66,74,83,0.12)] ${
        canContinue
          ? "bg-[#5185FF] hover:bg-[#4175EF] text-white"
          : "bg-gray-200 text-gray-500"
      }`}
      variant={canContinue ? "default" : "secondary"}
      onClick={handleConfirm}
      disabled={!canContinue || isLoading}
    >
      <span className="max-sm:block hidden">
        {isLoading ? "Creating..." : "Confirm"}
      </span>
      <span className="max-sm:hidden block">
        {isLoading ? "Creating Chat..." : "Confirm"}
      </span>
    </Button>
  );

  return (
    <>
      <MobileHeader
        onBackClick={handleClose}
        centerContent={headerTitle}
        rightContent={headerRightContent}
      />

      {/* Use client-side only rendering with device-specific dialog */}
      {typeof window !== "undefined" && (
        <>
          {isMobile ? (
            /* Mobile Dialog */
            <div>
              <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogContent
                  className="p-0 max-w-full sm:max-w-md h-screen flex flex-col bg-[#F5F5F5]"
                  showCloseButton={false}
                >
                  <DialogHeader>
                    <MobileHeader
                      leftContent={
                        <Button
                          variant="ghost"
                          className="p-2 mr-2"
                          onClick={() => setIsDialogOpen(false)}
                        >
                          <Image
                            alt="back"
                            src="/arrow-left.svg"
                            width={24}
                            height={24}
                          />
                        </Button>
                      }
                      centerContent={
                        <span className="font-semibold text-base text-[#171717] font-figtree">
                          Set Group Name
                        </span>
                      }
                      showBackButton={false}
                      className="backdrop-blur-md"
                    />
                  </DialogHeader>

                  <div className="flex flex-col gap-3">
                    <div className="bg-white p-6">
                      <Input
                        className="w-full font-figtree text-[16px]"
                        placeholder="Group Name"
                        value={groupName}
                        onChange={(e) => setGroupName(e.target.value)}
                      />
                    </div>

                    <div className="p-2 px-5">
                      <Button
                        className="w-full bg-[#5185FF] hover:bg-[#4175EF] text-white rounded-full font-figtree font-medium text-[16px] py-3"
                        onClick={handleCreateGroup}
                      >
                        Create Group
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          ) : (
            /* Desktop AlertDialog */
            <div>
              <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <AlertDialogContent className="p-0 gap-0 max-w-md bg-white rounded-2xl shadow-[0px_40px_80px_0px_rgba(66,_74,_83,_0.24),_0px_0px_0px_1px_rgba(208,_215,_222,_0.5)] ">
                  <div className="p-6 flex justify-between items-center">
                    <h2 className="text-[20px] font-normal font-figtree text-[#171717]">
                      New Chat
                    </h2>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="rounded-2xl h-8 w-8 hover:bg-transparent"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      <X className="h-4 w-4 text-black" />
                      <span className="sr-only">Close</span>
                    </Button>
                  </div>

                  <div className="px-5 pb-[22px]">
                    <div className="rounded-2xl bg-[#F5F5F5] backdrop-blur-[10px] shadow-[0px_1px_1px_0px_rgba(0,0,0,0.04)]">
                      <Input
                        className="w-full font-figtree text-[16px] border-0 bg-transparent px-3 py-4 placeholder:text-[#A3A3A3]"
                        placeholder="Group Name"
                        value={groupName}
                        onChange={(e) => setGroupName(e.target.value)}
                      />
                    </div>
                  </div>

                  <div className="flex p-4 border-t border-[#D4D4D4] gap-2">
                    <Button
                      variant="ghost"
                      className="flex-1 rounded-[999px] bg-[#E5E5E5] hover:bg-[#E5E5E5]/90 text-[#171717] font-figtree font-medium h-auto py-[6px]"
                      onClick={() => setIsDialogOpen(false)}
                    >
                      Back
                    </Button>
                    <Button
                      className="flex-1 rounded-[999px] bg-[#5185FF] hover:bg-[#4175EF] text-white font-figtree font-medium h-auto py-[6px] shadow-[0px_3px_6px_0px_rgba(66,_74,_83,_0.12)]"
                      onClick={handleCreateGroup}
                    >
                      Create
                    </Button>
                  </div>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          )}
        </>
      )}
    </>
  );
};

export default NewChatHeader;
