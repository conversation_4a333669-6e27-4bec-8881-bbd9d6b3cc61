import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

const NewChatSearchInput = () => (
  <div className="px-4 py-[16px] md:bg-[#F5F5F5]">
    <div className="relative">
      <Input
        type="text"
        className="w-full rounded-full border border-gray-300 bg-white text-sm placeholder:text-gray-400 focus:outline-none pl-10 pr-4 py-2"
        placeholder="Search"
      />
      <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
    </div>
  </div>
);

export default NewChatSearchInput;
