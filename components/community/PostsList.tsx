import Link from "next/link";
import Moment from "@/components/discovery/Moment";

interface Post {
  id: string;
  title: string;
  content: string;
  userName: string;
  images: string[];
  likes: number;
  commentsCount: number;
  createdAt: string;
}

interface PostsListProps {
  communityId: string;
  posts: Post[];
  isLoading: boolean;
  isMember: boolean;
  community: any;
  transformPostToMoment: (post: Post) => any;
}

export default function PostsList({
  communityId,
  posts,
  isLoading,
  isMember,
  community,
  transformPostToMoment
}: PostsListProps) {
  return (
    <div className="flex-1 md:mx-6  md:mt-0 overflow-auto h-[calc(100vh-120px)]">
      
      {isLoading ? (
        <div className="animate-pulse">
          <div className="h-36 bg-gray-200 rounded-lg mb-4"></div>
          <div className="h-36 bg-gray-200 rounded-lg mb-4"></div>
        </div>
      ) : posts.length > 0 ? (
        <div className="space-y-2">
          {posts.map((post) => (
            <Moment key={post.id} moment={transformPostToMoment(post)} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-lg border border-gray-200">
          <p className="text-gray-500 mb-4">No posts in this community yet</p>
          {isMember && (
            <Link href={`/comunity/${communityId}/create-post`}>
              <button className="bg-blue-600 text-white px-5 py-2 rounded-full text-sm">
                Create the first post
              </button>
            </Link>
          )}
        </div>
      )}
    </div>
  );
}
