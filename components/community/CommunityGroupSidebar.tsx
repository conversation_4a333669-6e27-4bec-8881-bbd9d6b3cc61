import Image from "next/image";

const ownedGroups = [
  {
    id: "1",
    name: "Group Name",
    avatar: "/groupavatar.svg",
  },
];

const joinedGroups = [
  {
    id: "2",
    name: "Group Name",
    avatar: "/groupavatar.svg",
    unread: false,
  },
  {
    id: "3",
    name: "Group Name",
    avatar: "/groupavatar.svg",
    unread: true,
  },
  {
    id: "4",
    name: "Group Name",
    avatar: "/groupavatar.svg",
    unread: false,
  },
  {
    id: "5",
    name: "Group Name",
    avatar: "/groupavatar.svg",
    unread: false,
  },
];

export default function CommunityGroupSidebar() {
  return (
    <aside
      className="hidden md:flex relative top-0 left-0 z-30 w-[480px] min-w-[260px] max-w-[480px] h-screen bg-white border-r border-gray-100  flex-col py-4 px-0"
    >
      <div className="px-6 pb-4">
        <div className="flex items-center gap-2">
          <input
            type="text"
            placeholder="Search"
            className="w-full rounded-full border border-gray-200 px-4 py-2 text-sm focus:outline-none"
          />
          <button className="w-9 h-9 flex items-center justify-center rounded-full border border-gray-200">
            <span className="text-2xl font-bold">+</span>
          </button>
        </div>
      </div>
      {/* Moment */}
      <div className="px-6 pb-2">
        <div className="flex items-center gap-2">
          <span className="text-blue-600 text-lg">⧫</span>
          <span className="font-medium text-base">Moment</span>
        </div>
      </div>
      {/* Owned */}
      <div className="px-0">
        <div className="text-xs font-semibold text-gray-500 px-6 py-2">Owned</div>
        {ownedGroups.map((group) => (
          <div key={group.id} className="flex items-center gap-3 px-6 py-2 hover:bg-gray-100 cursor-pointer">
            <Image src={group.avatar} alt="" width={40} height={40} className="rounded-full" />
            <span className="font-medium text-base">{group.name}</span>
          </div>
        ))}
      </div>
      {/* Joined */}
      <div className="px-0">
        <div className="text-xs font-semibold text-gray-500 px-6 py-2">Joined</div>
        {joinedGroups.map((group, idx) => (
          <div
            key={group.id}
            className={`flex items-center gap-3 px-6 py-2 hover:bg-gray-100 cursor-pointer ${
              idx === 0 ? "bg-gray-100" : ""
            }`}
          >
            <Image src={group.avatar} alt="" width={40} height={40} className="rounded-full" />
            <span className="font-medium text-base">{group.name}</span>
            {group.unread && <span className="ml-2 w-2 h-2 bg-red-500 rounded-full inline-block"></span>}
          </div>
        ))}
      </div>
    </aside>
  );
}
