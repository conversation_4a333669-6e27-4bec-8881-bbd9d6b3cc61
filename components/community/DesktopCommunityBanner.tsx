import Image from "next/image";

interface DesktopCommunityBannerProps {
  community: any;
 
}

export default function DesktopCommunityBanner({
  community,
  
}: DesktopCommunityBannerProps) {
  return (
    <div className={'relative'}>
        <div className="bg-[#E9F0FF] z-0 absolute w-full pt-6 pb-[160px] hidden md:block">

        </div>
      <div className=" relative pl-8 pt-[7.5rem] z-30 items-end hidden md:flex">
          <div className="w-[128px] h-[128px] md:w-[128px] md:h-[128px] rounded-full overflow-hidden bg-white border-4 border-white shadow-sm">
            <Image
              src={community?.icon || "/groupavatar.svg"}
              alt={community?.name || "Community"}
              width={128}
              height={128}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="ml-4 mb-5 max-w-[60%]">
            <h1 className="text-2xl md:text-3xl font-bold text-gray-900 truncate w-full">{community?.name}</h1>
          </div>
        
        </div>
    </div>
  );
}
