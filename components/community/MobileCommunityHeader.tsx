import { ChevronRight } from "lucide-react";
import Image from "next/image";

interface MobileCommunityHeaderProps {
  community: any;
  isMember: boolean;
  handleJoinCommunity: () => void;
  showRules: boolean;
  setShowRules: (show: boolean) => void;
}

export default function MobileCommunityHeader({
  community, 
  isMember, 
  handleJoinCommunity, 
  showRules, 
  setShowRules
}: MobileCommunityHeaderProps) {
  return (
    <div className="md:hidden px-4 py-5">
      <div className="flex items-center">
        <div className="w-16 h-16 rounded-full overflow-hidden bg-white border-2 border-white shadow-sm">
          <Image
            src={community?.icon || "/groupavatar.svg"}
            alt={community?.name || "Community"}
            width={64}
            height={64}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="ml-4 flex-1">
          <h1 className="text-xl font-bold text-gray-900 word-break break-all" style={{ wordWrap: 'break-word', hyphens: 'auto' }}>{community?.name || "Community Name"}</h1>
          <p className="text-sm text-gray-500">{community?.membersCount || 0} members</p>
        </div>
        <div>
          {isMember ? (
            <div className="px-5 py-2 border rounded-full text-blue-600 border-blue-600 text-sm font-medium">
              Joined
            </div>
          ) : (
            <button 
              onClick={handleJoinCommunity}
              className="bg-blue-600 text-white px-5 py-2 rounded-full text-sm font-medium"
            >
              Join
            </button>
          )}
        </div>
      </div>
      
      <div className="mt-4 text-gray-600 text-sm whitespace-pre-wrap break-words">
        {community?.description || "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Corem ipsum dolor sit amet."}
      </div>
      
      <button 
        className="mt-3 text-blue-600 text-sm font-medium flex items-center"
        onClick={() => setShowRules(!showRules)}
      >
        View Rules
        <ChevronRight className="h-4 w-4 ml-1" />
      </button>
    </div>
  );
}
