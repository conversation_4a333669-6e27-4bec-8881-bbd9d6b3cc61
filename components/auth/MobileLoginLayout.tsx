import Image from "next/image"
import { LoginForm } from "./LoginForm"
import { SocialLoginButtons } from "./SocialLoginButtons"
import Link from "next/link"
import { Button } from "../ui/button"
import { Mail } from "lucide-react"
import { useState } from "react"

type Props = {
  loading: boolean
  errorMessage?: string
  onLogin: (data: any) => void
  onGoogleLogin: () => void
  onFacebookLogin: () => void
}

export function MobileLoginLayout({ loading, errorMessage, onLogin, onGoogleLogin, onFacebookLogin }: Props) {
  const [showEmailForm, setShowEmailForm] = useState(false)

  return (
    <div className="md:hidden flex flex-col items-center justify-center min-h-screen bg-gradient-to-b from-blue-50 to-white p-6">
      <div className="relative w-full max-w-md mb-8">
        <Image src="/6.png" alt="Abstract shape" width={400} height={400} className="w-full h-auto object-contain" />
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-white/50 backdrop-blur-sm p-4 rounded-lg w-4/5 text-center">
            <p className="text-gray-700 text-sm">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate libero et velit interdum, ac
              aliquet odio mattis.
            </p>
          </div>
        </div>
      </div>

      <div className="w-full max-w-md text-center mb-4">
        <div className="inline-block bg-white rounded-full px-6 py-2 mb-2">
          <p className="text-[#0051FF] font-medium">Sign In</p>
        </div>
        <h1 className="text-4xl font-bold text-[#0A1E42] mb-8">
          Welcome <span className="text-[#0051FF]">Back</span>
        </h1>
      </div>

      <div className="flex flex-col items-center space-y-4 w-full max-w-md">
        {showEmailForm ? (
          <>
            <LoginForm loading={loading} errorMessage={errorMessage} onSubmit={onLogin} />
            <Button
              type="button"
              onClick={() => setShowEmailForm(false)}
              className="w-full h-[52px] bg-gray-200 text-gray-900 font-medium rounded-full hover:bg-gray-300 transition-colors"
            >
              Back
            </Button>
          </>
        ) : (
          <>
            <Button
              onClick={() => setShowEmailForm(true)}
              className="w-full max-w-[398px] h-[52px] bg-gradient-to-r from-[#FFB300] via-[#0051FF] to-[#FF569F] text-white font-medium rounded-full hover:opacity-90 transition-opacity flex items-center justify-center gap-2"
            >
              <Mail className="h-5 w-5" /> Sign in with email
            </Button>
            <SocialLoginButtons loading={loading} onGoogleLogin={onGoogleLogin} onFacebookLogin={onFacebookLogin} />
            <p className="text-center text-sm text-gray-600 mt-8">
              Already have an account?{" "}
              <Link href="/register" className="text-[#0051FF] hover:underline">
                Create an account
              </Link>
            </p>
          </>
        )}
      </div>
    </div>
  )
}
