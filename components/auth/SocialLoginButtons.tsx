import { But<PERSON> } from "../ui/button"
import Image from "next/image"

type Props = {
  loading: boolean
  onGoogleLogin: () => void
  onFacebookLogin: () => void
}

export function SocialLoginButtons({ loading, onGoogleLogin, onFacebookLogin }: Props) {
  return (
    <div className="flex flex-col gap-4">
      <Button
        variant="outline"
        onClick={onGoogleLogin}
        className="flex items-center justify-between gap-2 rounded-full border border-gray-300 py-2 px-4"
        disabled={loading}
      >
        <Image src="/google.svg" width={20} height={20} alt="Google" />
        <span className="flex-1 text-center">Sign In with Google</span>
      </Button>
      <Button
        variant="outline"
        onClick={onFacebookLogin}
        className="flex items-center justify-between gap-2 rounded-full border border-gray-300 py-2 px-4"
        disabled={loading}
      >
        <Image src="/facebook.svg" width={9.17} height={15} alt="Facebook" />
        <span className="flex-1 text-center">Sign In with Facebook</span>
      </Button>
    </div>
  )
}
