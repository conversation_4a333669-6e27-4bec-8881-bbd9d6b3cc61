import Image from "next/image"
import Link from "next/link"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Eye, EyeOff, X } from "lucide-react"
import { useState } from "react"
import { UseFormReturn } from "react-hook-form"
import { FormData } from "@/types/auth"

import CustomButton from "@/components/ui/CustomButton";
import { useRouter } from "next/navigation"

interface MobileLayoutProps {
  form: UseFormReturn<FormData>;
  handleRegister: (data: FormData) => void;
  loading: boolean;
  errorMessage?: string;
  handleGoogleSignIn: () => void;
  handleFacebookSignIn: () => void;
}

export function MobileLayout(props: MobileLayoutProps) {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  return (
    <div className="md:hidden flex flex-col items-center justify-start min-h-screen h-screen max-h-screen bg-gradient-to-b p-5 overflow-hidden bg-white">
      {/* X icon at the top left */}
      <div className="absolute top-5 left-5 mb-8">
        <X className="w-6 h-6 cursor-pointer" onClick={() => router.push("/")} />
      </div>
      
      <div className="relative w-full max-w-md mb-8">
        {/* <Image src="/6.png" alt="Abstract shape" width={400} height={400} className="w-full h-auto max-h-[200px] object-contain" /> */}
        <div className="absolute inset-0 flex items-center justify-center"></div>
      </div>

      <div className="w-full max-w-md text-start mb-2">
        <div className="inline-block  rounded-full  py-2 mb-2">
          <p className="text-[#0F172A] text-[30px] font-[400]">Create an Account</p>
          <p className="text-[14px] font-[500] text-[#737373]">Vorem ipsum dolor sit amet, consectetur adipiscing elit.Vorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
        </div>
        {/* <h1 className="text-4xl font-bold text-[#0A1E42] mb-8">
          Get <span className="text-[#0051FF]">Started</span>
        </h1> */}
      </div>

      <div className="flex flex-col items-center space-y-3 mt-4 w-full max-w-md overflow-y-auto">
        <form onSubmit={props.form.handleSubmit(props.handleRegister)} className="space-y-2 w-full ">
          <div>
            <Input
              id="email"
              type="email"
              placeholder="Email"
              className={`w-full h-[43px] mb-3 py-3 pl-4 rounded-xl bg-[#F5F5F54D] border ${
                props.form.formState.errors.email ? "border-[#DC2625]" : "border-gray-300"
              } focus:outline-none focus:border-blue-500 text-gray-900`}
              {...props.form.register("email")}
            />
            {props.form.formState.errors.email && (
              <p className="text-red-500 text-sm mt-1">{props.form.formState.errors.email.message}</p>
            )}
          </div>

          <div>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="Password"
                className={`w-full h-[43px] mb-3 py-3 pl-4 rounded-xl bg-[#F5F5F54D] border ${
                  props.form.formState.errors.password ? "border-[#DC2625]" : "border-gray-300"
                } focus:outline-none focus:border-blue-500 text-gray-900`}
                {...props.form.register("password")}
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>
            {props.form.formState.errors.password && (
              <p className="text-red-500 text-sm mt-1">{props.form.formState.errors.password.message}</p>
            )}
          </div>

          <div>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm Password"
                className={`w-full h-[43px] mb-3 py-3 pl-4 rounded-xl bg-[#F5F5F54D] border ${
                  props.form.formState.errors.confirmPassword ? "border-[#DC2625]" : "border-gray-300"
                } focus:outline-none focus:border-blue-500 text-gray-900`}
                {...props.form.register("confirmPassword")}
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>
            {props.form.formState.errors.confirmPassword && (
              <p className="text-[#DC2625] text-sm mt-1">{props.form.formState.errors.confirmPassword.message}</p>
            )}
          </div>

          {props.errorMessage && <p className="text-[#DC2625] text-sm mb-4">{props.errorMessage}</p>}

          <CustomButton
            type="submit"
            loading={props.loading}
          >
            Continue
          </CustomButton>
        </form>

        <div className="flex items-center w-full">
          <div className="flex-1 h-px bg-gray-300"></div>
          <p className="mx-[2px] my-3 text-gray-600 text-sm whitespace-nowrap">Or</p>
          <div className="flex-1 h-px bg-gray-300"></div>
        </div>

        <div className="flex flex-col w-full gap-3">
          <Button
            variant="outline"
            onClick={props.handleFacebookSignIn}
            className="w-full h-[52px] flex items-center justify-start gap-2 rounded-xl border border-gray-300 pl-3 bg-white text-gray-900 hover:bg-gray-100"
            disabled={props.loading}
          >
            <Image src="/facebookk.svg" width={16} height={16} alt="Facebook" className="w-[28px] h-[28px]"/>
            <span className="flex-grow text-center">Continue with Facebook</span>
          </Button>
          <Button
            variant="outline"
            onClick={props.handleGoogleSignIn}
            className="w-full h-[52px] flex items-center justify-start gap-2 rounded-xl border border-gray-300 bg-white text-gray-900 hover:bg-gray-100"
            disabled={props.loading}
          >
            <Image src="/Group.svg" width={16} height={16} alt="Google" className="w-5 h-5"/>
            <span className="flex-grow text-center">Continue with Google</span>
          </Button>
        </div>

        <div className="w-full max-w-md fixed bottom-0 text-center py-5  ">
          <p className="text-center text-sm text-gray-600">
            Already have an account?{" "}
            <Link href="/login" className="text-[#0051FF] hover:underline">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}