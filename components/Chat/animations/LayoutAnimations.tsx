"use client";

import React from "react";
import { motion, Variants } from "framer-motion";

// Optimized animation variants for better performance
export const chatPanelVariants: Variants = {
  hidden: {
    opacity: 0,
    transform: "translateY(8px)",
  },
  visible: {
    opacity: 1,
    transform: "translateY(0px)",
    transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] },
  },
  exit: {
    opacity: 0,
    transform: "translateY(-8px)",
    transition: { duration: 0.15 },
  },
};

export const sidebarVariants: Variants = {
  mobile: {
    width: "100%",
    transition: { duration: 0.25, ease: [0.4, 0, 0.2, 1] },
  },
  desktop: {
    width: "400px",
    transition: { duration: 0.25, ease: [0.4, 0, 0.2, 1] },
  },
  hidden: {
    width: "0px",
    transition: { duration: 0.25, ease: [0.4, 0, 0.2, 1] },
  },
};

export const mainContentVariants: Variants = {
  full: {
    transform: "translateX(0px)",
    transition: { duration: 0.25, ease: [0.4, 0, 0.2, 1] },
  },
  withSidebar: {
    transform: "translateX(0px)",
    transition: { duration: 0.25, ease: [0.4, 0, 0.2, 1] },
  },
  withDetails: {
    transform: "translateX(0px)",
    transition: { duration: 0.25, ease: [0.4, 0, 0.2, 1] },
  },
};

export const detailsPanelVariants: Variants = {
  hidden: {
    opacity: 0,
    transform: "translateX(100%)",
  },
  visible: {
    opacity: 1,
    transform: "translateX(0%)",
    transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] },
  },
  exit: {
    opacity: 0,
    transform: "translateX(100%)",
    transition: { duration: 0.15 },
  },
};

export const mobileDetailsVariants: Variants = {
  hidden: {
    opacity: 0,
    transform: "translateY(100%)",
  },
  visible: {
    opacity: 1,
    transform: "translateY(0%)",
    transition: { duration: 0.25, ease: [0.4, 0, 0.2, 1] },
  },
  newChat: {
    opacity: 1,
    transform: "translateY(0%)",
    transition: { duration: 0.25, ease: [0.4, 0, 0.2, 1] },
  },
  exit: {
    opacity: 0,
    transform: "translateY(100%)",
    transition: { duration: 0.2 },
  },
};

export const containerVariants: Variants = {
  normal: {
    transition: { duration: 0.2 },
  },
  expanded: {
    transition: { duration: 0.2 },
  },
};

interface AnimatedPanelProps {
  children: React.ReactNode;
  variants: Variants;
  initialState: string;
  animateState: string;
  className?: string;
  custom?: number;
}

export const AnimatedPanel = ({
  children,
  variants,
  initialState,
  animateState,
  className = "",
  custom,
}: AnimatedPanelProps) => {
  return (
    <motion.div
      className={className}
      variants={variants}
      initial={initialState}
      animate={animateState}
      exit="exit"
      custom={custom}
    >
      {children}
    </motion.div>
  );
};
