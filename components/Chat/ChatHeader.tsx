import Image from "next/image";
import { toast } from "sonner";
import { ChatParticipantFormatted } from "@/types/chat";
import MobileHeader from "./MobileHeader";
import { ChatDropDownGroup } from "./dropdowns/ChatDropDownGroup";
import { ChatDropDown } from "./dropdowns/ChatDropDown";
import { usePathname } from "next/navigation";
import HealthMenu from "../Health/HealthMenu";
import { useMobile } from "@/hooks/use-mobile";

interface ChatData {
  id: string;
  name: string;
  type: string;
  members: ChatParticipantFormatted[];
  isGroup: boolean;
}

interface ChatHeaderProps {
  chatData: ChatData;
  onGroupDetailsClick?: () => void;
  onBackClick: () => void;
  icon?: string;
  handleLeaveGroup?: () => Promise<void>;
  onPanelToggle?: (isOpen: boolean) => void;
  isPanelOpen?: boolean; // Add this prop
}

const ChatHeader = ({
  chatData,
  onGroupDetailsClick,
  onBackClick,
  icon,
  handleLeaveGroup,
  onPanelToggle,
  isPanelOpen = false,
}: ChatHeaderProps) => {
  const pathname = usePathname();
  const isHealthPage = pathname === "/health" || pathname?.includes("/health");

  const isMobile = useMobile();
  // Ensure members array is valid to prevent rendering issues
  const validMembers = Array.isArray(chatData.members) ? chatData.members : [];
  const firstMember = validMembers.length > 0 ? validMembers[0] : null;

  const chatAvatar = (
    <div className="flex items-center">
      <div className="relative w-[60px] h-[60px] rounded-full p-1 bg-[rgba(37,103,255,0.08)]">
        <div className="rounded-full p-1 bg-[rgba(37,103,255,0.14)]">
          <div className="rounded-full p-0.5 bg-[rgba(37,103,255,0.34)]">
            {icon ? (
              <div className="w-[40px] h-[40px] rounded-full overflow-hidden flex items-center justify-center">
                <Image
                  src={icon}
                  alt={chatData.name}
                  width={40}
                  height={40}
                  className="object-cover"
                />
              </div>
            ) : chatData.type === "AI" ? (
              <div className="w-[40px] h-[40px] rounded-full overflow-hidden flex items-center justify-center">
                <Image
                  src="/chat/logo_als.png"
                  alt="AI Assistant"
                  width={40}
                  height={40}
                  className="object-cover"
                />
              </div>
            ) : chatData.isGroup ? (
              <div className="w-[40px] h-[40px] rounded-full overflow-hidden flex items-center justify-center">
                <Image
                  src="/groupavatar.svg"
                  alt="Group"
                  width={40}
                  height={40}
                />
              </div>
            ) : (
              <div className="w-[40px] h-[40px] rounded-full overflow-hidden bg-gradient-to-br from-[#4000FF] to-[#0093FC] flex items-center justify-center">
                <Image
                  src={firstMember?.user?.profilePicture || "/Avatar.png"}
                  alt={firstMember?.user?.email || "User"}
                  width={40}
                  height={40}
                  className="object-cover"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const chatTitle = (
    <h1 className="text-[16px] font-semibold text-[#171717] truncate">
      {chatData.type === "GROUP" || chatData.type === "AI"
        ? chatData.name
        : firstMember?.user?.email || chatData.name}
    </h1>
  );

  return (
    <MobileHeader
      leftContent={chatAvatar}
      centerContent={chatTitle}
      onBackClick={onBackClick}
      rightContent={
        <div>
          {isMobile && onGroupDetailsClick && (
            <button
              onClick={() => {
                if (chatData.type === "AI") {
                
                } else {
                  onGroupDetailsClick();
                }
              }}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
            >
              <Image
                alt="chat details"
                src="/chats/chat-details.svg"
                width={24}
                height={24}
                className="cursor-pointer"
              />
            </button>
          )}

          {/* HealthMenu integration */}
          {isHealthPage && (
            <HealthMenu
              isVisible={true}
              onPanelToggle={onPanelToggle || (() => {})}
              isPanelOpen={isPanelOpen} // Pass panel state to HealthMenu
            />
          )}

          {!isMobile && (
            <>
              {chatData.type === "GROUP" && handleLeaveGroup ? (
                <ChatDropDownGroup
                  chatName={chatData.name}
                  chatId={chatData.id}
                  members={validMembers}
                  handleLeaveGroup={handleLeaveGroup}
                />
              ) : (
                firstMember && (
                  <ChatDropDown userData={firstMember} chatId={chatData.id} />
                )
              )}
            </>
          )}
        </div>
      }
    />
  );
};

export default ChatHeader;
