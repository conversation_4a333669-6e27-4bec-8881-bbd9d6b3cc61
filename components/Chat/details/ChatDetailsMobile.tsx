import { Chat } from "@/types/chat";
import { motion, AnimatePresence } from "framer-motion";
import { ChatDetailsMobileContainer } from "@/components/Chat/containers/ChatDetailsMobileContainer";
import { useViewState } from "@/components/Chat/context/ViewStateContext";

// Optimized animation variants with GPU-accelerated properties
const mobileDetailsVariants = {
  hidden: {
    opacity: 0,
    transform: "translateY(100%)",
  },
  visible: {
    opacity: 1,
    transform: "translateY(0%)",
    transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] },
  },
  newChat: {
    opacity: 1,
    transform: "translateY(0%)",
    transition: { duration: 0.2, ease: [0.4, 0, 0.2, 1] },
  },
  exit: {
    opacity: 0,
    transform: "translateY(100%)",
    transition: { duration: 0.15, ease: [0.4, 0, 1, 1] },
  },
};

interface ChatDetailsMobileProps {
  currentChat: Chat | null;
  showAllMembers: boolean;
  setShowAllMembers: (value: boolean) => void;
  isMobile: boolean;
}

/**
 * Mobile details component that coordinates which detail view to show
 */
export const ChatDetailsMobile = ({
  currentChat,
  showAllMembers,
  setShowAllMembers,
  isMobile,
}: ChatDetailsMobileProps) => {
  const { activeView } = useViewState();

  // Only show on mobile and in specific views
  const showDetails = activeView === "CHAT_DETAILS";
  const showNewChat = activeView === "NEW_CHAT";
  const showUserDetails = activeView === "USER_DETAILS";

  if (!isMobile || (!showDetails && !showNewChat && !showUserDetails)) {
    return null;
  }

  return (
    <AnimatePresence>
      {(showDetails || showNewChat || showUserDetails) && (
        <motion.div
          className="flex-1 flex flex-col overflow-y-auto"
          variants={mobileDetailsVariants}
          initial="hidden"
          animate={showNewChat ? "newChat" : "visible"}
          exit="exit"
        >
          <ChatDetailsMobileContainer
            currentChat={currentChat}
            showAllMembers={showAllMembers}
            setShowAllMembers={setShowAllMembers}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
};
