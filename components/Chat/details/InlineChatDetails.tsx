import { Chat } from "@/types/chat";
import { ChatDetailsContainer } from "@/components/Chat/containers/ChatDetailsContainer";
import { NewChatContainer } from "@/components/Chat/containers/NewChatContainer";
import { useContext } from "react";
import { ChatContext } from "@/components/Chat/context/ChatContext";
import { useViewState } from "@/components/Chat/context/ViewStateContext";

interface InlineChatDetailsProps {
  currentChat: Chat | null;
  handleLeaveGroup: () => Promise<void>;
}

/**
 * Inline details component that delegates to specialized containers
 * based on the current context state
 */
export const InlineChatDetails = ({
  currentChat,
  handleLeaveGroup,
}: InlineChatDetailsProps) => {
  const { showChatDetails, showNewChatPage } = useContext(ChatContext);
  const { isViewActive } = useViewState();

  const shouldShowDetails = showChatDetails || isViewActive("CHAT_DETAILS");
  const shouldShowNewChat = showNewChatPage || isViewActive("NEW_CHAT");

  if (!shouldShowDetails && !shouldShowNewChat) return null;

  return (
    <>
      {shouldShowNewChat && <NewChatContainer />}
      {shouldShowDetails && !shouldShowNewChat && (
        <ChatDetailsContainer
          currentChat={currentChat}
          handleLeaveGroup={handleLeaveGroup}
        />
      )}
    </>
  );
};
