"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import PageHeader from "@/components/ui/PageHeader";
import { Loader2, X } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { generateClient } from "aws-amplify/data";
import { type Schema } from "@/amplify/data/resource";

const client = generateClient<Schema>();

export default function LeaveGroupDialog({
  children,
  chatId,
  userId,
  onLeaveSuccess,
}: {
  children: React.ReactNode;
  chatId: string;
  userId: string;
  onLeaveSuccess?: () => void;
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleLeaveGroup = async () => {
    if (!chatId || !userId) {
      setError("Missing required information");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
   
      await client.models.ChatParticipant.delete({
        chatId,
        userId,
      });

      
      if (onLeaveSuccess) {
        onLeaveSuccess();
      } else {
     
        router.push("/chats");
        router.refresh();
      }
    } catch (err) {
      console.error("Error leaving group:", err);
      setError("Failed to leave group. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
      <AlertDialogContent className="max-w-[300px] rounded-lg p-0 overflow-hidden">
        <PageHeader
          title={<span className="font-semibold">Leave Group</span>}
          rightContent={
            <AlertDialogCancel className="p-0 m-0 h-auto bg-transparent border-none hover:bg-transparent">
              <X size={18} className="text-gray-500" />
            </AlertDialogCancel>
          }
          background="transparent"
          className="border-b border-gray-200 py-2"
          sticky={false}
        />

        <div className="p-4">
          <p className="text-sm text-center text-gray-700 mb-4">
            You are about to leave the group.
            <br />
            This action cannot be undone.
          </p>

          {error && (
            <p className="text-sm text-center text-red-500 mb-4">{error}</p>
          )}

          <div className="flex flex-col gap-2">
            <AlertDialogAction
              className="w-full bg-red-500 hover:bg-red-600 text-white rounded-full py-3 flex items-center justify-center"
              onClick={handleLeaveGroup}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Leaving...
                </>
              ) : (
                "Leave Group"
              )}
            </AlertDialogAction>
            <AlertDialogCancel className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-full py-3">
              Cancel
            </AlertDialogCancel>
          </div>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}
