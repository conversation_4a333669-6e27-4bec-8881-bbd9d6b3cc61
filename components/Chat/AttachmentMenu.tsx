import Image from "next/image";

const menuItems = [
  { name: "Photo", icon: "/image.svg" }, // Assuming icon paths
  { name: "Camera", icon: "/camera.svg" },
  { name: "Video Call", icon: "/cam.svg" },
  { name: "Voice call", icon: "/phone.svg" },
  { name: "Voice Input", icon: "/input.svg" }, // Example icon name
  { name: "Saved Posts", icon: "/rainbow.svg" }, // Example icon name
  { name: "Friends Profile", icon: "/users-group.svg" }, // Example icon name
  { name: "File", icon: "/file.svg" }, // Example icon name
];

export default function AttachmentMenu() {
  return (
    <div className="  rounded-t-lg ">
      <div className="grid grid-cols-4 gap-4 text-center">
        {menuItems.map((item) => (
          <button
            key={item.name}
            className="flex flex-col items-center justify-center space-y-1 text-gray-700 hover:text-blue-600"
            aria-label={item.name}
          >
            <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center border border-gray-200 shadow-sm">
              {/* Replace with actual icons or Icon components */}
              <Image src={item.icon} width={24} height={24} alt={item.name} />
            </div>
            <span className="text-xs">{item.name}</span>
          </button>
        ))}
      </div>
    </div>
  );
}
