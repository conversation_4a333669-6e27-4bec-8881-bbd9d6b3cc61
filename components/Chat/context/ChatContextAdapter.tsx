"use client";

import React, { ReactNode } from "react";
import { ChatContext } from "./ChatContext";
import { useViewState } from "./ViewStateContext";

/**
 * A wrapper component that provides backward compatibility
 * by mapping ViewState values to the old ChatContext interface
 */
export const ChatContextAdapter = ({ children }: { children: ReactNode }) => {
  const {
    activeView,
    setActiveView,
    currentChat,
    setCurrentChat,
    selectedUserDetail,
    setSelectedUserDetail,
    newChatData,
    setNewChatData,
  } = useViewState();

  // Map the new view state to the old boolean flags
  const showChatDetails =
    activeView === "CHAT_DETAILS" || activeView === "USER_DETAILS";
  const showNewChatPage = activeView === "NEW_CHAT";

  // Map the old boolean setters to the new view state setters
  const setShowChatDetails = (show: boolean) => {
    if (show) {
      setActiveView("CHAT_DETAILS");
    } else if (activeView === "CHAT_DETAILS" || activeView === "USER_DETAILS") {
      setActiveView("CHAT_CONVERSATION");
    }
  };

  const setShowNewChatPage = (show: boolean) => {
    if (show) {
      setActiveView("NEW_CHAT");
    } else if (activeView === "NEW_CHAT") {
      setActiveView("CHAT_LIST");
    }
  };

  // Create the compatibility value object
  const contextValue = {
    currentChat,
    setCurrentChat,
    selectedUserDetail,
    setSelectedUserDetail,
    showChatDetails,
    setShowChatDetails,
    showNewChatPage,
    setShowNewChatPage,
    newChatData,
    setNewChatData,
  };

  return (
    <ChatContext.Provider value={contextValue}>{children}</ChatContext.Provider>
  );
};
