"use client";

import React, { createContext, useContext, useState, ReactNode } from "react";
import { Chat } from "@/types/chat";

import { NewChatData, SelectedUser } from "./ChatContext";

// Define all possible view states in the chat interface
export type ChatViewState =
  | "CHAT_LIST" // Main chat list view
  | "CHAT_DETAILS" // Chat details/info view
  | "USER_DETAILS" // Individual user details view
  | "NEW_CHAT" // New chat creation view
  | "CONFIRM_GROUP_CHAT" // Set group name for new chat
  | "CHAT_CONVERSATION"; // Active chat conversation view

interface ViewStateContextType {
  // Current active view
  activeView: ChatViewState;
  // Change the active view
  setActiveView: (view: ChatViewState) => void;
  // View history for back navigation
  viewHistory: ChatViewState[];
  // Go back to previous view
  goBack: () => void;
  // Check if a view is active
  isViewActive: (view: ChatViewState) => boolean;
  // The currently selected chat
  currentChat: Chat | null;
  setCurrentChat: (chat: Chat | null) => void;
  // The currently selected user detail
  selectedUserDetail: SelectedUser | null;
  setSelectedUserDetail: (user: SelectedUser | null) => void;
  // New chat creation data
  newChatData: NewChatData | null;
  setNewChatData: (data: NewChatData | null) => void;
}

const ViewStateContext = createContext<ViewStateContextType | undefined>(
  undefined
);

export const ViewStateProvider = ({ children }: { children: ReactNode }) => {
  const [activeView, setActiveViewState] = useState<ChatViewState>("CHAT_LIST");
  const [viewHistory, setViewHistory] = useState<ChatViewState[]>([]);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [selectedUserDetail, setSelectedUserDetail] =
    useState<SelectedUser | null>(null);
  const [newChatData, setNewChatData] = useState<NewChatData | null>(null);

  const setActiveView = (view: ChatViewState) => {
    // Add current view to history before changing
    if (activeView !== view) {
      setViewHistory((prev) => [...prev, activeView]);
      setActiveViewState(view);
    }
  };

  const goBack = () => {
    if (viewHistory.length > 0) {
      // Get the last view from history
      const prevView = viewHistory[viewHistory.length - 1];
      // Remove it from history
      setViewHistory((prev) => prev.slice(0, -1));
      // Set it as active view
      setActiveViewState(prevView);
    } else {
      // Default to chat list if no history
      setActiveViewState("CHAT_LIST");
    }
  };

  const isViewActive = (view: ChatViewState) => activeView === view;

  const value = {
    activeView,
    setActiveView,
    viewHistory,
    goBack,
    isViewActive,
    currentChat,
    setCurrentChat,
    selectedUserDetail,
    setSelectedUserDetail,
    newChatData,
    setNewChatData,
  };

  return (
    <ViewStateContext.Provider value={value}>
      {children}
    </ViewStateContext.Provider>
  );
};

export const useViewState = () => {
  const context = useContext(ViewStateContext);
  if (context === undefined) {
    throw new Error("useViewState must be used within a ViewStateProvider");
  }
  return context;
};
