import { Chat } from "@/types/chat";
import { createContext, useContext } from "react";

// Extended SelectedUser interface to cover both id and userId scenarios
export interface SelectedUser {
  id: string;
  userId?: string; // Adding userId for flexibility
  email?: string;
  profilePicture?: string;
}

// Interface for new chat related data
export interface NewChatData {
  selectedUserIds: string[];
  selectedQuestionnaireId: string | null;
  isCreatingChat: boolean;
  totalUserCount: number;
  handleConfirmCreateChat: (params?: { chatName?: string }) => Promise<void>;
  handleClose: () => void;
}

export type ChatContextType = {
  // Current chat data
  currentChat: Chat | null;
  setCurrentChat: (chat: Chat | null) => void;

  // Selected user details (for viewing user information)
  selectedUserDetail: SelectedUser | null;
  setSelectedUserDetail: (user: SelectedUser | null) => void;

  // Chat details panel visibility - for viewing chat/user/group details
  showChatDetails: boolean;
  setShowChatDetails: (show: boolean) => void;

  // New chat panel visibility - for creating new chats
  showNewChatPage: boolean;
  setShowNewChatPage: (show: boolean) => void;

  // New chat data - for storing state during chat creation
  newChatData: NewChatData | null;
  setNewChatData: (data: NewChatData | null) => void;
};

export const ChatContext = createContext<ChatContextType>({
  currentChat: null,
  setCurrentChat: () => {},
  selectedUserDetail: null,
  setSelectedUserDetail: () => {},
  showChatDetails: false,
  setShowChatDetails: () => {},
  showNewChatPage: false,
  setShowNewChatPage: () => {},
  newChatData: null,
  setNewChatData: () => {},
});

export const useCurrentChat = () => useContext(ChatContext);
