"use client";

import type React from "react";
import { useState, useRef, useMemo } from "react";
import { cn } from "@/lib/utils";
import { GhostMessageType, MessageWithoutChat } from "@/types/chat";
import dynamic from "next/dynamic";
import { useMap as useMapHook } from "@/hooks/useMap";
import Image from "next/image";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";

const LeafletMap = dynamic(() => import("../map/LeafletMapComponent"), {
  ssr: false,
});

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { AI_ASSISTANT_ID } from "@/constants/chat";

import Link from "next/link";

// Sample data for charts
const barChartData = [
  { ticker: "A", value1: 40, value2: 0 },
  { ticker: "B", value1: 80, value2: 0 },
  { ticker: "C", value1: 60, value2: 0 },
  { ticker: "D", value1: 120, value2: 180 },
  { ticker: "E", value1: 0, value2: 210 },
  { ticker: "F", value1: 180, value2: 0 },
  { ticker: "G", value1: 130, value2: 0 },
  { ticker: "H", value1: 200, value2: 180 },
  { ticker: "I", value1: 130, value2: 0 },
  { ticker: "J", value1: 200, value2: 0 },
  { ticker: "K", value1: 160, value2: 0 },
];

const scatterChartData = [
  { group: "A", x: 10, y: 30, z: 200 },
  { group: "A", x: 15, y: 40, z: 200 },
  { group: "A", x: 20, y: 80, z: 200 },
  { group: "A", x: 25, y: 100, z: 200 },
  { group: "A", x: 30, y: 120, z: 200 },
  { group: "A", x: 35, y: 150, z: 200 },
  { group: "A", x: 40, y: 140, z: 200 },
  { group: "B", x: 12, y: 35, z: 200 },
  { group: "B", x: 18, y: 40, z: 200 },
  { group: "B", x: 22, y: 45, z: 200 },
  { group: "B", x: 28, y: 50, z: 200 },
  { group: "B", x: 32, y: 60, z: 200 },
  { group: "B", x: 38, y: 55, z: 200 },
  { group: "C", x: 30, y: 20, z: 200 },
  { group: "C", x: 32, y: 25, z: 200 },
  { group: "C", x: 34, y: 30, z: 200 },
  { group: "C", x: 36, y: 35, z: 200 },
  { group: "C", x: 38, y: 40, z: 200 },
];

const lineChartData = Array.from({ length: 24 }, (_, i) => {
  const date = new Date(2024, 0, 1);
  date.setMonth(date.getMonth() + i);
  return {
    date: date.toISOString().substring(0, 7),
    value1: Math.floor(Math.random() * 100) + 50,
    value2: Math.floor(Math.random() * 50) + 25,
  };
});

interface MessageProps {
  msg: MessageWithoutChat | GhostMessageType;
  isGhostMessage?: boolean;
  showCursor?: boolean;
  currentUserId: string | null;
  withId?: boolean;
}

const Message: React.FC<MessageProps> = ({
  msg,
  isGhostMessage = false,
  showCursor = false,
  currentUserId,
  withId = false,
}) => {
  const messageRef = useRef<HTMLDivElement>(null);
  const isCurrentUser = currentUserId && msg.userId === currentUserId;

  // Always show charts (set to true as required)
  const shouldRenderCharts = false;
  const shouldRenderMap = (msg.showMap && !isGhostMessage) || false;

  const { entitiesWithinRadius, lat, long, calculateDistance } = useMapHook(
    51.574,
    -0.1278,
    11,
    "",
    "",
    shouldRenderMap
  );

  const [selectedEntity, setSelectedEntity] = useState<any | null>(null);
  const [panTo, setPanTo] = useState<{ lat: number; long: number } | null>(
    null
  );

  const filteredEntities = useMemo(() => {
    if (!shouldRenderMap) return [];
    return entitiesWithinRadius.filter(
      (entity) => calculateDistance(lat, long, entity.lat, entity.long) <= 100
    );
  }, [entitiesWithinRadius, lat, long, calculateDistance, shouldRenderMap]);

  const memoizedMap = useMemo(() => {
    if (!shouldRenderMap) return null;
    return (
      <LeafletMap
        lat={lat}
        long={long}
        zoom={11}
        filteredEntities={filteredEntities}
        selectedEntity={selectedEntity}
        setSelectedEntity={setSelectedEntity}
        panTo={panTo}
      />
    );
  }, [filteredEntities, selectedEntity, panTo, shouldRenderMap, lat, long]);

  // Chart components
  const renderBarChart = () => (
    <div className="mt-[10px] w-[100%] md:w-[70%]">
      <div className="chart-container p-4 bg-white rounded-lg shadow-sm border border-gray-100">
        <div className="flex items-center text-[#008CFF] mb-2">
          <Image
            src="/outpatient/heart-rate-monitor-blue.svg"
            alt="Line Chart Icon"
            width={24}
            height={24}
            className="mr-2"
          />
          <span className="font-medium">Title</span>
        </div>
        <p className="text-gray-700 mb-2">Summary</p>
        <ResponsiveContainer width="100%" height={200}>
          <BarChart data={barChartData}>
            <CartesianGrid
              strokeDasharray="3 3"
              horizontal={true}
              vertical={false}
            />
            <XAxis dataKey="ticker" axisLine={false} tickLine={false} />
            <YAxis
              domain={[0, "dataMax + 50"]}
              axisLine={false}
              tickLine={false}
            />
            <Tooltip />
            <Bar dataKey="value1" fill="#4285F4" radius={[4, 4, 0, 0]} />
            <Bar dataKey="value2" fill="#26D07C" radius={[4, 4, 0, 0]} />
            <Legend
              iconType="circle"
              payload={[
                { value: "Legend", color: "#4285F4" },
                { value: "Legend", color: "#26D07C" },
              ]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );

  const renderScatterChart = () => (
    <div className="mt-[10px] w-[100%] md:w-[70%]">
      <div className="chart-container p-4 bg-white rounded-lg shadow-sm border border-gray-100">
        <div className="flex items-center text-[#FF899E] mb-2">
          <Image
            src="/outpatient/heart-rate-monitor.svg"
            alt="Line Chart Icon"
            width={24}
            height={24}
            className="mr-2"
          />
          <span className="font-medium">Title</span>
        </div>
        <p className="text-gray-700 mb-2">Summary</p>
        <ResponsiveContainer width="100%" height={200}>
          <ScatterChart>
            <CartesianGrid
              strokeDasharray="3 3"
              horizontal={true}
              vertical={false}
            />
            <XAxis
              dataKey="x"
              name="Ticker"
              axisLine={false}
              tickLine={false}
            />
            <YAxis
              dataKey="y"
              name="Ticker"
              domain={[0, "dataMax + 50"]}
              axisLine={false}
              tickLine={false}
            />
            <Tooltip cursor={{ strokeDasharray: "3 3" }} />
            <Scatter
              name="Group A"
              data={scatterChartData.filter((d) => d.group === "A")}
              fill="#FF9EB3"
              shape="circle"
            />
            <Scatter
              name="Group B"
              data={scatterChartData.filter((d) => d.group === "B")}
              fill="#4285F4"
              shape="circle"
            />
            <Scatter
              name="Group C"
              data={scatterChartData.filter((d) => d.group === "C")}
              fill="#26D07C"
              shape="circle"
            />
            <Legend
              iconType="circle"
              payload={[
                { value: "Legend", color: "#FF9EB3" },
                { value: "Legend", color: "#4285F4" },
                { value: "Legend", color: "#26D07C" },
              ]}
            />
          </ScatterChart>
        </ResponsiveContainer>
      </div>
    </div>
  );

  const renderLineChart = () => (
    <div className="mt-[10px] w-[100%] md:w-[70%]">
      <div className="chart-container p-4 bg-white rounded-lg shadow-sm border border-gray-100">
        <div className="flex items-center text-[#FF899E] mb-2">
          <Image
            src="/outpatient/heart-rate-monitor.svg"
            alt="Line Chart Icon"
            width={24}
            height={24}
            className="mr-2"
          />
          <span className="font-medium">Title</span>
        </div>
        <p className="text-gray-700 mb-2">Summary</p>
        <ResponsiveContainer width="100%" height={200}>
          <LineChart data={lineChartData}>
            <CartesianGrid
              strokeDasharray="3 3"
              horizontal={true}
              vertical={false}
            />
            <XAxis
              dataKey="date"
              axisLine={false}
              tickLine={false}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.getMonth() === 0 ? `${date.getFullYear()} Jan` : "";
              }}
            />
            <YAxis
              domain={[0, "dataMax + 50"]}
              axisLine={false}
              tickLine={false}
            />
            <Tooltip />
            <Line
              type="monotone"
              dataKey="value1"
              stroke="#FF9EB3"
              strokeWidth={2}
              dot={false}
            />
            <Line
              type="monotone"
              dataKey="value2"
              stroke="#4285F4"
              strokeWidth={2}
              dot={false}
            />
            <Legend
              iconType="circle"
              payload={[
                { value: "Legend", color: "#FF9EB3" },
                { value: "Legend", color: "#4285F4" },
              ]}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );

  return (
    <div
      ref={messageRef}
      id={withId ? msg.id : undefined}
      className={cn(
        "flex flex-col w-full",
        isCurrentUser ? "items-end" : "items-start"
      )}
      data-testid={`message-${msg.id}`}
      data-ghost={isGhostMessage ? "true" : "false"}
    >
      <div className={cn("flex w-full items-start gap-2.5")}>
        {!isCurrentUser && (
          <Avatar className="h-8 w-8">
            <AvatarImage
              src={
                msg.userId === AI_ASSISTANT_ID
                  ? "/chat/health-logo.svg"
                  : "/placeholder.svg?height=32&width=32"
              }
              alt={msg.userId === AI_ASSISTANT_ID ? "AI" : "User"}
            />
            <AvatarFallback>
              {msg.userId === AI_ASSISTANT_ID
                ? "AI"
                : msg.user?.email?.charAt(0).toUpperCase() || "U"}
            </AvatarFallback>
          </Avatar>
        )}
        <div
          className={cn(
            "flex flex-1 flex-col gap-1 w-full",
            isCurrentUser ? "items-end" : "items-start"
          )}
        >
          <div
            className={cn(
              "flex flex-col gap-1 px-4 py-2 rounded-xl max-w-[80%]",
              isCurrentUser
                ? "bg-[#5185FF] text-white rounded-tr-none ml-auto"
                : "bg-[#F5F5F5] text-gray-800 rounded-tl-none",
              isGhostMessage && "border-2"
            )}
          >
            <p className="whitespace-pre-wrap">
              {msg.message || ""}
              {isGhostMessage && showCursor && (
                <span className="inline-block w-2 h-4 bg-blue-500 ml-1 animate-pulse"></span>
              )}
            </p>
          </div>

          <div className={cn("text-sm text-gray-500 ")}>
            {msg.createdAt
              ? new Date(msg.createdAt).toLocaleString("en-US", {
                  month: "short",
                  day: "numeric",
                  hour: "numeric",
                  minute: "numeric",
                  hour12: true,
                })
              : "Just now"}
            {isGhostMessage && <span className="ml-2">(typing...)</span>}
          </div>

          {/* Always render charts if not a ghost message */}

          {!isGhostMessage && shouldRenderCharts && (
            <>
              {renderBarChart()}
              {renderScatterChart()}
              {renderLineChart()}
            </>
          )}

          {shouldRenderMap && (
            <div className="mt-[10px] w-[100%] md:w-[70%]">
              <div
                className="w-full  bg-gray-100  p-4 overflow-hidden relative rounded-lg shadow-sm border border-gray-100"
                style={{ height: "230px" }}
              >
                {memoizedMap}
              </div>
              <div className="mt-2 w-full">
                <div className="bg-[#5185FF] py-2 px-3 rounded-2xl flex justify-between items-center shadow-sm w-full border-1 border-[#FAFAFA]">
                  <div className="text-white font-medium text-sm">
                    {filteredEntities.length > 0 ? (
                      <>
                        {filteredEntities.length}
                        {filteredEntities.length >= 25 ? "+" : ""} doctor
                        {filteredEntities.length !== 1 ? "s" : ""} included
                      </>
                    ) : (
                      "No doctors found"
                    )}
                  </div>
                  <Link
                    href="/map"
                    className="text-white font-medium flex items-center hover:underline focus:outline-none rounded px-1 py-0.5 transition-all text-sm"
                    aria-label="View all doctors on map"
                  >
                    View all{" "}
                    <Image
                      src="/common/arrow-right.svg"
                      alt="View all"
                      width={24}
                      height={24}
                    />
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>

        {isCurrentUser && (
          <Avatar className="h-8 w-8 ">
            <AvatarImage src="/placeholder.svg?height=32&width=32" alt="User" />
            <AvatarFallback>
              {currentUserId && msg.userId === currentUserId && msg.user?.email
                ? msg.user.email.charAt(0).toUpperCase()
                : "U"}
            </AvatarFallback>
          </Avatar>
        )}
      </div>
    </div>
  );
};

export default Message;
