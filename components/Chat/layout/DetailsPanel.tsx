"use client";

import React from "react";
import { motion } from "framer-motion";
import { useMobile } from "@/hooks/use-mobile";
import {
  mobileDetailsVariants,
  detailsPanelVariants,
} from "../animations/LayoutAnimations";

interface DetailsPanelProps {
  children: React.ReactNode;
  mobilePanelHeight?: "standard" | "compact";
}

export const DetailsPanel = React.memo<DetailsPanelProps>(
  ({ children, mobilePanelHeight = "standard" }) => {
    const isMobile = useMobile();

    const animateState = React.useMemo(() => {
      if (isMobile) {
        return mobilePanelHeight === "compact" ? "newChat" : "visible";
      }
      return "visible";
    }, [isMobile, mobilePanelHeight]);

    const variants = React.useMemo(() => {
      return isMobile ? mobileDetailsVariants : detailsPanelVariants;
    }, [isMobile]);

    return (
      <motion.div
        className={`
        ${isMobile ? "w-full" : "h-full"} 
        overflow-auto bg-white
      `}
        variants={variants}
        initial="hidden"
        animate={animateState}
        exit="exit"
        transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
      >
        {children}
      </motion.div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.children === nextProps.children &&
      prevProps.mobilePanelHeight === nextProps.mobilePanelHeight
    );
  }
);

DetailsPanel.displayName = "DetailsPanel";
