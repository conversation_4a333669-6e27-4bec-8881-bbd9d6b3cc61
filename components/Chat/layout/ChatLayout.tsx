"use client";

import React from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useViewState } from "../context/ViewStateContext";
import { useMobile } from "@/hooks/use-mobile";
import {
  mainContentVariants,
  sidebarVariants,
  detailsPanelVariants,
} from "../animations/LayoutAnimations";

interface ChatLayoutProps {
  children: React.ReactNode;
  sidebar: React.ReactNode;
  detailsPanel: React.ReactNode;
}

/**
 * Optimized main layout component for the chat interface
 * Uses GPU-accelerated animations and minimal re-renders
 */
export const ChatLayout = React.memo<ChatLayoutProps>(
  ({ children, sidebar, detailsPanel }) => {
    const { activeView } = useViewState();
    const isMobile = useMobile();

    const { showSidebar, showMainContent, showDetailsPanel } = React.useMemo(
      () => ({
        showSidebar: !isMobile,

        showMainContent:
          !isMobile ||
          activeView === "CHAT_LIST" ||
          activeView === "CHAT_CONVERSATION" ||
          activeView === "CHAT_DETAILS" ||
          activeView === "USER_DETAILS" ||
          activeView === "NEW_CHAT" ||
          activeView === "CONFIRM_GROUP_CHAT",

        showDetailsPanel:
          !isMobile &&
          (activeView === "CHAT_DETAILS" ||
            activeView === "USER_DETAILS" ||
            activeView === "NEW_CHAT"),
      }),
      [isMobile, activeView]
    );

    const animationStates = React.useMemo(
      () => ({
        sidebarState: isMobile
          ? showSidebar
            ? "mobile"
            : "hidden"
          : "desktop",
        mainContentState: showDetailsPanel ? "withDetails" : "full",
        detailsPanelState: showDetailsPanel ? "visible" : "hidden",
      }),
      [isMobile, showSidebar, showDetailsPanel]
    );

    return (
      <div className="flex flex-col md:flex-row h-full w-full overflow-hidden">
        <AnimatePresence mode="wait">
          {showSidebar && (
            <motion.div
              className="w-full md:w-[400px] md:border-r border-gray-200 overflow-hidden"
              variants={sidebarVariants}
              initial="hidden"
              animate={animationStates.sidebarState}
              exit="hidden"
              transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
            >
              {sidebar}
            </motion.div>
          )}
        </AnimatePresence>

        <AnimatePresence mode="wait">
          {showMainContent && (
            <motion.div
              className="flex-1 overflow-hidden md:relative"
              variants={mainContentVariants}
              initial="full"
              animate={animationStates.mainContentState}
              exit="hidden"
              transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
            >
              {children}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Optimized Details Panel - Only visible on desktop */}
        {!isMobile && (
          <AnimatePresence mode="wait">
            {showDetailsPanel && (
              <motion.div
                className="border-l border-gray-200 overflow-hidden bg-white"
                variants={detailsPanelVariants}
                initial="hidden"
                animate={animationStates.detailsPanelState}
                exit="hidden"
                transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
              >
                {detailsPanel}
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </div>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.children === nextProps.children &&
      prevProps.sidebar === nextProps.sidebar &&
      prevProps.detailsPanel === nextProps.detailsPanel
    );
  }
);

ChatLayout.displayName = "ChatLayout";
