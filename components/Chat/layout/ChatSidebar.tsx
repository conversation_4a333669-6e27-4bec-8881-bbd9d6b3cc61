"use client";

import React from "react";
import { motion } from "framer-motion";
import { chatPanelVariants } from "../animations/LayoutAnimations";
import { useMobile } from "@/hooks/use-mobile";
import MobileHeader from "../MobileHeader";
import { ChatActionsMenu } from "@/components/Chats/ActionMenu";

export const ChatSidebar = React.memo<{ children: React.ReactNode }>(
  ({ children }) => {
    const isMobile = useMobile();

    const headerContent = React.useMemo(() => {
      if (!isMobile) return null;

      return (
        <MobileHeader
          showBackButton={false}
          leftContent={
            <div className="text-[24px] font-semibold text-[#171717]">
              Chats
            </div>
          }
          rightContent={
            <div className="flex items-center">
              <div className="p-[2px] bg-white rounded-2xl shadow-lg">
                <ChatActionsMenu />
              </div>
            </div>
          }
        />
      );
    }, [isMobile]);

    return (
      <motion.div
        className="flex flex-col h-full w-full overflow-hidden"
        variants={chatPanelVariants}
        initial="hidden"
        animate="visible"
        exit="hidden"
        transition={{ duration: 0.2, ease: [0.4, 0, 0.2, 1] }}
      >
        {headerContent}
        <div className="flex-1 overflow-hidden">{children}</div>
      </motion.div>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.children === nextProps.children;
  }
);

ChatSidebar.displayName = "ChatSidebar";
