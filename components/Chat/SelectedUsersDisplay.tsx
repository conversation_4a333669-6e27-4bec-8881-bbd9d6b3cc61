import { useContext, useEffect, useState } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChatContext } from "@/components/Chat/context/ChatContext";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";

interface User {
  userId: string;
  name?: string;
  email?: string;
  profilePicture?: string;
}

export const SelectedUsersAvatarList = ({ users }: { users?: User[] }) => {
  const { newChatData } = useContext(ChatContext);
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);

  useEffect(() => {
    // If users are provided as props, use those
    if (users && users.length > 0) {
      setSelectedUsers(users);
    } else if (newChatData?.selectedUserIds.length) {
      // Otherwise try to get them from the context
      // In a real implementation, you would fetch user details based on IDs
      // For now, we'll create placeholder users
      const contextUsers = newChatData.selectedUserIds.map((id) => ({
        userId: id,
        email: `user-${id.substring(0, 4)}@example.com`,
      }));
      setSelectedUsers(contextUsers);
    }
  }, [users, newChatData?.selectedUserIds]);

  if (!selectedUsers.length) {
    return null;
  }

  return (
    <div className="h-[72px] flex items-center px-4 ">
      <ScrollArea className="w-full whitespace-nowrap">
        <div className="flex space-x-4 p-1">
          {selectedUsers.map((user) => (
            <div key={user.userId} className="flex flex-col items-center">
              <Avatar className="h-10 w-10">
                <AvatarImage
                  src={user.profilePicture || "/Avatar.png"}
                  alt={user.name || user.email || "User"}
                />
                <AvatarFallback>
                  {(user.name || user.email || "U").charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
            </div>
          ))}
        </div>
        <ScrollBar orientation="horizontal" className="h-2" />
      </ScrollArea>
    </div>
  );
};
