import { useRouter, usePathname } from "next/navigation";
import { ReactNode } from "react";
import { ChevronLeftIcon } from "lucide-react";

interface MobileHeaderProps {
  leftContent?: ReactNode;
  centerContent?: ReactNode;
  rightContent?: ReactNode;
  showBackButton?: boolean;
  onBackClick?: () => void;
  className?: string;
}

const MobileHeader = ({
  leftContent,
  centerContent,
  rightContent,
  showBackButton = true,
  onBackClick,
  className = "",
}: MobileHeaderProps) => {
  const router = useRouter();
  const pathname = usePathname();
  
  const handleBack = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      router.back();
    }
  };

  return (
    <header
      className={`grid grid-cols-[auto_1fr_auto] items-center md:bg-white md:rounded-y-3xl w-full py-4 px-5 h-[84px] ${className}`}
    >
      <div className="flex items-center gap-2.5">
        {showBackButton && (
          <button
            onClick={handleBack}
            className="p-2 rounded-full"
            aria-label="Go back"
          >
            <ChevronLeftIcon className="w-6 h-6 text-[#929292]" />
          </button>
        )}
        {leftContent}
      </div>

      <div className="px-4 truncate">{centerContent}</div>

      <div className="flex items-center justify-end">
        {rightContent || <div className="w-6"></div>}
      </div>
    </header>
  );
};

export default MobileHeader;
