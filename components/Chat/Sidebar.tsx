"use client";

import * as React from "react";

import { Search, Plus, Trash2 } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import Image from "next/image";

import {
  Sidebar as UISidebar,
  SidebarContent,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { Chat } from "@/types/chat";

interface SidebarProps {
  chats: Chat[];
  currentChatId: string | null;
  onChatSelect: (chatId: string) => void;
  onNewChat: () => void;
  onDeleteChat: (chatId: string) => void;
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMoreChats: boolean;
  onLoadMore: () => void;
}

export default function Sidebar({
  chats,
  currentChatId,
  onChatSelect,
  onNewChat,
  onDeleteChat,
  isLoading,
  isLoadingMore,
  hasMoreChats,
  onLoadMore,
}: SidebarProps) {
  const observerRef = React.useRef<IntersectionObserver | null>(null);
  const lastChatRef = React.useRef<HTMLDivElement | null>(null);
  const [searchQuery, setSearchQuery] = React.useState("");

  const filteredChats = React.useMemo(() => {
    if (!searchQuery.trim()) return chats;

    const query = searchQuery.toLowerCase();
    return chats.filter((chat) =>
      (chat.name || "New Chat").toLowerCase().includes(query)
    );
  }, [searchQuery, chats]);

  const lastChatCallback = React.useCallback(
    (node: HTMLDivElement | null) => {
      if (isLoading) return;

      if (observerRef.current) {
        observerRef.current.disconnect();
      }

      observerRef.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting && hasMoreChats && !isLoadingMore) {
            onLoadMore();
          }
        },
        { threshold: 0.5 }
      );

      lastChatRef.current = node;

      if (node && hasMoreChats) {
        observerRef.current.observe(node);
      }
    },
    [hasMoreChats, isLoading, isLoadingMore, onLoadMore]
  );

  // Cleanup observer on unmount
  React.useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  return (
    <UISidebar
      side="right"
      className="border-none p-4 rounded-xl bg-transparent h-full overflow-hidden "
    >
      <div className="h-full  bg-white rounded-xl shadow-lg  flex flex-col overflow-hidden max-sm:rounded-r-none   ">
        <SidebarHeader className="px-4 py-3 border-b">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-medium text-gray-700">Chat History</h2>
          </div>
          <div className="mt-3 relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search"
              className="pl-9 rounded-full border-gray-300 bg-white"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <button
            onClick={onNewChat}
            className="w-full mt-3 py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center justify-center gap-2 transition-colors"
          >
            <Plus className="h-5 w-5" />
            <span>Create New Chat</span>
          </button>
        </SidebarHeader>

        <SidebarContent className="px-2 flex-1 overflow-auto">
          <div className="space-y-1 py-2">
            {isLoading && !isLoadingMore ? (
              <div className="py-4 px-2 flex justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : filteredChats.length > 0 ? (
              <>
                {filteredChats.map((chat, index) => (
                  <div
                    key={chat.id}
                    ref={
                      index === filteredChats.length - 1
                        ? lastChatCallback
                        : null
                    }
                  >
                    <ChatItem
                      chat={chat}
                      isActive={currentChatId === chat.id}
                      onSelect={() => onChatSelect(chat.id)}
                      onDelete={() => onDeleteChat(chat.id)}
                    />
                  </div>
                ))}
                {isLoadingMore && (
                  <div className="py-2 px-2 flex justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500"></div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-4 text-gray-500">
                No chats available
              </div>
            )}
          </div>
        </SidebarContent>
      </div>
    </UISidebar>
  );
}

interface ChatItemProps {
  chat: Chat;
  isActive: boolean;
  onSelect: () => void;
  onDelete: () => void;
}

function ChatItem({ chat, isActive, onSelect, onDelete }: ChatItemProps) {
  const title = chat.name || "New Chat";
  const timeAgo = chat.updatedAt
    ? formatDistanceToNow(new Date(chat.updatedAt), { addSuffix: true })
    : "Just now";

  return (
    <div
      className={cn(
        "flex w-full items-center gap-1 rounded-md px-2 py-2 hover:bg-gray-100",
        isActive ? "bg-gray-200 hover:bg-gray-200" : ""
      )}
    >
      <button
        onClick={onSelect}
        className="flex flex-1 items-center gap-3 text-left min-w-0"
      >
        <Image
          src="/chat.svg"
          alt="Chat"
          width={24}
          height={24}
          className="h-6 w-6 shrink-0"
        />
        <div className="min-w-0 flex-1">
          <p className="truncate text-sm font-medium text-gray-700">{title}</p>
          <p className="truncate text-xs text-gray-500">{timeAgo}</p>
        </div>
      </button>
      <button
        onClick={(e) => {
          e.stopPropagation();
          onDelete();
        }}
        className="p-1 text-gray-500 hover:text-red-600 transition-colors"
        title="Delete chat"
      >
        <Trash2 className="h-4 w-4" />
      </button>
    </div>
  );
}
