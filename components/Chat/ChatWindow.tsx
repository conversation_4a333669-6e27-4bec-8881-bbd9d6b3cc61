"use client";

import { useRef, useEffect, useState } from "react";
import Message from "./Message";
import MasageInput from "./MasageInput";
import { Skeleton } from "@/components/ui/skeleton";
import { useScrollManagement } from "@/hooks/useScrollManagement";
import Image from "next/image";
import SymptomHeatmap from "../diagrams/SymptomHeatmap";
import SingleLineChart from "../diagrams/SingleLineChart";
import BloodPressureChart from "../diagrams/BloodPressureChart";
import GlucoseChart from "../diagrams/GlucoseChart";
import MedicationTimeline from "../diagrams/MedicationTimeline";
import ReactionBarChart from "../diagrams/ReactionBarChart";

interface ChatWindowProps {
  messages: any[];
  isLoadingMessages: boolean;
  isSending: boolean;
  isStreaming: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  setObserverTarget?: (element: HTMLDivElement | null) => void;
  onSendMessage: (content: string) => Promise<void>;
  chatId: string | null;
  headerAction?: React.ReactNode;
  newMessageAdded: boolean;
  setNewMessageAdded: (value: boolean) => void;
  currentUserId: string | null;
  isInitialLoading?: boolean;
}

export default function ChatWindow({
  messages,
  isLoadingMessages,
  isSending,
  isStreaming,
  isLoadingMore,
  hasMore,
  setObserverTarget,
  onSendMessage,
  chatId,
  headerAction,
  newMessageAdded,
  setNewMessageAdded,
  currentUserId,
  isInitialLoading = false,
}: ChatWindowProps) {
  const observerTargetRef = useRef<HTMLDivElement | null>(null);
  const [prevFirstMessageId, setPrevFirstMessageId] = useState<string | null>(
    null
  );

  const {
    messageEndRef,
    scrollContainerRef,
    wasAtBottomRef,
    isFirstRenderRef,
    prevMessagesLengthRef,
    scrollToBottom,
    forceScrollToBottom,
    scrollToMessage,
  } = useScrollManagement();

  useEffect(() => {
    if (setObserverTarget) {
      setObserverTarget(observerTargetRef.current);
      return () => setObserverTarget(null);
    }
  }, [setObserverTarget]);

  useEffect(() => {
    if (!messages.length) return;

    const firstMessage = messages[0];
    const firstMessageId = firstMessage?.id;
    const messagesLoaded = messages.length > prevMessagesLengthRef.current;
    const isOlderMessagesLoaded =
      messagesLoaded && firstMessageId !== prevFirstMessageId;
    const isNewMessageAtEnd = messagesLoaded && !isOlderMessagesLoaded;

    if (firstMessageId !== prevFirstMessageId) {
      setPrevFirstMessageId(firstMessageId);
    }

    const hasSendingOrStreamingMessage = isSending || isStreaming;

    if (isFirstRenderRef.current && messages.length > 0) {
      forceScrollToBottom({ behavior: "auto", block: "end" });
      isFirstRenderRef.current = false;
    } else if (newMessageAdded) {
      forceScrollToBottom({ behavior: "smooth", block: "end" });
      setNewMessageAdded(false);
    } else if (
      (isNewMessageAtEnd || hasSendingOrStreamingMessage) &&
      wasAtBottomRef.current
    ) {
      scrollToBottom({ behavior: "smooth", block: "end" });
    } else if (isOlderMessagesLoaded && !isLoadingMessages) {
      if (prevFirstMessageId) {
        scrollToMessage(prevFirstMessageId);
      }
    }

    prevMessagesLengthRef.current = messages.length;
  }, [
    messages,
    newMessageAdded,
    setNewMessageAdded,
    prevFirstMessageId,
    isLoadingMessages,
    isSending,
    isStreaming,
    scrollToBottom,
    forceScrollToBottom,
    scrollToMessage,
    wasAtBottomRef,
    isFirstRenderRef,
    prevMessagesLengthRef,
  ]);

  useEffect(() => {
    if ((isStreaming && wasAtBottomRef.current) || isSending) {
      forceScrollToBottom({ behavior: "smooth", block: "end" });
    }
  }, [
    isSending,
    isStreaming,
    forceScrollToBottom,
    scrollToBottom,
    wasAtBottomRef,
  ]);

  const handleMicClick = (e: React.MouseEvent) => {
    e.preventDefault();
    // The microphone handling is now inside the MasageInput component
  };

  useEffect(() => {
    if (
      !isStreaming &&
      prevMessagesLengthRef.current > 0 &&
      messages.length > 0
    ) {
      const timer = setTimeout(() => {
        forceScrollToBottom({ behavior: "smooth", block: "end" });
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [
    isStreaming,
    messages.length,
    forceScrollToBottom,
    prevMessagesLengthRef,
  ]);

  const renderSkeletonMessages = () => {
    return (
      <>
        {/* AI message skeleton */}
        <div className="flex items-start gap-2.5 animate-pulse">
          <Skeleton className="h-8 w-8 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-24 w-[220px] rounded-xl rounded-tl-none" />
            <Skeleton className="h-3 w-20" />
          </div>
        </div>

        {/* User message skeleton */}
        <div className="flex flex-col w-full">
          <div className="flex items-start gap-2.5 animate-pulse ml-auto">
            <div className="space-y-2 flex flex-col items-end">
              <Skeleton className="h-16 w-[180px] rounded-xl rounded-tr-none" />
              <Skeleton className="h-3 w-20" />
            </div>
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>

        {/* AI message skeleton */}
        <div className="flex items-start gap-2.5 animate-pulse">
          <Skeleton className="h-8 w-8 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-28 w-[260px] rounded-xl rounded-tl-none" />
            <Skeleton className="h-3 w-20" />
          </div>
        </div>
      </>
    );
  };

  const renderMessages = () => {
    if (isInitialLoading) {
      return renderSkeletonMessages();
    }

    if (isLoadingMessages && messages.length === 0 && !isLoadingMore) {
      return (
        <div className="flex-grow flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      );
    }

    if (!chatId && !isInitialLoading) {
      return (
        <div className="flex-grow flex items-center justify-center text-center">
          <p className="text-gray-500">
            Select a chat or start a new one to begin messaging.
          </p>
        </div>
      );
    }

    if (messages.length === 0 && !isLoadingMessages && !isInitialLoading) {
      return (
        <div className="flex-grow flex items-center justify-center text-center">
          <p className="text-gray-500">
            Start the conversation by typing a message below.
          </p>
        </div>
      );
    }

    return messages.map((msg: any) => (
      <div key={msg.id} className="mb-6">
        {/* <SymptomHeatmap />
        <BloodPressureChart />
        <SingleLineChart />
        <GlucoseChart />
        <MedicationTimeline />
        <ReactionBarChart /> */}
        <Message msg={msg} currentUserId={currentUserId} withId={true} />
      </div>
    ));
  };

  return (
    <div className="w-full mx-auto rounded-xl md:pb-8 md-px-8 border-gray-200 flex flex-col h-full overflow-hidden bg-white relative">
      <div
        ref={scrollContainerRef}
        className="flex-1 flex justify-center overflow-y-auto scrollbar-hide pb-[32px] pt-6"
        data-testid="messages-container"
      >
        <div className="w-full max-w-[900px] md:px-[24px] space-y-[30px]">
          {isInitialLoading && (
            <div
              ref={observerTargetRef}
              className="w-full flex justify-center items-center min-h-[30px] mb-2 pt-1"
              data-testid="messages-observer"
            >
              {isLoadingMore ? (
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
              ) : hasMore ? (
                <div className="text-xs text-gray-400"></div>
              ) : (
                messages.length > 0 && (
                  <div className="text-xs text-gray-400"></div>
                )
              )}
            </div>
          )}

          {renderMessages()}

          <div ref={messageEndRef} data-message-end className="mb-4" />
        </div>
      </div>

      <div className="mt-auto md:px-0 md:pb-0 bg-transparent relative">
        {headerAction && (
          <div className="absolute -top-[42px] right-4 md:right-auto md:left-1/2 md:transform md:-translate-x-1/2 bg-transparent !bg-opacity-0 z-20 md:w-full md:flex md:justify-center">
            {headerAction}
          </div>
        )}
        <div className="flex items-center space-x-2 ">
          <div className="flex-grow">
            <MasageInput
              handleMicClick={handleMicClick}
              onSendMessage={onSendMessage}
              isDisabled={
                isSending ||
                isStreaming ||
                isLoadingMessages ||
                !chatId ||
                isInitialLoading
              }
            />
          </div>
        </div>
      </div>
    </div>
  );
}
