import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { X, Loader2 } from "lucide-react";
import { ActionButton } from "./ActionButton";
import { fetchAuthSession } from "aws-amplify/auth";
import {
  getFriendRequestStatus,
  sendFriendRequest,
  deleteContact,
} from "@/services/contactService";

interface ContactManagementButtonsProps {
  targetUserId: string;
  className?: string;
  onStatusChange?: () => void;
}

export const ContactManagementButtons = ({
  targetUserId,
  className = "",
  onStatusChange,
}: ContactManagementButtonsProps) => {
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [friendRequestStatus, setFriendRequestStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  useEffect(() => {
    const getCurrentUserId = async () => {
      try {
        const session = await fetchAuthSession();
        if (session.tokens?.idToken?.payload?.sub) {
          const userId = String(session.tokens.idToken.payload.sub);
          setCurrentUserId(userId);
        }
      } catch (error) {
        console.error("Error fetching current user:", error);
      }
    };

    getCurrentUserId();
  }, []);

  useEffect(() => {
    const fetchFriendStatus = async () => {
      if (currentUserId && targetUserId) {
        setIsInitialLoading(true);
        try {
          const status = await getFriendRequestStatus(
            currentUserId,
            targetUserId
          );
          setFriendRequestStatus(status);
        } catch (error) {
          console.error("Error fetching friend status:", error);
        } finally {
          setIsInitialLoading(false);
        }
      }
    };

    fetchFriendStatus();
  }, [currentUserId, targetUserId]);

  const handleSendFriendRequest = useCallback(async () => {
    if (targetUserId && currentUserId) {
      setIsLoading(true);
      try {
        const result = await sendFriendRequest(currentUserId, targetUserId);
        if (result.success && result.requestId) {
          setFriendRequestStatus({
            type: "sent",
            status: "PENDING",
            requestId: result.requestId,
          });
          if (onStatusChange) onStatusChange();
        }
      } catch (error) {
        console.error("Error sending friend request:", error);
      } finally {
        setIsLoading(false);
      }
    }
  }, [targetUserId, currentUserId, onStatusChange]);

  const handleDeleteContact = useCallback(async () => {
    if (targetUserId && currentUserId) {
      setIsLoading(true);
      try {
        const success = await deleteContact(currentUserId, targetUserId);
        if (success) {
          setFriendRequestStatus(null);
          if (onStatusChange) onStatusChange();
        }
      } catch (error) {
        console.error("Error deleting contact:", error);
      } finally {
        setIsLoading(false);
      }
    }
  }, [targetUserId, currentUserId, onStatusChange]);

  // Show loading button while we're determining the relationship status
  if (isInitialLoading) {
    return (
      <Button
        className={`bg-[#5185FF] hover:bg-[#4070ee] text-white rounded-[16px] px-4 py-3 w-full text-[16px] font-medium shadow-[0px_3px_6px_rgba(66,74,83,0.12)] ${className} flex items-center justify-center`}
        disabled={true}
      >
        <Loader2 className="w-5 h-5 animate-spin text-white" />
      </Button>
    );
  }

  // No relationship case
  if (!friendRequestStatus) {
    return (
      <Button
        className={`bg-[#5185FF] hover:bg-[#4070ee] text-white rounded-[16px] px-4 py-3 w-full text-[16px] font-medium shadow-[0px_3px_6px_rgba(66,74,83,0.12)] ${className} flex items-center justify-center`}
        onClick={handleSendFriendRequest}
        disabled={isLoading}
      >
        {isLoading ? (
          <Loader2 className="w-5 h-5 animate-spin text-white" />
        ) : (
          <span>Add Contact</span>
        )}
      </Button>
    );
  }

  // Pending request sent by current user
  if (
    friendRequestStatus.type === "sent" &&
    friendRequestStatus.status === "PENDING"
  ) {
    return (
      <Button
        className={`bg-gray-300 text-gray-700 rounded-[16px] cursor-default w-full py-3 text-[16px] font-medium ${className} flex items-center justify-center`}
        disabled
      >
        {isLoading ? (
          <Loader2 className="w-5 h-5 animate-spin text-gray-700" />
        ) : (
          <span>Request Sent</span>
        )}
      </Button>
    );
  }

  // Accepted (friends)
  if (
    (friendRequestStatus.type === "sent" &&
      friendRequestStatus.status === "ACCEPTED") ||
    (friendRequestStatus.type === "received" &&
      friendRequestStatus.status === "ACCEPTED")
  ) {
    return (
      <ActionButton
        icon={X}
        text="Delete Contact"
        onConfirm={handleDeleteContact}
        confirmationTitle="Delete Contact"
        confirmationMessage="Are you sure you want to delete this contact? This action cannot be undone."
        confirmationButtonText="Delete Contact"
        disabled={isLoading}
        className={className}
        isLoading={isLoading}
      />
    );
  }

  // Default (should not reach here in normal operation)
  return null;
};
