"use client";

import { ReactNode } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

import PageHeader from "@/components/ui/PageHeader";
import { X } from "lucide-react";

interface ConfirmationDialogProps {
  children: ReactNode;
  title?: string;
  message?: string;
  confirmButtonText?: string;
  cancelButtonText?: string;
  confirmButtonVariant?: "default" | "destructive" | "outline" | "ghost";
  onConfirm: () => void;
}

export default function ConfirmationDialog({
  children,
  title = "Confirm Action",
  message = "Are you sure you want to proceed with this action?",
  confirmButtonText = "Confirm",
  cancelButtonText = "Cancel",

  onConfirm,
}: ConfirmationDialogProps) {
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>{children}</AlertDialogTrigger>
      <AlertDialogContent className="max-w-[300px] rounded-lg p-0 overflow-hidden">
        <PageHeader
          title={<span className="font-semibold">{title}</span>}
          rightContent={
            <AlertDialogCancel className="p-0 m-0 h-auto bg-transparent border-none hover:bg-transparent">
              <X size={18} className="text-gray-500" />
            </AlertDialogCancel>
          }
          background="transparent"
          className="border-b border-gray-200 py-2"
          sticky={false}
        />

        <div className="p-4">
          <p className="text-sm text-center text-gray-700 mb-4">{message}</p>

          <div className="flex flex-col gap-2">
            <AlertDialogAction
              className="w-full bg-red-500 hover:bg-red-600 text-white rounded-full py-3"
              onClick={onConfirm}
            >
              {confirmButtonText}
            </AlertDialogAction>
            <AlertDialogCancel className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-full py-3">
              {cancelButtonText}
            </AlertDialogCancel>
          </div>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}
