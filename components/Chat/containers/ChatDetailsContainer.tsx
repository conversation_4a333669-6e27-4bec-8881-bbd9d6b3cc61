"use client";

import { useContext } from "react";
import { ChatContext } from "@/components/Chat/context/ChatContext";
import { ActionButton } from "@/components/Chat/common/ActionButton";
import { X } from "lucide-react";
import { useState } from "react";
import { ContactManagementButtons } from "@/components/Chat/common/ContactManagementButtons";
import { useUser } from "@/hooks/useUsers";
import { Chat } from "@/types/chat";

interface ChatDetailsProps {
  currentChat: Chat | null;
  handleLeaveGroup: () => Promise<void>;
}

/**
 * A container component to display chat details for a user or group
 */
export const ChatDetailsContainer = ({
  currentChat,
  handleLeaveGroup,
}: ChatDetailsProps) => {
  const { currentUser, loading: isLoadingUser } = useUser();
  const [refreshKey, setRefreshKey] = useState(0);
  const { selectedUserDetail, showChatDetails } = useContext(ChatContext);
  const currentUserId = currentUser?.userId || null;

  const handleStatusChange = () => {
    setRefreshKey((prev) => prev + 1);
  };

  if (!showChatDetails || (!currentChat && !selectedUserDetail)) {
    return null;
  }

  const directChatMember =
    !selectedUserDetail &&
    currentChat?.chatType === "DIRECT" &&
    currentChat.chatParticipants
      ? currentChat.chatParticipants.find(
          (member) => member.userId !== currentUserId
        )
      : null;

  return (
    <div className="flex h-full flex-col justify-between p-[20px] pt-6 ">
      {selectedUserDetail ? (
        <>
          <div className="flex flex-col h-full bg-white justify-start">
            <div className="flex flex-row justify-between gap-[30px] py-2">
              <div className="flex flex-col gap-1 w-[100px]">
                <span className="text-[14px] font-medium text-[#929292] whitespace-nowrap">
                  User Email
                </span>
              </div>
              <div className="flex flex-col gap-1 flex-1">
                <span className="text-[14px] font-medium text-[#171717]">
                  {selectedUserDetail.email ||
                    `User ID: ${selectedUserDetail.id}`}
                </span>
              </div>
            </div>
            {/* Check if selectedUserDetail exists and has a valid ID to use */}
            {selectedUserDetail &&
              (selectedUserDetail.id || selectedUserDetail.userId) &&
              (selectedUserDetail.id || selectedUserDetail.userId) !==
                currentUserId && (
                <div className="mt-[10px] w-full">
                  <ContactManagementButtons
                    key={`contact-buttons-${selectedUserDetail.id || selectedUserDetail.userId}-${refreshKey}`}
                    targetUserId={
                      selectedUserDetail.id || selectedUserDetail.userId || ""
                    }
                    onStatusChange={handleStatusChange}
                  />
                </div>
              )}
          </div>
        </>
      ) : currentChat?.chatType === "GROUP" ? (
        <>
          <div className="flex justify-between items-center">
            <p className="flex-shrink-0 whitespace-nowrap">Group Name</p>
            <h2 className="text-lg font-semibold text-[#171717] truncate">
              {currentChat.name}
            </h2>
          </div>
          <div className="border-gray-200 px-5 py-2 w-full bg-white mt-3">
            <ActionButton
              icon={X}
              text="Leave Group"
              onConfirm={handleLeaveGroup}
              confirmationTitle="Leave Group"
              confirmationMessage="Are you sure you want to leave this group? You won't receive messages from this group anymore."
              confirmationButtonText="Leave Group"
            />
          </div>
        </>
      ) : (
        currentChat?.chatType === "DIRECT" &&
        directChatMember &&
        directChatMember.user && (
          <>
            <div className="flex flex-col bg-white justify-center">
              <div className="flex flex-row justify-between gap-[30px] py-2">
                <div className="flex flex-col gap-1 w-[100px]">
                  <span className="text-[14px] font-medium text-[#929292] whitespace-nowrap">
                    User Email
                  </span>
                </div>
                <div className="flex flex-col gap-1 flex-1">
                  <span className="text-[14px] font-medium text-[#171717]">
                    {directChatMember.user.email ||
                      `User ID: ${directChatMember.userId}`}
                  </span>
                </div>
              </div>
              <div className="mt-[10px] w-full">
                <ContactManagementButtons
                  key={`contact-buttons-${directChatMember.userId}-${refreshKey}`}
                  targetUserId={directChatMember.userId}
                  onStatusChange={handleStatusChange}
                />
              </div>
            </div>
          </>
        )
      )}
    </div>
  );
};
