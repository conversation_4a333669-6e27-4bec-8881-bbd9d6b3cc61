"use client";

import { useViewState } from "@/components/Chat/context/ViewStateContext";
import ConfirmNewGroupChat from "@/components/NewChat/ConfirmNewGroupChat";

/**
 * A container component to display the Confirm New Group Chat functionality
 * Works with both the old ChatContext and the new ViewState approach for backward compatibility
 */
export const ConfirmNewGroupChatContainer = () => {
  const { isViewActive, newChatData, setActiveView } = useViewState();

  const shouldShowConfirmGroupChat = isViewActive("CONFIRM_GROUP_CHAT");

  if (!shouldShowConfirmGroupChat) {
    return null;
  }

  const handleClose = () => {
    setActiveView("NEW_CHAT");
  };

  console.log("ConfirmNewGroupChatContainer - newChatData:", newChatData);

  return (
    <div className="flex h-full flex-col">
      <ConfirmNewGroupChat
        selectedCount={newChatData?.selectedUserIds?.length}
        onConfirm={newChatData?.handleConfirmCreateChat}
        onClose={handleClose}
        isLoading={newChatData?.isCreatingChat}
      />
    </div>
  );
};
