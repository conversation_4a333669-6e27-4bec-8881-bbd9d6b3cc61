"use client";

import { motion, AnimatePresence } from "framer-motion";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { useViewState } from "@/components/Chat/context/ViewStateContext";

// Variants for the user details panel
const userDetailsVariants = {
  hidden: {
    opacity: 0,
    flex: "0 0 0%",
    y: 50,
  },
  visible: {
    opacity: 1,
    flex: "0 0 100%",
    y: 0,
    transition: { duration: 0.3, ease: "easeInOut" },
  },
  exit: {
    opacity: 0,
    flex: "0 0 0%",
    y: 50,
    transition: { duration: 0.3, ease: "easeInOut" },
  },
};

/**
 * Component specifically for displaying individual user details
 */
export const UserDetailsContainer = () => {
  const { selectedUserDetail } = useViewState();

  if (!selectedUserDetail) {
    return null;
  }

  return (
    <AnimatePresence>
      <motion.div
        className="flex-1 flex flex-col overflow-y-auto p-4"
        variants={userDetailsVariants}
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        <div className="flex flex-col items-center mb-4 mt-2">
          <div className="w-[120px] h-[120px] relative flex items-center justify-center mb-4">
            <div className="absolute w-full h-full rounded-full bg-[#2567FF57] opacity-20"></div>
            <div className="absolute w-[80%] h-[80%] rounded-full bg-[#2567FF24] opacity-30"></div>
            <div className="absolute w-[60%] h-[60%] rounded-full bg-[#2567FF14] opacity-40"></div>
            <Avatar className="relative z-10 w-[80px] h-[80px] border-2 border-white">
              <AvatarImage
                src={selectedUserDetail.profilePicture || "/Avatar.png"}
                alt={selectedUserDetail.email || "User"}
              />
              <AvatarFallback>
                {selectedUserDetail.email?.charAt(0)?.toUpperCase() || "U"}
              </AvatarFallback>
            </Avatar>
          </div>
          <h2 className="text-xl font-semibold mb-1">
            {selectedUserDetail.email || `User ${selectedUserDetail.id}`}
          </h2>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};
