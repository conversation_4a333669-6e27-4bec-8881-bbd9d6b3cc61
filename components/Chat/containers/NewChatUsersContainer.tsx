"use client";

import { motion, AnimatePresence } from "framer-motion";
import { useContext } from "react";
import {
  ChatContext,
  ChatContextType,
} from "@/components/Chat/context/ChatContext";
import { SelectedUsersAvatarList } from "../SelectedUsersDisplay";

// Variants for the new chat panel
const newChatVariants = {
  hidden: {
    opacity: 0,
    flex: "0 0 0%",
    y: 50,
  },
  visible: {
    opacity: 1,
    flex: "0 0 100%",
    y: 0,
    transition: { duration: 0.3, ease: "easeInOut" },
  },
  exit: {
    opacity: 0,
    flex: "0 0 0%",
    y: 50,
    transition: { duration: 0.3, ease: "easeInOut" },
  },
};

/**
 * Component specifically for displaying new chat creation UI
 */
export const NewChatUsersContainer = () => {
  const { newChatData } = useContext(ChatContext) as ChatContextType;

  if (!newChatData) {
    return null;
  }

  return (
    <AnimatePresence>
      {/* Show the selected users avatar list when newChatData exists and has selected users */}
      {newChatData.selectedUserIds &&
        newChatData.selectedUserIds.length > 0 && <SelectedUsersAvatarList />}

      {/* Show message when in new chat mode but no users selected */}
      {(!newChatData.selectedUserIds ||
        newChatData.selectedUserIds.length === 0) && (
        <motion.div
          className="flex-1 flex flex-col items-center justify-center px-4"
          variants={newChatVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
        >
          <h3 className="text-base font-medium text-[#7DA4FF]">
            No users selected
          </h3>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
