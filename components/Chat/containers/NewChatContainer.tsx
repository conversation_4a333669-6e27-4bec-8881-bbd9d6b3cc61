"use client";

import { useContext } from "react";
import { ChatContext } from "@/components/Chat/context/ChatContext";
import { useViewState } from "@/components/Chat/context/ViewStateContext";
import NewChatList from "@/components/NewChat/NewChatList";

/**
 * A container component to display the New Chat functionality
 * Works with both the old ChatContext and the new ViewState approach for backward compatibility
 */
export const NewChatContainer = () => {
  // Using both contexts for compatibility during migration
  const { showNewChatPage } = useContext(ChatContext);
  const { isViewActive } = useViewState();

  // Check if we should show the new chat page with either approach
  const shouldShowNewChat = showNewChatPage || isViewActive("NEW_CHAT");

  if (!shouldShowNewChat) {
    return null;
  }

  return (
    <div className="flex h-full flex-col">
      <NewChatList hideHeader={true} />
    </div>
  );
};
