"use client";

import React from "react";
import { useViewState } from "@/components/Chat/context/ViewStateContext";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useMobile } from "@/hooks/use-mobile";

/**
 * Example of a migrated component using the ViewState context
 * instead of the old ChatContext
 */
export const UserProfileView = () => {
  const { selectedUserDetail, setSelectedUserDetail, setActiveView } =
    useViewState();
  const isMobile = useMobile();

  // Handle back button click - go back to chat details
  const handleBackClick = () => {
    setSelectedUserDetail(null);
    setActiveView("CHAT_DETAILS");
  };

  // Handle chat button click - start a conversation with the user
  const handleChatClick = () => {
    // Logic to start a conversation would go here
    setActiveView("CHAT_CONVERSATION");
  };

  if (!selectedUserDetail) {
    return null;
  }

  return (
    <div className="flex flex-col h-full w-full p-4">
      <div className="flex flex-col items-center mb-6">
        <Avatar className="w-24 h-24 mb-4">
          <AvatarImage
            src={selectedUserDetail.profilePicture || "/Avatar.png"}
            alt={selectedUserDetail.email || "User"}
          />
          <AvatarFallback>
            {selectedUserDetail.email?.charAt(0)?.toUpperCase() || "U"}
          </AvatarFallback>
        </Avatar>

        <h2 className="text-xl font-bold mb-1">
          {selectedUserDetail.email || `User ${selectedUserDetail.id}`}
        </h2>
      </div>

      <div className="flex flex-col gap-4">
        <Button variant="default" className="w-full" onClick={handleChatClick}>
          Start Conversation
        </Button>

        {isMobile && (
          <Button
            variant="outline"
            className="w-full"
            onClick={handleBackClick}
          >
            Back to Details
          </Button>
        )}
      </div>
    </div>
  );
};
