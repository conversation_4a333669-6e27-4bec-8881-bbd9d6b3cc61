"use client";

import { useCallback } from "react";
import { useRouter } from "next/navigation";
import { useMemo } from "react";
import { AI_ASSISTANT_ID } from "@/constants/chat";
import { useUser } from "@/hooks/useUsers";
import { useCurrentChat } from "../context/ChatContext";
import { ChatParticipant, ChatParticipantFormatted } from "@/types/chat";
import ChatHeader from "../ChatHeader";

interface ChatHeaderWrapperProps {
  onGroupDetailsClick?: () => void;
  handleLeaveGroup?: () => Promise<void>;
}

export const ChatHeaderWrapper = ({
  onGroupDetailsClick,
  handleLeaveGroup,
}: ChatHeaderWrapperProps) => {
  const router = useRouter();
  const { currentChat } = useCurrentChat();
  const { currentUser } = useUser();
  const userId = currentUser?.userId || null;

  const chatData = useMemo(() => {
    if (!currentChat) return null;
    const { id, name, chatType, chatParticipants } = currentChat;

    const defaultMembers: ChatParticipantFormatted[] = [];

    const members: ChatParticipantFormatted[] =
      chatParticipants && userId
        ? chatParticipants
            .filter(
              (p) =>
                p.userId !== userId && // exclude current user
                p.userId !== AI_ASSISTANT_ID // exclude AI assistant
            )
            .map((p) => {
              return {
                userId: p.userId,
                user: p.user,
              };
            })
        : defaultMembers;

    return {
      id,
      name,
      type: chatType,
      members,
      isGroup: chatType === "GROUP",
    };
  }, [currentChat, userId]);

  const handleBackClick = useCallback(() => {
    router.push("/chats");
  }, [router]);

  if (!chatData) {
    return null;
  }

  return (
    <ChatHeader
      chatData={chatData}
      onBackClick={handleBackClick}
      onGroupDetailsClick={onGroupDetailsClick}
      handleLeaveGroup={handleLeaveGroup}
    />
  );
};
