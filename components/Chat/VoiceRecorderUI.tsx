import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';

interface IWindow extends Window {
    webkitSpeechRecognition: any;
    SpeechRecognition: any;
}

interface VoiceRecorderUIProps {
    onCancel: () => void;
    onSend: (text: string) => void;
    onTransform: () => void;
}

const VoiceRecorderUI: React.FC<VoiceRecorderUIProps> = ({ onCancel, onSend, onTransform }) => {
    const [transcribedText, setTranscribedText] = React.useState<string | null>(null);
    const [isRecording, setIsRecording] = useState(false);
    const [micPermission, setMicPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');
    const [isRequestingPermission, setIsRequestingPermission] = useState(false);
    const [showTranscribedText, setShowTranscribedText] = useState(false);

    const recognitionRef = useRef<any>(null);
    const permissionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
 
    useEffect(() => {
        const checkMicrophonePermission = async () => {
            try {
                if (navigator.permissions && navigator.permissions.query) {
                    const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });
                    
                    console.log('Initial microphone permission state:', permissionStatus.state);
                    
                    if (permissionStatus.state === 'granted') {
                        setMicPermission('granted');
                    } else if (permissionStatus.state === 'denied') {
                        setMicPermission('denied');
                    } else {
                        setMicPermission('prompt');
                    }
                    
                    permissionStatus.onchange = () => {
                        console.log('Permission status changed to:', permissionStatus.state);
                        if (permissionStatus.state === 'granted') {
                            setMicPermission('granted');
                        } else if (permissionStatus.state === 'denied') {
                            setMicPermission('denied');
                        }
                    };
                } else {
                    try {
                        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                        stream.getTracks().forEach(track => track.stop());
                        setMicPermission('granted');
                    } catch (err) {
                        console.error("Microphone access error during initial check:", err);
                        setMicPermission('prompt');
                    }
                }
            } catch (err) {
                console.error("Error checking microphone permission:", err);
            }
        };
        
        checkMicrophonePermission();
        
        return () => {
            if (permissionTimeoutRef.current) {
                clearTimeout(permissionTimeoutRef.current);
            }
        };
    }, []);

    useEffect(() => {
        console.log('Current microphone permission state:', micPermission);
    }, [micPermission]);
    
    useEffect(() => {
        
        const windowWithSpeech = window as unknown as IWindow;
        const SpeechRecognition = windowWithSpeech.SpeechRecognition || windowWithSpeech.webkitSpeechRecognition;
        
        if (!SpeechRecognition) {
            console.error("Speech recognition not supported in this browser");
            return;
        }
        
        const recognition = new SpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.lang = 'en-US';
        
        recognition.onresult = (event: any) => {
            const results = event.results;
            let finalTranscript = '';
            
            for (let i = 0; i < results.length; i++) {
                const transcript = results[i][0].transcript;
                if (results[i].isFinal) {
                    finalTranscript += transcript + ' ';
                } else {
                    setTranscribedText(transcript);
                }
            }
            
            if (finalTranscript) {
                setTranscribedText(finalTranscript);
            }
        };
        
        recognition.onstart = () => {
            console.log("Speech recognition started");
            setIsRecording(true);
            setMicPermission('granted');
        };
        
        recognition.onend = () => {
            console.log("Speech recognition ended");
        };
        
        recognition.onerror = (event: any) => {
            console.error("Speech recognition error", event.error);
            if (event.error === 'not-allowed') {
                setMicPermission('denied');
            }
            setIsRecording(false);
            setIsRequestingPermission(false);
        };
        
        recognitionRef.current = recognition;
        
        return () => {
            if (recognitionRef.current) {
                try {
                    recognitionRef.current.stop();
                } catch (e) {
                }
            }
        };
    }, []);
    
    const requestMicrophonePermission = async () => {
        setIsRequestingPermission(true);
        
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            
            setMicPermission('granted');
            setIsRequestingPermission(false);
            
            startRecording();
            
            stream.getTracks().forEach(track => track.stop());
            
            return true;
        } catch (err) {
            console.error("Microphone access error:", err);
            setMicPermission('denied');
            setIsRequestingPermission(false);
            return false;
        } finally {
            if (permissionTimeoutRef.current) {
                clearTimeout(permissionTimeoutRef.current);
            }
            permissionTimeoutRef.current = setTimeout(() => {
                setIsRequestingPermission(false);
            }, 5000);
        }
    };
    
    const startRecording = async () => {
        if (recognitionRef.current) {
            try {
                recognitionRef.current.start();
                setIsRecording(true);
                setTranscribedText("");
                setShowTranscribedText(false);
            } catch (e) {
                console.error("Error starting recording:", e);
            }
        }
    };
    
    const stopRecording = () => {
        if (recognitionRef.current && isRecording) {
            try {
                recognitionRef.current.stop();
                setIsRecording(false);
            } catch (e) {
                console.error("Error stopping recording:", e);
            }
        }
    };
    
    const toggleRecording = async (e: React.MouseEvent) => {
        e.stopPropagation();
        
        if (isRecording) {
            stopRecording();
            if (transcribedText) {
                onSend(transcribedText);
            }
        } else {
            if (micPermission !== 'granted') {
                await requestMicrophonePermission();
            } else {
                startRecording();
            }
        }
    };

    const handleRequestPermission = (e: React.MouseEvent) => {
        e.stopPropagation();
        requestMicrophonePermission();
    };

    const handleDragStart = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
    };
    
    const handleTransform = (e: React.MouseEvent) => {
        e.stopPropagation();
        setShowTranscribedText(prev => !prev);
        onTransform();
    };
    
    const handleCancel = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (isRecording) {
            stopRecording();
        }
        onCancel();
    };

    const renderPermissionUI = () => {
        console.log('Rendering permission UI with state:', micPermission, 'requesting:', isRequestingPermission);
        
        if (micPermission === 'denied') {
            return (
                <div className="text-center">
                    <p className="text-white mb-4">Microphone access blocked.</p>
                    <p className="text-white text-sm mb-8">Please allow microphone access in your browser settings and try again.</p>
                </div>
            );
        }
        
        if (micPermission === 'prompt' || isRequestingPermission) {
            return (
                <div className="text-center">
                    <p className="text-white mb-4">Microphone access required</p>
                    <button
                        onClick={handleRequestPermission}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg mb-8 transition-colors"
                        disabled={isRequestingPermission}
                    >
                        {isRequestingPermission ? 'Waiting for permission...' : 'Allow microphone access'}
                    </button>
                </div>
            );
        }
        
        return null;
    };

    return (
        <div 
            className="fixed inset-0 bg-[#00000099] backdrop-blur-sm flex flex-col items-center justify-end z-50"
            onClick={(e) => e.stopPropagation()}
        >
            <div className="flex flex-col items-center w-full px-4">
                {(micPermission !== 'granted' || isRequestingPermission) ? (
                    renderPermissionUI()
                ) : (
                    <>
                        <div className={`w-full max-w-md rounded-lg p-4 mb-[134px] flex flex-col items-center relative transition-colors duration-150 ${isRecording ? 'bg-red-200' : 'bg-blue-200'}`}>
                            {transcribedText && showTranscribedText && (
                                <p className="text-sm text-gray-800 mb-2 text-left w-full">{transcribedText}</p>
                            )}
                            {!transcribedText && isRecording && (
                                <p className="text-sm text-gray-500 mb-2 text-left w-full">Listening... say something</p>
                            )}
                            <div className="w-full h-10 flex items-center justify-center">
                                {isRecording ? (
                                    <div className="flex items-center space-x-1">
                                        {[...Array(10)].map((_, i) => (
                                            <div 
                                                key={i}
                                                className="bg-red-500 w-1 rounded-full animate-pulse"
                                                style={{
                                                    height: `${Math.random() * 20 + 10}px`,
                                                    animationDelay: `${i * 0.1}s`
                                                }}
                                            ></div>
                                        ))}
                                    </div>
                                ) : (
                                    <span className="text-blue-600 text-xs">
                                        || | | |||| | | || | |||| | | ||
                                    </span>
                                )}
                            </div>
                        </div>

                        <div className="flex justify-between items-center w-full max-w-md mb-4">
                            <button
                                onClick={handleCancel}
                                className="flex flex-col items-center text-white text-xs"
                                aria-label="Cancel recording"
                            >
                                <div className="bg-[#FFFFFF4D] border border-[#D4D4D4] rounded-full p-2 mb-1 h-12 w-12 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-white">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
                                    </svg>
                                </div>
                                Cancel
                            </button>

                            <button
                                onClick={handleTransform}
                                className={`flex flex-col items-center ${showTranscribedText ? 'text-black' : 'text-white'} text-xs`}
                                aria-label="Transform speech to text"
                                disabled={!transcribedText}
                            >
                                <div className={`${showTranscribedText ? 'bg-white' : 'bg-[#FFFFFF4D]'} border border-[#D4D4D4] rounded-full p-2 mb-1 h-12 w-12 flex items-center justify-center ${!transcribedText ? 'opacity-50' : ''}`}>
                                    <span className={`font-bold text-xl ${showTranscribedText ? 'text-black' : 'text-white'}`}>Aa</span>
                                </div>
                                {showTranscribedText ? 'Hide Text' : 'Show Text'}
                            </button>
                        </div>

                        <p className="text-white text-sm mb-4" aria-label="Microphone status">
                            {isRecording 
                                ? 'Recording... Press the microphone to stop' 
                                : 'Press the microphone to start recording'}
                        </p>
                    </>
                )}
            </div>

            <button
                type="button"
                onClick={toggleRecording}
                onDragStart={handleDragStart}
                className={`w-full h-20 rounded-t-full flex items-center justify-center relative focus:outline-none pt-4 transition-all duration-150 ${
                    isRecording
                        ? 'bg-[#F5F5F5] shadow-[inset_0_4px_0_0_#BFBFBF40]' 
                        : 'bg-gradient-to-t from-gray-500 to-white shadow-[inset_0_4px_0_0_rgba(255,255,255,0.25)]'
                }`}
                aria-label={isRecording ? "Stop recording" : "Start recording"}
                disabled={micPermission === 'denied' || isRequestingPermission}
            >
                <Image
                    src={micPermission === 'denied' ? '/microfon-disabled.svg' : '/microfon.svg'}
                    height={32}
                    width={32}
                    alt={micPermission === 'denied' ? "Microphone access denied" : "Microphone"}
                    draggable="false"
                />
               
            </button>
        </div>
    );
};

export default VoiceRecorderUI;
