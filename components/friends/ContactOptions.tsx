import { Button } from "@/components/ui/button";
import Image from "next/image";

type ContactOptionsProps = {
  pendingRequestCount: number;
  onClick: () => void;
};

export default function ContactOptions({ pendingRequestCount, onClick }: ContactOptionsProps) {
  return (
    <div className="mt-3 bg-white">
      <Button 
        variant="ghost" 
        className="w-full flex justify-start items-center py-8 px-[30px] relative"
        onClick={onClick}
      >
        <div className="w-6 h-6 flex items-center justify-center rounded-md">
          <Image src="/blueuser.svg" alt="Contacts" width={24} height={24} />
        </div>
        <span className="text-base font-medium ml-1">New Friends</span>
        
        {/* Notification badge for pending requests */}
        {pendingRequestCount > 0 && (
          <div className="absolute right-5 w-6 h-6 flex items-center justify-center bg-red-500 text-white rounded-full text-xs">
            {pendingRequestCount}
          </div>
        )}
      </Button>
    </div>
  );
}
