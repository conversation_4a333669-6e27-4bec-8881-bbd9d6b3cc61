import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useState } from "react";
import ContactRequestConfirmation from "./ContactRequestConfirmation";

type UserListProps = {
  users: any[];
  onUserClick: (user: any) => void;
  onSendFriendRequest: (userId: string) => Promise<void>;
  onRespondToFriendRequest: (
    requestId: string,
    status: "ACCEPTED" | "DECLINED"
  ) => Promise<void>;
};

export default function UserList({
  users,
  onUserClick,
  onSendFriendRequest,
  onRespondToFriendRequest,
}: UserListProps) {
  const [showConfirmation, setShowConfirmation] = useState(false);

  const handleSendRequest = async (userId: string) => {
    await onSendFriendRequest(userId);
    setShowConfirmation(true);
  };

  if (users.length === 0) return null;

  return (
    <div className="px-4 mt-4">
      <div className="bg-white rounded-lg overflow-hidden">
        {users.map((user) => (
          <div key={user.userId} className="p-4  flex items-center">
            <div className="h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center mr-3 overflow-hidden">
              <Image src="/Avatar.png" alt={user.name} width={40} height={40} />
            </div>
            <span
              className="font-medium flex-1 cursor-pointer truncate"
              onClick={() => onUserClick(user)}
            >
              {user.email}
            </span>

            {/* Add button for users with no request */}
            {!user.friendRequestStatus && (
              <Button
                className="bg-blue-500 hover:bg-blue-600 text-white rounded-full"
                onClick={() => handleSendRequest(user.userId)}
              >
                Add
              </Button>
            )}

            {/* Request sent status */}
            {user.friendRequestStatus?.type === "sent" &&
              user.friendRequestStatus.status === "PENDING" && (
                <Button
                  variant="ghost"
                  className="text-gray-700 p-0 cursor-default"
                  disabled
                >
                  Request Sent
                </Button>
              )}

            {/* Added status for accepted requests */}
            {((user.friendRequestStatus?.type === "sent" &&
              user.friendRequestStatus.status === "ACCEPTED") ||
              (user.friendRequestStatus?.type === "received" &&
                user.friendRequestStatus.status === "ACCEPTED")) && (
              <Button
                variant="ghost"
                className="text-gray-700 p-0 cursor-default"
                disabled
              >
                Added
              </Button>
            )}

            {/* Accept/Decline buttons for pending received requests */}
            {user.friendRequestStatus?.type === "received" &&
              user.friendRequestStatus.status === "PENDING" && (
                <div className="flex space-x-4">
                  <Button
                    variant="ghost"
                    className="text-blue-500 hover:text-blue-600 hover:bg-transparent p-0"
                    onClick={() =>
                      onRespondToFriendRequest(
                        user.friendRequestStatus.requestId,
                        "ACCEPTED"
                      )
                    }
                  >
                    Add
                  </Button>
                  <Button
                    variant="ghost"
                    className="text-red-500 hover:text-red-600 hover:bg-transparent p-0"
                    onClick={() =>
                      onRespondToFriendRequest(
                        user.friendRequestStatus.requestId,
                        "DECLINED"
                      )
                    }
                  >
                    Reject
                  </Button>
                </div>
              )}
          </div>
        ))}
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <ContactRequestConfirmation
          onClose={() => setShowConfirmation(false)}
        />
      )}
    </div>
  );
}
