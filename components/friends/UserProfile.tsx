import { Button } from "@/components/ui/button";
import Image from "next/image";
import { useState } from "react";
import ContactRequestConfirmation from "./ContactRequestConfirmation";

type UserProfileProps = {
  user: any;
  onClose: () => void;
  onSendFriendRequest: (userId: string) => Promise<void>;
};

export default function UserProfile({ user, onClose, onSendFriendRequest }: UserProfileProps) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [requestSent, setRequestSent] = useState(false);
  
  const handleSendRequest = async (userId: string) => {
    await onSendFriendRequest(userId);
    setRequestSent(true);
    setShowConfirmation(true);
  };
  
  return (
    <div className="flex flex-col h-screen bg-[#E9F0FF] backdrop-blur-md z-50 relative">
      {/* User Profile Header */}
      <div className="p-4 flex items-center">
        <Button
          variant="ghost"
          size="icon"
          onClick={onClose}
          className="mr-4"
        >
          <Image src="/chats/arrow-left.svg" alt="Back" width={24} height={24} />
        </Button>
      </div>
      
      {/* Profile section - now responsive */}
      <div className="flex flex-col items-center mb-[32px] mt-8 mx-5">
          <div className="w-[160px] h-[160px] relative flex items-center justify-center">
            {/* Circular background layers */}
            <div className="absolute w-full h-full rounded-full bg-[#2567FF57] opacity-20"></div>
            <div className="absolute w-[80%] h-[80%] rounded-full bg-[#2567FF24] opacity-30"></div>
            <div className="absolute w-[60%] h-[60%] rounded-full bg-[#2567FF14] opacity-40"></div>
            
            {/* Avatar image */}
            <div className="relative z-10 w-[100px] h-[100px] overflow-hidden">
              <Image 
                src="/famele.png" 
                alt={user.name || "User"} 
                width={100} 
                height={100} 
                className="rounded-full"
              />
            </div>
          </div>
          <h2 className="text-2xl font-bold mb-3 mt-4">{user.email || "Jeffrey "}</h2>
      </div>

      {/* White card - now responsive and not absolute */}
      <div className="mx-[6px] mb-[6px] bg-white rounded-3xl p-6 box-border h-[596px] max-h-[596px] min-h-[596px] overflow-auto">
        
        <div className="flex flex-col items-center mt-4">
          <div className="flex items-center mb-6">
            <span className="text-gray-500">
              User id
            </span>
            <span className="text-black ml-2 font-medium">
              {user.userId || "1234jinix"}
            </span>
          </div>
          
          {/* Add Contact Button */}
          {!user.friendRequestStatus && !requestSent && (
            <Button 
              className="bg-blue-500 hover:bg-blue-600 text-white rounded-2xl px-8 py-6 w-full"
              onClick={() => handleSendRequest(user.userId)}
            >
              Add Contact
            </Button>
          )}
          
          {/* Show different button states based on request status */}
          {(user.friendRequestStatus?.type === "sent" && user.friendRequestStatus.status === "PENDING") || requestSent ? (
            <Button 
              className="bg-gray-300 text-gray-700 rounded-2xl cursor-default w-full"
              disabled
            >
              Request Sent
            </Button>
          ) : null}
          
          {((user.friendRequestStatus?.type === "sent" && user.friendRequestStatus.status === "ACCEPTED") || 
            (user.friendRequestStatus?.type === "received" && user.friendRequestStatus.status === "ACCEPTED")) && (
            <Button 
              className="bg-green-500 text-white rounded-2xl cursor-default w-full"
              disabled
            >
              Contact Added
            </Button>
          )}
        </div>
      </div>
      
      {/* Confirmation Modal */}
      {showConfirmation && (
        <ContactRequestConfirmation onClose={() => setShowConfirmation(false)} />
      )}
    </div>
  );
}
